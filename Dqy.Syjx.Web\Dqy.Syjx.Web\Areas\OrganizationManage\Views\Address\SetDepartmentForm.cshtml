﻿@{
    Layout = "~/Views/Shared/_FormWhite.cshtml";
}

<div class="wrapper animated fadeInRight">
    <form id="form" class="form-horizontal m">
        <div class="form-group row">
            <label class="col-sm-3 control-label ">所属部门<font class="red"> </font></label>
            <div class="col-sm-8">
                <div col="DepartmentId" id="DepartmentId"></div>
            </div>
        </div>
    </form>
</div>

<script type="text/javascript">
    var ids = ys.request("ids");
    $(function () {
        $('#DepartmentId').ysComboBoxTree({
            url: '@Url.Content("~/OrganizationManage/Department/GetUnitTreeListJson")',
            class: "form-control",
            key: 'id',
            value: 'name'
        });
        $('#form').validate({
            rules: {
                DepartmentId: { required: true }
            }
        });
    });
    function saveForm(index) {
        if ($('#form').validate().form()) {
            //var ids = '';
            //var selectedRow = $('#gridTable').bootstrapTable('getSelections');
            //if (ys.checkRowDelete(selectedRow)) {
            //    ids = ys.getIds(selectedRow);
            //} else {
            //    ys.msgError("请选择需要设置单位的数据。");
            //}
            var postData = $('#form').getWebControls();
            postData.DepartmentId = ys.getLastValue(postData.DepartmentId);
            ys.ajax({
                url: '@Url.Content("~/OrganizationManage/Address/SaveDepartmentFormJson")',
                type: 'post',
                data: { ids: ids, DepartmentId: postData.DepartmentId },
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        parent.layer.close(index);
                        parent.searchTreeGrid();
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        }
    }
</script>

