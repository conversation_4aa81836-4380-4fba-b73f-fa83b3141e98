﻿@{
    Layout = "~/Views/Shared/_FormWhite.cshtml";
    int UnitType = (int)ViewBag.UnitType;
}
<style type="text/css">
    .select2-search { width: 100%;}
    .select2-search .select2-search__field { width: 100% !important;}
</style>
@inject Microsoft.AspNetCore.Hosting.IWebHostEnvironment HostingEnvironment
<script src='@Url.Content("~/junfei/js/CommonPaper.js")' type="text/javascript"></script>
<div class="wrapper animated fadeInRight">
    <form id="form" class="form-horizontal m">
        <div class="form-group row">
            <label class="col-sm-3 control-label ">适用对象<font class="red"> *</font></label>
            <div class="col-sm-7">
                <div id="UseObj" col="UseObj"></div>
            </div>
            <div class="col-sm-2">
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">适用学科<font class="red"> *</font></label>
            <div class="col-sm-7">
                <div id="CourseId" col="CourseId"></div>
            </div>
            <div class="col-sm-2">
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">适用学段<font class="red"> *</font></label>
            <div class="col-sm-7">
                <div id="SchoolStagez" col="SchoolStagez"></div>
            </div>
            <div class="col-sm-2">
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">适用年级<font class="red"> *</font></label>
            <div class="col-sm-7">
                <div id="GradeId" col="GradeId"></div>
            </div>
            <div class="col-sm-2">
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">题目分类<font class="red"> *</font></label>
            <div class="col-sm-7">
                <div id="Classifyz" col="Classifyz"></div>
            </div>
            <div class="col-sm-2">
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">单选题数量<font class="red"> *</font></label>
            <div class="col-sm-7">
                <input id="RadioQuestionNum" col="RadioQuestionNum" type="text" class="form-control" />
            </div>
            <div class="col-sm-2">
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">多选题数量<font class="red"> *</font></label>
            <div class="col-sm-7">
                <input id="ChedkboxQuestionNum" col="ChedkboxQuestionNum" type="text" class="form-control" />
            </div>
            <div class="col-sm-2">
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">判断题数量<font class="red"> *</font></label>
            <div class="col-sm-7">
                <input id="IfQuestionNum" col="IfQuestionNum" type="text" class="form-control" />
            </div>
            <div class="col-sm-2">
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label "></label>
            <div class="col-sm-7" style="color:red;">请创建完成后尽快完成练习，长时间不完成练习，系统会删除你的练习！
            </div>
            <div class="col-sm-2">
            </div>
        </div>
        <input type="hidden" id="hidClassifyz"/>
    </form>
</div>

<script type="text/javascript">
    var id = ys.request("id");
    var UseObjUrl = '@Url.Content("~/SystemManage/StaticDictionary/GetListJson")' + '?TypeCode=2003&Ids=2003001,2003002';
    var CourseUrl = '@Url.Content("~/SystemManage/StaticDictionary/GetListJson")' + '?TypeCode=1005&OptType=8';
    var SchoolStageUrl = '@Url.Content("~/SystemManage/StaticDictionary/GetListJson")' + '?TypeCode=1002&OptType=8';
    var GradeUrl = '@Url.Content("~/SystemManage/StaticDictionary/GetListJson")' + '?TypeCode=1003&OptType=8';
    $(function () {
        bindClassifyz();
        getForm();
    });

    function loadBind(jsonData) {
        if (jsonData != undefined) {
            Paper.BindComboBox($("#UseObj"), UseObjUrl, jsonData.UseObj, 1, '',0);
            Paper.BindChangeComboBox($("#CourseId"), CourseUrl, jsonData.CourseId, 0, '', 1, loadSchoolStage);
            Paper.BindChangeComboBox($("#SchoolStagez"), (SchoolStageUrl + "&Pid=" + jsonData.CourseId), jsonData.SchoolStagez, 0, '', 1, loadGrade);

            if (jsonData.SchoolStagez > 0) {
                Paper.BindComboBox($("#GradeId"), (GradeUrl + "&Pid=" + jsonData.SchoolStagez), jsonData.GradeId, 0, '', 1);
            } else {
                Paper.BindComboBox($("#GradeId"), (GradeUrl + "&Pid=" + jsonData.CourseId), jsonData.GradeId, 0, '', 1);
            }
        } else {
            Paper.BindComboBox($("#UseObj"), UseObjUrl, -1, 0, '',0);
            Paper.BindChangeComboBox($("#CourseId"), CourseUrl, -1, 0, '', 1, loadSchoolStage);
            loadSchoolStage(-1);
            loadGrade(-1);
        }
    }

    function loadSchoolStage(selectid) {
        if (selectid > 0) {
            Paper.BindChangeComboBox($("#SchoolStagez"), (SchoolStageUrl + "&Pid=" + selectid), -1, 0, '', 1, loadGrade);
        } else {
            Paper.BindAllEmptyComboBox($("#SchoolStagez"), -1, 0, '', 1, loadGrade);
        }
    }

    /**
     *  选择学段，根据学段加载班级
     *  没有选择学段、选择了学科，根据学科加载。
     *  否则加载全部（默认）。
     */
    function loadGrade(selectid) {
        var selectPid = $("#SchoolStagez").ysComboBox('getValue');
        if (!parseInt(selectPid) > 0) {
            var courseid = $("#CourseId").ysComboBox('getValue');
            if (parseInt(courseid) > 0) {
                selectPid = courseid;
            }
        }
        if (parseInt(selectid) > 0) {
            Paper.BindComboBox($("#GradeId"), (GradeUrl + "&Pid=" + selectPid), 0, 0, '', 1);
        } else {
            Paper.BindAllEmptyComboBox($("#GradeId"), -1, 0, '', 1, undefined);
        }
    }

    //题目类型
    function bindClassifyz() {
        $('#Classifyz').ysComboBox({
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetList2Json")' + '?TypeCode=2001',
            key: 'DictionaryId',
            value: 'DicName',
            class: 'form-control',
            multiple: true,
            placeholder: '下拉框的内容支持多选'
        });
        //var classidz = $("#hidClassifyz").val();
        //if (data != undefined && data.length && classidz != undefined && classidz.length > 0) {
        //    $('#Classifyz').ysComboBox("setValue", classidz);
        //}
        //$('#Classifyz .select2-search__field').attr("placeholder", "下拉框的内容支持多选");
        //$("#Classifyz").off('select2:open');
        //$("#Classifyz").on('select2:open', (element) => {
        //    $('#Classifyz .select2-search__field').attr('placeholder', '下拉框的内容支持多选');
        //});
    }

    function getForm() {
        if (id > 0) {
            $("#divCode").show();
            $("#divModifyTime").show();
            ys.ajax({
                url: '@Url.Content("~/PaperExamineManage/Paper/GetFormJson")' + '?id=' + id,
                type: 'get',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        $('#Code').val(obj.Data.Code);
                        $('#BaseModifyTime').val(obj.Data.BaseModifyTime);
                        $('#Name').val(obj.Data.Name);
                        loadBind(obj.Data);
                    }
                }
            });
        }
        else {
            $("#divCode").hide();
            $("#divModifyTime").hide();
            var defaultData = {};
            $('#form').setWebControls(defaultData);
            loadBind(undefined);
        }
    }

    function saveForm(index) {

        var postData = $('#form').getWebControls({ Id: id });
        var msg = '';

        if (!(postData.UseObj != undefined && parseInt(postData.UseObj) > -1)) {
            msg += '请选择适用对象！<br/>';
        }
        if (!(postData.CourseId != undefined && parseInt(postData.CourseId) > -1)) {
            msg += '请选择适用学科！<br/>';
        }
        if (!(postData.SchoolStagez != undefined && parseInt(postData.SchoolStagez) > -1)) {
            msg += '请选择适用学段！<br/>';
        }
        if (!(postData.GradeId != undefined && parseInt(postData.GradeId) > -1)) {
            msg += '请选择适用年级！<br/>';
        }
        if (!(postData.Classifyz != undefined && postData.Classifyz.length > 0)) {
            msg += '请选择题目分类！<br/>';
        }

        if (!(postData.RadioQuestionNum != undefined && postData.RadioQuestionNum.length > 0)) {
            msg += '请输入单选题数量！<br/>';
        } else if (isNaN(postData.RadioQuestionNum)) {
            msg += '单选题数量请输入数字！<br/>';
        }
        if (!(postData.ChedkboxQuestionNum != undefined && postData.ChedkboxQuestionNum.length > 0)) {
            msg += '请输入多选题数量！<br/>';
        } else if (isNaN(postData.ChedkboxQuestionNum)) {
            msg += '多选题数量请输入数字！<br/>';
        }
        if (!(postData.IfQuestionNum != undefined && postData.IfQuestionNum.length > 0)) {
            msg += '请输入判断题数量！<br/>';
        } else if (isNaN(postData.IfQuestionNum)) {
            msg += '判断题数量请输入数字！<br/>';
        }
        var totalQuestionNum = parseInt(postData.RadioQuestionNum) + parseInt(postData.ChedkboxQuestionNum) + parseInt(postData.IfQuestionNum);
        if (!isNaN(totalQuestionNum) && totalQuestionNum < 20) {
            msg += '练习抽题总题数不得少于20题！<br/>';
        }

        if (msg != '') {
            ys.msgError("验证失败<br/>" + msg);
            return;
        }
        postData.PaperType = 2; //自我评测试卷
        ys.ajax({
            url: '@Url.Content("~/PaperExamineManage/Paper/SaveUserExercise")',
            type: 'post',
            data: postData,
            success: function (obj) {
                if (obj.Tag == 1) {
                    ys.msgSuccess(obj.Message);
                    parent.$('#gridTable').ysTable('refresh');parent.resetToolbarStatus();
                    parent.layer.close(index);
                }
                else {
                    ys.msgError(obj.Message);
                }
            }
        });
    }
</script>

