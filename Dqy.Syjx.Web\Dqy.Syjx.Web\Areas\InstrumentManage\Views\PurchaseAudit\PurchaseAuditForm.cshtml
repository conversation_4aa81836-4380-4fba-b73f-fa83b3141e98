﻿@{
    Layout = "~/Views/Shared/_Index.cshtml";
}
<style type="text/css">
    .check-box {
        width: 100px;
    }

    .iradio-box {
        width: 100px;
    }

    .iradio-box {
        position: initial;
        display: inline-block;
    }

    .iradio-blue {
        position: inherit;
        display: inline-block;
        /*  top: 6px;*/
    }

    .col-sm-8 {
        padding-top: 7px;
    }
</style>
@*<div class="container-div">

    <div class="row" style="height:auto;">
        <div class="col-sm-12">
            <div class="ibox float-e-margins">
                <div class="ibox-title">
                    <h5>填报信息</h5>
                    <div class="ibox-tools">
                        <a class="collapse-link">
                            <i class="fa fa-chevron-up"></i>
                        </a>
                    </div>
                </div>
                <div class="ibox-content">
                    <div class="row">
                        <div class="col-sm-12" style="font-size:14px">
                            <div class="card-body">
                                <form id="baseForm" class="form-horizontal m">
                                    <input type="hidden" id="statuz" col="Statuz" />
                                    <div class="form-group row">
                                        <label class="col-sm-2 control-label ">采购年度</label>
                                        <div class="col-sm-4">
                                            <input id="purchaseYear" col="PurchaseYear" type="text" readonly="readonly" class="form-control" />
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-sm-2 control-label ">仪器名称</label>
                                        <div class="col-sm-4">
                                            <input id="name" col="Name" type="text" readonly="readonly" class="form-control" />
                                        </div>
                                        <label class="col-sm-2 control-label ">规格属性</label>
                                        <div class="col-sm-4">
                                            <input id="model" col="Model" type="text" readonly="readonly" class="form-control" />
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-sm-2 control-label ">数量</label>
                                        <div class="col-sm-4">
                                            <input id="num" col="Num" type="text" readonly="readonly" class="form-control" />
                                        </div>
                                        <label class="col-sm-2 control-label ">单位</label>
                                        <div class="col-sm-4">
                                            <input id="unitName" col="UnitName" type="text" readonly="readonly" class="form-control" />
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-sm-2 control-label ">单价</label>
                                        <div class="col-sm-4">
                                            <input id="price" col="Price" type="text" readonly="readonly" class="form-control" />
                                        </div>
                                        <label class="col-sm-2 control-label ">总价</label>
                                        <div class="col-sm-4">
                                            <input id="amountSum" col="AmountSum" type="text" readonly="readonly" class="form-control" />
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row" style="height:auto;">
        <div class="col-sm-12">
            <div class="ibox float-e-margins">
                <div class="ibox-title">
                    <h5>校长审批</h5>
                </div>
                <div class="ibox-content">
                    <div class="row">
                        <div class="col-sm-12" style="font-size:14px">
                            <div class="card-body">
                                <form id="fileConfigForm" class="form-horizontal m">
                                    <div class="form-group row">
                                        <label class="col-sm-2 control-label ">审批结果<font class="red"> *</font></label>
                                        <div class="col-sm-10">
                                            <div id="approvalStatuz" col="ApprovalStatuz"></div>
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-sm-2 control-label ">审批意见<font class="red"> *</font></label>
                                        <div class="col-sm-10">
                                            <textarea id="approvalRemark" col="ApprovalRemark" class="form-control"></textarea>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row" style="text-align:center;" id="divButton">
        <a id="btnAdd" class="btn btn-info" onclick="saveData()"><i class="fa fa-save"></i> 提交</a>
    </div>
</div>*@

<div class="wrapper animated fadeInRight">
    <div class="row">
        <form id="baseForm" class="form-horizontal m">
            <input type="hidden" id="statuz" col="Statuz" />
        </form>
        <form id="fileConfigForm" class="form-horizontal m">
            <div class="form-group row">
                <label class="col-sm-2 control-label ">审批结果<font class="red"> *</font></label>
                <div class="col-sm-8">
                    <div id="approvalStatuz" col="ApprovalStatuz"></div>
                </div>
                <div class="col-sm-2"> </div>
            </div>
            <div class="form-group row">
                <label class="col-sm-2 control-label ">审批意见</label>
                <div class="col-sm-8">
                    <textarea id="approvalRemark" style="max-width:100%" col="ApprovalRemark" class="form-control"></textarea>
                </div>
                <div class="col-sm-2"> </div>
            </div>
        </form>
    </div>
</div>

<script type="text/javascript">
    var id = ys.request("id");
    $(function () {
        getForm();
    });

    function getForm() {
        if (id > 0) {
            ys.ajax({
                url: '@Url.Content("~/InstrumentManage/PurchaseDeclaration/GetFormJson")' + '?id=' + id,
                type: 'get',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        //console.log("obj.Data:" + JSON.stringify(obj.Data));
                        obj.Data.Price = ComBox.ToLocaleString(obj.Data.Price);
                        obj.Data.AmountSum = ComBox.ToLocaleString(obj.Data.AmountSum);
                        $('#baseForm').setWebControls(obj.Data);
                        loadApprovalStatuz(obj.Data.Statuz);
                    }
                }
            });
        }
        else {
            var defaultData = {};
            $('#baseForm').setWebControls(defaultData);
        }
    }

    function loadApprovalStatuz(statuz) {
        var html = '';
        html += '<label class="iradio-box"><div class="iradio-blue pass" style="vertical-align:bottom;"><input name="inputType_checkbox" type="radio" value="@InstrumentAuditStatuzEnum.End.ParseToInt()" style="position: absolute; opacity: 0;"></div> 通过</label>';
        html += '<label class="iradio-box"><div class="iradio-blue nopass" style="vertical-align:bottom;"><input name="inputType_checkbox" type="radio" value="@InstrumentAuditStatuzEnum.SchoolAuditPass.ParseToInt()" style="position: absolute; opacity: 0;"></div> 不通过</label>';
        $("#approvalStatuz").html(html);

        $('input[name="inputType_checkbox"]').change(function () {
            if ($(this).prop("checked")) {
                var val = $(this).val();
                if (val % 2 == 0) {
                    $(".pass").addClass("checked");
                    $(".nopass").removeClass("checked");
                    if ($('#approvalRemark').val() == '')
                        $('#approvalRemark').val('同意');

                } else {
                    $(".nopass").addClass("checked");
                    $(".pass").removeClass("checked");
                    if ($('#approvalRemark').val() == '同意')
                        $('#approvalRemark').val('');
                }
            }
        });
    }

    function saveForm(index) {
        var statuz = $('input[name="inputType_checkbox"]:checked').val();
        if (statuz > 0) {
            var remark = $('#approvalRemark').val();
            if (statuz % 2 == 1 && remark == '') {
                ys.msgError('不通过必须填写审批意见！');
                return false;
            }
            //ys.confirm('您确认审批' + (statuz % 2 == 1 ? '不通过' : '通过') +'吗？', function () {

            //});
            var postData = {
                PurchaseDeclarationId: id,
                ApprovalStatuz: statuz,
                ApprovalRemark: $('#approvalRemark').val(),
                ProcessNumber: $('#statuz').val()
            };
            ys.ajax({
                url: '@Url.Content("~/InstrumentManage/PurchaseAudit/SaveFormJson")',
                type: 'post',
                data: postData,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        //var url = '@Url.Content("~/InstrumentManage/PurchaseAudit/PurchaseAuditIndex")';
                        //createMenuAndCloseCurrent(url, "待审批计划");
                        parent.$('#gridTable').ysTable('refresh'); parent.resetToolbarStatus();
                        parent.layer.close(index);
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        }
        else {
            ys.msgError('请选择审批结果！');
        }
    }

</script>

