﻿@{
    Layout = "~/Views/Shared/_FormWhite.cshtml";
}

<div class="wrapper animated fadeInRight">
    <form id="form" class="form-horizontal m">
        <div class="form-group row">
            <label class="col-sm-3 control-label ">上级组织</label>
            <div class="col-sm-8">
                <div id="parentId" col="ParentId"></div>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">组织名称<font class="red"> *</font></label>
            <div class="col-sm-8">
                <input id="departmentName" col="DepartmentName" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row" style="display:none;">
            <label class="col-sm-3 control-label ">显示顺序</label>
            <div class="col-sm-8">
                <input id="departmentSort" col="DepartmentSort" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label">状态</label>
            <div class="col-sm-8" id="statuz" col="Statuz"></div>
        </div>
    </form>
</div>

<script type="text/javascript">
    var id = ys.request("id");
    $(function () {
        $("#statuz").ysRadioBox({ data: ys.getJson(@Html.Raw(typeof(StatusEnum).EnumToDictionaryString())) });

        $('#parentId').ysComboBoxTree({
            url: '@Url.Content("~/OrganizationManage/Department/GetDepartmentTreeListJson")', async: false,
            callback: {
                customOnSuccess: function (param, treeId, data) {
                    if (data.Data.length == 1) {
                        $('#parentId').ysComboBoxTree('setValue', data.Data[0].id);
                    }
                }
            }
        });

        getForm();

        $("#form").validate({
            rules: {
                departmentName: { required: true },
                //parentIdInput: { required: true }
            }
        });
    });

    function getForm() {
        if (id > 0) {
            ys.ajax({
                url: '@Url.Content("~/OrganizationManage/Department/GetFormJson")' + '?id=' + id,
                type: "get",
                success: function (obj) {
                    if (obj.Tag == 1) {
                        var result = obj.Data;
                        $("#form").setWebControls(result);
                    }
                }
            });
        }
        else {
             ys.ajax({
                 url: '@Url.Content("~/OrganizationManage/Department/GetMaxSortJson")',
                type: "get",
                success: function (obj) {
                    if (obj.Tag == 1) {
                        $("#departmentSort").val(obj.Data);
                    }
                }
             });
            var defaultData = {};
            defaultData.Statuz = "@StatusEnum.Yes.ParseToInt()";
            $("#form").setWebControls(defaultData);
        }
    }

    function saveForm(index) {
        if ($("#form").validate().form()) {
            var postData = $("#form").getWebControls({ Id: id });
            postData.ParentId = ys.getLastValue(postData.ParentId);
            //if (!ys.getLastValue(postData.ParentId)) {
            //    ys.msgError('请选择上级组织！');
            //    return false;
            //}
            ys.ajax({
                url: '@Url.Content("~/OrganizationManage/Department/SaveFormJson")',
                type: "post",
                data: postData,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        parent.searchTreeGrid(obj.Data);
                        parent.layer.close(index);
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        }
    }
</script>
