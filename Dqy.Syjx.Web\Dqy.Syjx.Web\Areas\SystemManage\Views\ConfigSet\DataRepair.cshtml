﻿@using Dqy.Syjx.Enum.BusinessManage
@using Dqy.Syjx.Enum.OrganizationManage
@{
    Layout = "~/Views/Shared/_FormWhite.cshtml";
}
<style type="text/css">
    .select2-container {
        display: block;
        width: auto !important;
    }
</style>
@inject Microsoft.AspNetCore.Hosting.IWebHostEnvironment HostingEnvironment
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/uploadifive/File.min.css"))
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/uploadifive/File.min.js"))
<div class="container-div">
    <div class="btn-group-sm hidden-xs" id="toolbar">
    </div>
    <div class="col-sm-12 select-table table-striped">
        <div class="ibox-title">
            <h5>更新在线巡课数据</h5>
        </div>

        <div class="card-body">
            <form id="form" class="form-horizontal m">
                <input id="Id" col="Id" type="hidden" />
     
                <div class="form-group row">
                    <label class="col-sm-2 control-label " id="labBirthday1">修复日期范围：</label>
                    <div class="col-sm-8">
                        <input id="BeginDate" col="BeginDate" type="text" class="form-control" />
                        &nbsp;
                        ~
                        &nbsp;
                        <input id="EndDate" col="EndDate" type="text" class="form-control" />
                    </div>
                </div>

            </form>
        </div>
        <div class="btn-group-sm hidden-xs" id="toolbar" style="text-align: center;">
            <a id="btnAdd" class="btn btn-info" onclick="saveForm();"><i class="fa fa-edit"></i> 确定</a>
        </div>
    </div>
</div>
<script type="text/javascript">
   
    $(function () {

        laydate.render({ elem: '#BeginDate', format: 'yyyy-MM-dd', trigger: 'click', position: 'fixed' });
        laydate.render({ elem: '#EndDate', format: 'yyyy-MM-dd', trigger: 'click', position: 'fixed' });
  
    });


    function saveForm() {
        if ($('#form').validate().form()) {
            var postData = $('#form').getWebControls();
            ys.ajax({
                url: '@Url.Content("~/SystemManage/ConfigSet/SaveDataRepairJson?strTime=")' + postData.BeginDate + '&endTime=' + postData.EndDate,
                type: 'get',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        }
    }
</script>
