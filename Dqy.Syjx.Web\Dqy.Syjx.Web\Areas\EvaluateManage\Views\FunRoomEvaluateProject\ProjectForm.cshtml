﻿@{
    Layout = "~/Views/Shared/_FormWhite.cshtml";
}

<div class="wrapper animated fadeInRight">
    <form id="form" class="form-horizontal m">
        <input type="hidden" id="Id" col="Id" value="0" />
        <div class="form-group row">
            <label class="col-sm-3 control-label "><span id="spanProjectName">评估项目名称</span><font class="red"> *</font></label>
            <div class="col-sm-7">
                <input id="EvaluateName" col="EvaluateName" type="text" class="form-control" />
            </div>
            <div class="col-sm-2">
            </div>
        </div>
    </form>
</div>

<script type="text/javascript">
    var id = ys.request("id");
    $(function () {
        $("#Id").val(id);
        if (id > 0) {
            $("#spanProjectName").text("评估项目新名称");
        }
        $('#form').validate({
            rules: {
                EvaluateName: { required: true }
            }
        });
    });
    function saveForm(index) {
        if ($('#form').validate().form()) {
            var postData = $('#form').getWebControls({ Id: id });
            ys.ajax({
                url: '@Url.Content("~/EvaluateManage/FunRoomEvaluateProject/SaveProjectFormJson")',
                type: 'post',
                data: postData,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        parent.reloadVersionProject();
                        parent.$('#gridTable').ysTable('refresh');parent.resetToolbarStatus();
                        parent.layer.close(index);
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        }
    }
</script>

