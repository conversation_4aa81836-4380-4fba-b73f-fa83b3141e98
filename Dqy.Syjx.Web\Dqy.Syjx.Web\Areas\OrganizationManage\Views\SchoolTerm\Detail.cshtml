﻿@{
    Layout = "~/Views/Shared/_FormWhite.cshtml";
}
<style type="text/css">
    .tbllist {
        border-collapse: collapse;
        margin: 10px;
        display: inline-block;
    }

        .tbllist tr td {
            border: 1px solid #cccccc;
            text-align: center;
            padding: 5px;
            width: 60px;
        }

        .tbllist tr {
            height: 25px;
            line-height: 25px;
        }
</style>
<div class="wrapper animated fadeInRight">
    <form id="form" class="form-horizontal m">
        <div class="form-group row">
            <div class="col-sm-12" style="text-align:center;">
                <table class="tbllist">
                    <tr>
                        <td style="width:100px;height:100px;">
                            <div style="position: relative; height: 100%; width: 100%;">
                                <div style="transform: rotate(60deg); transform-origin: 50% 210% 0px; border-bottom: 1px solid #cccccc;">星期</div>
                                <div style="transform: rotate(40deg); transform-origin: 30% 70% 0px;">日期</div>
                                <div style="transform: rotate(30deg); transform-origin: 10% -10% 0px;border-top: 1px solid #cccccc;text-align:left;padding-left:15px;">周次</div>
                            </div>
                        </td>
                        <td>一</td>
                        <td>二</td>
                        <td>三</td>
                        <td>四</td>
                        <td>五</td>
                        <td>六</td>
                        <td>七</td>
                        <td style="width:120px;">备注</td>
                    </tr>
                    <tbody id="tbodyData"></tbody>
                </table>
            </div>
        </div>
    </form>
</div>

<script type="text/javascript">
    var id = ys.request("id");
    $(function () {
        parent.removeConfirm();
        var td1 = $("#td1");
        var td2 = $("#td2");
        getForm();
    });

    function getForm() {
        if (id > 0) {
            ys.ajax({
                url: '@Url.Content("~/OrganizationManage/SchoolTerm/GetDetailFormJson")' + '?id=' + id,
                type: 'get',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        if (obj.Data.Detail != undefined && obj.Data.Detail.length > 0) {
                            var html = '';
                            /**1:第一行，判断是星期几，如果是0则是星期天,加载前面的空格
                             * 2:如果遇到星期天，则添加结束行</tr>,
                             * 3:遇到周一，则开始行。
                             *
                             */
                            var monthn = 1;
                            for (var i = 0; i < obj.Data.Detail.length; i++) {
                                var data = obj.Data.Detail[i];
                                if (data.Weekn == 0) {
                                    data.Weekn = 7;
                                }
                                if (i == 0 || data.Weekn == 1) {
                                    html = '<tr>';
                                    html += $.Format('<td>{0}</td>', numberToChinese(data.WeekNum));
                                }
                                if (i == 0) {
                                    if (data.Weekn > 1) {
                                        for (var j = 0; j < (data.Weekn-1); j++) {
                                            html += '<td>&nbsp;</td>';
                                        }
                                    }
                                }
                                var day = new Date(data.Daten).getDate();
                                if (monthn != data.Monthn || i == 0) {
                                    monthn = data.Monthn;
                                    html += $.Format('<td>{0}.{1}</td>', monthn, day);
                                } else {
                                    html += $.Format('<td>{0}</td>', day);
                                }

                                if (data.Weekn == 7) {
                                    if (data.WeekNum == 1) {
                                        var remark = '';
                                        if (obj.Data.Remark != undefined && obj.Data.Remark.length > 0) {
                                            remark = obj.Data.Remark;
                                        }
                                        html += $.Format('<td rowspan="{0}">{1}</td>', obj.Data.Detail[obj.Data.Detail.length - 1].WeekNum, remark);//备注那一列
                                    }
                                    html += '</tr>';
                                    $("#tbodyData").append(html);
                                    html = '';
                                }
                            }
                        }
                    }
                }
            });
        }
        else {
            var defaultData = {};
            $('#form').setWebControls(defaultData);
        }
    }
    function numberToChinese(num) {
        var AA = new Array("零", "一", "二", "三", "四", "五", "六", "七", "八", "九", "十");
        var BB = new Array("", "十", "百", "");
        var a = ("" + num).replace(/(^0*)/g, ""),
            k = 0,
            re = "";
        for (var i = a.length - 1; i >= 0; i--) {
            if (k % 4 == 2 && a.charAt(i + 2) != 0 && a.charAt(i + 1) == 0)
                re = AA[0] + re;
            if (a.charAt(i) != 0)
                re = AA[a.charAt(i)] + BB[k % 4] + re;
            k++;
        }
        if (re == '一十')
            re = "十";
        if (re.match(/^一/) && re.length == 3)
            re = re.replace("一", "");
        return re;
    }

</script>

