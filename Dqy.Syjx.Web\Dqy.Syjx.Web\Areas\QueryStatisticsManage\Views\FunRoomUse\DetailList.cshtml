﻿@{
    Layout = "~/Views/Shared/_Index.cshtml";
}
<style type="text/css">
    .select2-container {  width: 100% !important; }
</style>
<div class="container-div">
    <div class="row">
        <div id="searchDiv" class="col-sm-12 search-collapse">
            <div class="select-list">
                <ul>
                    <li>
                        <a class="btn btn-secondary btn-sm" onclick="addTabRecordForm()"><i class="fa fa-street-view"></i>&nbsp;按使用记录统计</a>
                    </li>
                    <li>
                        <div id="SchoolYearStart" col="SchoolYearStart" style="display: inline-block;width:100px;"></div>
                    </li>
                    <li>
                        <div id="SchoolTerm" col="SchoolTerm" style="display: inline-block;width:100px;"></div>
                    </li>
                    <li>
                        <div id="DictionaryId1006A" col="DictionaryId1006A" style="display: inline-block;width:100px;"></div>
                    </li>
                    <li>
                        <div id="DictionaryId1006B" col="DictionaryId1006B" style="display: inline-block;width:100px;"></div>
                    </li>
                    <li>
                        <div id="RoomAttribute" col="RoomAttribute" style="display: inline-block;width:100px;"></div>
                    </li>
                    <li>
                        <div id="DictionaryId1005" col="DictionaryId1005" style="display: inline-block;width:100px;"></div>
                    </li>
                    <li>
                        <div id="UseClass" col="UseClass" style="display: inline-block;width:100px;"></div>
                    </li>
                    <li>
                        <div id="RegisterUserId" col="RegisterUserId" style="display: inline-block;width:100px;"></div>
                    </li>
                    <li>
                        <input id="Name" col="Name" type="text" placeholder="名称、登记人" style="display: inline-block;width:160px;" />
                    </li>
                    <li>
                        <a id="btnSearch" class="btn btn-primary btn-sm" onclick="searchGrid()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                        <a id="btnSearch" class="btn btn-secondary btn-sm" onclick="resetGrid()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                    </li>
                    <!--帮助文档需要内容,id值必须为“helpBtn”-->
                    <li>
                        <i class="fa fa-question-circle" id="helpBtn"></i>
                    </li>
                </ul>
            </div>
        </div>
        <div class="btn-group-sm hidden-xs" id="toolbar">
            <a class="btn btn-warning btn-sm" onclick="exportForm()"><i class="fa fa-download"></i> 导出</a>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="gridTable" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>
<!--帮助文档需要内容,id值必须为“tipMsg”-->
<div id="tipMsg"></div>
<script type="text/javascript">
    var Com_SchoolTermYear = new Date().getFullYear();
    var Com_SchoolTerm = 1;
    var BasePageCode = 101031;//实验室使用记录(101031)
    $(function () {
          $("#DictionaryId1006A").ysComboBox({
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetList2Json")' + '?TypeCode=1006&OptType=5',
            key: 'DictionaryId',
              value: 'DicName',
            defaultName:'一级分类',
            onChange: function () {
                var dicA = $("#DictionaryId1006A").ysComboBox("getValue");
                loadDictionaryId1006B(dicA);
            }
        });
        loadDictionaryId1006B(0);
        loadNature();
        loadSubject();
        ComBox.SchoolTermYear($("#SchoolYearStart"), undefined, '学年');
        $("#SchoolTerm").ysComboBox({
            data: ys.getJson(@Html.Raw(typeof(SchoolTermEnum).EnumToDictionaryString())),
            defaultName: '学期'
        });
        loadGradeClass();
        loadRegisterUser();
        ys.ajax({
            url: '@Url.Content("~/ExperimentTeachManage/ExperimentBooking/GetSchoolTermInfo")',
            type: 'get',
            async: false,
            success: function (obj) {
                if (obj.Tag == 1 && obj.Data != undefined) {
                    if (obj.Data.SchoolTermStartYear != undefined && parseInt(obj.Data.SchoolTermStartYear) > 0) {
                        Com_SchoolTermYear = obj.Data.SchoolTermStartYear;
                        $("#SchoolYearStart").ysComboBox('setValue', Com_SchoolTermYear);
                    }
                    if (obj.Data.SchoolTerm == 1 || obj.Data.SchoolTerm == 2) {
                        Com_SchoolTerm = obj.Data.SchoolTerm;
                        $("#SchoolTerm").ysComboBox('setValue', Com_SchoolTerm);
                    }
                }
            }
        });
        initGrid();
    });
    function loadDictionaryId1006B(dicA) {
        if (parseInt(dicA) > 0) {
            $("#DictionaryId1006B").ysComboBox({
                url: '@Url.Content("~/SystemManage/StaticDictionary/GetList2Json")' + '?TypeCode=1006&OptType=5' + '&Pid=' + dicA,
                key: 'DictionaryId',
                value: 'DicName',
                defaultName: '二级分类',
            });
            $(".select2-container").width("100%");
        } else {
            $("#DictionaryId1006B").ysComboBox({
                data: [],
                key: 'DictionaryId',
                value: 'DicName',
                defaultName: '二级分类',
            });
            $(".select2-container").width("100%");
        }
    }
    function loadNature() {
        $("#RoomAttribute").ysComboBox({
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetList2Json")' + '?TypeCode=1009',
            key: 'DictionaryId',
            value: 'DicName',
            defaultName: '属性',
        });
    }
    function loadSubject() {
        $("#DictionaryId1005").ysComboBox({
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetListJson")' + '?TypeCode=1005&OptType=4',
            key: 'DictionaryId',
            value: 'DicName',
            defaultName: '适用学科',
        });
    }
    function loadGradeClass() {
        $('#UseClass').ysComboBox({
            url: '@Url.Content("~/OrganizationManage/SchoolGradeClass/GetListJson")',
            key: 'Id',
            value: 'ClassName',
            defaultName:'使用班级'
        });
    }
    function loadRegisterUser() {
        $('#RegisterUserId').ysComboBox({
            url: '@Url.Content("~/BusinessManage/FunRoom/GetUserListJson")',
            key: 'Id',
            value: 'RealName',
            defaultName: '登记人'
        });
    }
    function initGrid() {
        var queryUrl = '@Url.Content("~/QueryStatisticsManage/FunRoomUse/GetDetailPageJson")';
        $('#gridTable').ysTable({
            url: queryUrl,
            // showRefresh: false,
            // showToggle: false,
            // showColumns: false,
            showExportSetBtn : true,
            showExportSetCode: BasePageCode,
            columns: [
                {
                    title: '操作', halign: 'center', valign: 'middle', align: 'center', width: 80,
                    formatter: function (value, row, index) {
                        var actions = [];
                        actions.push('&nbsp;<a class="btn btn-info btn-xs" href="#" onclick="showDetailForm(\'' + row.Id + '\')"><i class="fa fa-eye"></i>查看</a>&nbsp;');
                        return actions.join('');
                    }
                },
                { field: 'index', title: '序号', width: 80, halign: 'center', valign: 'middle', align: 'center', formatter: function (value, row, index) { return index + 1;} },
                {
                    field: 'SubjectName', title: '学科', sortable: true, width: 120, halign: 'center', valign: 'middle', align: 'center',
                    formatter: function (value, row, index) {
                        var html = '';
                        if (row.Id) {
                            html = '--';
                            if (value && value.length > 0) {
                                html = value;
                            }
                        }
                        return html;
                    }
                },
                {
                    field: 'Name', title: '实验（专用）室名称', sortable: true, width: 260, halign: 'center', valign: 'middle',
                    formatter: function (value, row, index) {
                        var html = '';
                        if (row.Id) {
                            switch (row.NatureName) {
                                case '@FunNatureTypeEnum.Room.GetDescription()':
                                    html += Syjx.GetCircleRoomHtml();
                                    break;
                                case '@FunNatureTypeEnum.ZhuanRoom.GetDescription()':
                                    html += Syjx.GetCircleZhuanRoomHtml();
                                    break;
                                case '@FunNatureTypeEnum.FuRoom.GetDescription()':
                                    html += Syjx.GetCircleFuRoomHtml();
                                    break;
                            }
                            html += row.Name;

                        }
                        return html;
                    }
                },
                {
                    field: 'UseDate', title: '使用日期', sortable: true, width: 100, halign: 'center', valign: 'middle', align: 'center', formatter: function (value, row, index) {
                        var html = '--';
                        if (isNaN(value) && value.length > 0) {
                            html = ys.formatDate(value, "yyyy-MM-dd");
                        }
                        return html;
                    }
                },
                {
                    field: 'UseCourseSectionIndex', title: '课程节次', sortable: true, width: 160, halign: 'center', valign: 'middle', align: 'center',
                    formatter: function (value, row, index) {
                        var html = '--';
                        if (parseInt(row.UseCourseSectionIndex) > 0) {
                            html = ("第" + row.UseCourseSectionIndex + "节");
                        }
                        return html;
                    }
                },
                {
                    field: 'UseClassName', title: '使用班级', sortable: true, width: 120, halign: 'center', valign: 'middle', align: 'center',
                    formatter: function (value, row, index) {
                        var html = '';
                        if (row.Id) {
                            html = '--';
                            if (value && value.length > 0) {
                                html = value;
                            }
                        }
                        return html;
                    }
                },
                
                { field: 'RealName', title: '登记人', sortable: true, width: 120, halign: 'center', valign: 'middle', align: 'center' },
            ],
            queryParams: function (params) {
                var pagination = $('#gridTable').ysTable('getPagination', params);
                var queryString = $('#searchDiv').getWebControls(pagination);
                return queryString;
            }
        });
    }

    function searchGrid() {
        $('#gridTable').ysTable('search');
    }
    function resetGrid() {
        $('#SchoolYearStart').ysComboBox('setValue', -1);
        $('#SchoolTerm').ysComboBox('setValue', -1);
        $("#DictionaryId1006A").ysComboBox('setValue', -1);
        loadDictionaryId1006B(0);
        $("#RoomAttribute").ysComboBox('setValue', -1);
        $("#DictionaryId1005").ysComboBox('setValue', -1);
        $("#UseClass").ysComboBox('setValue', -1);
        $("#RegisterUserId").ysComboBox('setValue', -1);
        $("#Name").val("");
        $('#gridTable').ysTable('search');
    }
    function addTabRecordForm() {
        var url = '@Url.Content("~/QueryStatisticsManage/FunRoomUse/RecordList")';
        createMenuItem(url, "使用记录统计");
    }
    function showDetailForm(id) {
        ys.openDialog({
            title: '详情',
            content: '@Url.Content("~/QueryStatisticsManage/FunRoomUse/Detail")' + '?id=' + id,
            width: '768px',
            callback: function (index, layero) {
                var iframeWin = window[layero.find('iframe')[0]['name']];
                iframeWin.saveForm(index);
            }
        });
    }
    function removeConfirm() {
        $(".layui-layer-btn0").hide();
    }

    function exportForm() {
        var url = '@Url.Content("~/QueryStatisticsManage/FunRoomUse/ExportDetailList")';//实验室使用记录(101031)
        var pagination = $('#gridTable').ysTable('getPagination', { "sort": "Id", "order": "desc", "offset": 0, "limit": 10 });
        var postData = $('#searchDiv').getWebControls(pagination);
        postData.BasePageCode = BasePageCode;//实验室使用记录(101031)
        ys.exportExcel(url, postData);
    }
</script>
