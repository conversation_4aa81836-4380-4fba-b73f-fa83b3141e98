@{
    Layout = "~/Views/Shared/_Index.cshtml";
 }
<style type="text/css">
    .select2-container { width: 100% !important; }
    .span-left-tag{margin-left:82px;}
</style>
<div class="container-div">
    <div class="row">
        <div id="searchDiv" class="col-sm-12 search-collapse">
            <div class="select-list">
                <ul>
                    <li>
                        <div id="SchoolYearStart" col="SchoolYearStart" style="display: inline-block;width:100px;"></div>
                    </li>
                    <li>
                        <div id="SchoolTerm" col="SchoolTerm" style="display: inline-block;width:100px;"></div>
                    </li>
                    <li>
                        <div id="GradeId" col="GradeId" style="display: inline-block;width:100px;"></div>
                    </li>
                    <li>
                        <div id="CourseId" col="CourseId" style="display: inline-block;width:100px;"></div>
                    </li>                  
                    <li>
                        <input id="PlanName" col="PlanName" placeholder="名称" style="display: inline-block;width:180px;" type="text" />
                    </li>
                    <li>
                        <a id="btnSearch" class="btn btn-primary btn-sm" onclick="searchGrid()"><i class="fa fa-search"></i>搜索</a>
                        <a class="btn btn-secondary btn-sm" onclick="resetGrid()"><i class="fa fa-refresh"></i>重置</a>
                    </li>
                    <!--帮助文档需要内容,id值必须为“helpBtn”-->
                    <li>
                        <i class="fa fa-question-circle" id="helpBtn"></i>
                    </li>
                </ul>
            </div>
        </div>
        <div class="btn-group-sm hidden-xs" id="toolbar">
            <a id="btnAdd" class="btn btn-success" onclick="showSaveForm(true,0)"><i class="fa fa-plus"></i> 新增</a>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="gridTable" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>
<!--帮助文档需要内容,id值必须为“tipMsg”-->
<div id="tipMsg"></div>

<script type="text/javascript">
    var Com_SchoolTermYear = new Date().getFullYear();
    var Com_SchoolTerm = 1;
    $(function () {
        loadGrade();
        loadCourse(0);
        ComBox.SchoolTermYear($('#SchoolYearStart'), undefined, '学年', 10);
        $("#SchoolTerm").ysComboBox({
            data: ys.getJson(@Html.Raw(typeof(SchoolTermEnum).EnumToDictionaryString())),
            defaultName: '学期'
        });
        ys.ajax({
            url: '@Url.Content("~/ExperimentTeachManage/ExperimentBooking/GetSchoolTermInfo")',
            type: 'get',
            async: false,
            success: function (obj) {
                if (obj.Tag == 1 && obj.Data != undefined) {
                    if (obj.Data.SchoolTermStartYear != undefined && parseInt(obj.Data.SchoolTermStartYear) > 0) {
                        Com_SchoolTermYear = obj.Data.SchoolTermStartYear;
                        $("#SchoolYearStart").ysComboBox('setValue', Com_SchoolTermYear);
                    }
                    if (obj.Data.SchoolTerm == 1 || obj.Data.SchoolTerm == 2) {
                        Com_SchoolTerm = obj.Data.SchoolTerm;
                        $("#SchoolTerm").ysComboBox('setValue', Com_SchoolTerm);
                    }
                }
            }
        });
        initGrid();
        
        
    });
    function loadGrade() {
        $('#GradeId').ysComboBox({
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetSchoolGradeListJson")',
            key: 'DictionaryId',
            value: 'DicName',
            defaultName: '年级',
            onChange: function () {
                var selectid = $('#GradeId').ysComboBox('getValue');
                loadCourse(selectid);
            }
        });
    }
    function loadCourse(gradeid) {
        ys.ajax({
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetChildToListJson")' + "?TypeCode=1005&Nature=1&Pid=" + gradeid,
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1) {
                    bindCourseData(obj.Data);
                } else {
                    bindCourseData({});
                }
            }
        });
    }
    function bindCourseData(data) {
        $('#CourseId').ysComboBox({
            data: data,
            key: 'DictionaryId',
            value: 'DicName',
            defaultName: '课程'
        });
    }
    function initGrid() {
        var queryUrl = '@Url.Content("~/ExperimentTeachManage/PlanInfo/GetPageListJson")' + '?OptType=1';
        $('#gridTable').ysTable({
            url: queryUrl,
            showRefresh: false,
            showToggle: false,
            showColumns: false,
            columns: [
                { field: 'index', title: '序号', width: 60, halign: 'center', valign: 'middle', align: 'center', formatter: function (value, row, index) { return index + 1; } },
                // { checkbox: true, visible: true },
                {
                    title: '操作', width: 240, halign: 'center', valign: 'middle', align: 'center',
                    formatter: function (value, row, index) {
                        var actions = [];
                        if (row.IsExpiry == 2) {
                            actions.push('<a class="btn btn-primary btn-xs" href="#" onclick="showSaveForm(false,\'' + row.Id + '\')"><i class="fa fa-edit"></i>修改</a>');
                            actions.push('<a class="btn btn-danger btn-xs" href="#" onclick="deleteForm(\'' + row.Id + '\')"><i class="fa fa-remove"></i>删除</a>');
                        }
                        actions.push('<a class="btn btn-primary btn-xs" href="#" onclick="showCopyForm(\'' + row.Id + '\')"><i class="fa fa-edit"></i>复制</a>');

                        return actions.join('');
                    }
                },
                {
                    field: 'SchoolYearStart', title: '学年', sortable: true, width: 120, halign: 'center', valign: 'middle', align: 'center',
                    formatter: function (value, row, index) {
                        return (row.SchoolYearStart + '').substr(2) + '~' + (row.SchoolYearEnd + '').substr(2);
                    }
                },
                {
                    field: 'SchoolTerm', title: '学期', sortable: true, width: 80, halign: 'center', valign: 'middle', align: 'center',
                    formatter: function (value, row, index) {
                        var html = '@SchoolTermEnum.LastSemester.GetDescription()';
                        if (@SchoolTermEnum.NextSemester.ParseToInt()== value) {
                            html = '@SchoolTermEnum.NextSemester.GetDescription()';
                        }
                        return html;
                    }
                },
                { field: 'GradeName', title: '年级', sortable: true, width: 100, halign: 'center', valign: 'middle', align: 'center' },
                { field: 'CourseName', title: '课程', sortable: true, width: 120, halign: 'center', valign: 'middle', align: 'center'  },             
                { field: 'PlanName', title: '实验计划名称', sortable: true, width: 300, halign: 'center', valign: 'middle' },
                { field: 'Num', title: '已编实验数量', sortable: true, width: 120, halign: 'center', valign: 'middle', align: 'center' },
                {
                    field: 'CompulsoryType', title: '高中班级类型', sortable: true, width: 80, halign: 'center', valign: 'middle', align: 'center',
                    formatter: function (value, row, index) {
                        var html = '--';
                        if (row.GradeId >=  @GradeEnum.GaoYi.ParseToInt()) {
                            if (value == @ClassCompulsoryTypeEnum.Select.ParseToInt()) {
                                html = $.Format('选修{0}班', row.CourseName);
                            } else {
                                html = $.Format('非选修{0}班', row.CourseName);
                            }                          
                        }
                        return html;
                    }
                },
                {
                    field: 'UseNum', title: '计划明细', width: 70, halign: 'center', valign: 'middle', align: 'center',
                    formatter: function (value, row, index) {
                        var actions = [];
                        if (row.IsExpiry == 2) {
                            actions.push('<a class="btn btn-success btn-xs" href="#" onclick="openDetailForm(\'' + row.Id + '\',\'' + row.GradeId + '\',\'' + row.SchoolYearStart + '\',\'' + row.SchoolTerm + '\')"><i class="fa fa-edit"></i>编制</a>');
                        } else {
                            actions.push('<a class="btn btn-info btn-xs" href="#" onclick="openLookDetailForm(\'' + row.Id + '\')"><i class="fa fa-eye"></i>查看</a>');
                        }
                        return actions.join('');
                    }
                },
                {
                    field: 'ClassNamez', title: '班级', width: 200, sortable: true, halign: 'center', valign: 'middle',
                    formatter: function (value, row, index) {
                        var html = '--';
                        if (value != undefined && value.length > 0) {
                            html = value;
                        }
                        return html;
                    }
                },
                { field: 'RealName', title: '编制人', sortable: true, width: 100, halign: 'center', valign: 'middle', align: 'center' }
            ],
            queryParams: function (params) {
                var pagination = $('#gridTable').ysTable('getPagination', params);
                var queryString = $('#searchDiv').getWebControls(pagination);
                return queryString;
            }
        });
    }

    function searchGrid() {
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }
    function resetGrid() {
        $("#GradeId").ysComboBox('setValue', -1);
        $("#CourseId").ysComboBox('setValue', -1);
        $("#SchoolYearStart").ysComboBox('setValue', -1);
        $('#SchoolTerm').ysComboBox('setValue', -1);
        $("#PlanName").val('');
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }
    function showSaveForm(bAdd,id) {
        if (!bAdd && id == 0) {
            var selectedRow = $('#gridTable').bootstrapTable('getSelections');
            if (!ys.checkRowEdit(selectedRow)) {
                return;
            }
            else {
                id = selectedRow[0].Id;
            }
        }
        ys.openDialog({
            title: id > 0 ? '编辑' : '添加',
            content: '@Url.Content("~/ExperimentTeachManage/PlanInfo/Form")' + '?id=' + id,
            width: '768px',
            callback: function (index, layero) {
                var iframeWin = window[layero.find('iframe')[0]['name']];
                iframeWin.saveForm(index);
            }
        });
    }
    function showCopyForm(id) {
        ys.openDialog({
            title: '复制',
            content: '@Url.Content("~/ExperimentTeachManage/PlanInfo/Form")' + '?iscopy=1&id=' + id,
            width: '768px',
            callback: function (index, layero) {
                var iframeWin = window[layero.find('iframe')[0]['name']];
                iframeWin.saveForm(index);
            }
        });
    }
    function deleteForm(id) {
        ys.confirm('确认要删除这条数据吗？', function () {

            ys.ajax({
                url: '@Url.Content("~/ExperimentTeachManage/PlanInfo/DeleteFormJson")' + '?id=' + id,
                type: 'post',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        searchGrid();
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        });
    }

    function openDetailForm(id, gradeid, startyear, schoolterm) {
        var url = '@Url.Content("~/ExperimentTeachManage/PlanDetail/List")' + '?id=' + id + '&gradeid=' + gradeid+ '&startyear=' + startyear + '&schoolterm=' + schoolterm;
        createMenuItem(url, "编制计划");
    }
    function openLookDetailForm(id) {
        var url = '@Url.Content("~/ExperimentTeachManage/PlanDetail/ListDetail")' + '?id=' + id;
        createMenuItem(url, "计划明细");
    }
</script>
