﻿@{ Layout = "~/Views/Shared/_FormWhite.cshtml"; }
@inject Microsoft.AspNetCore.Hosting.IWebHostEnvironment HostingEnvironment
@{
    OperatorInfo operatorInfo = ViewBag.OperatorInfo;
}
<div class="wrapper animated fadeInRight">
    <form id="form" class="form-horizontal m">

        <div class="form-group row">
            <label class="col-sm-2 control-label ">姓名<font class="red"> *</font></label>
            <div class="col-sm-4">
                <input id="realName" col="RealName" type="text" class="form-control" />
            </div>
            <label class="col-sm-2 control-label ">手机号码<font class="red"> *</font></label>
            <div class="col-sm-4">
                <input id="mobile" col="Mobile" type="text" class="form-control" />
            </div>
        </div>

        <div class="form-group row">
            <label class="col-sm-2 control-label ">账号<font class="red"> *</font></label>
            <div class="col-sm-4">
                <input id="userName" col="UserName" type="text" autocomplete="new-password"  class="form-control" />
            </div>

            @if (operatorInfo.IsThirdLogin == 0)
            {
                <label class="col-sm-2 control-label ">密码<font class="red"> *</font></label>
                <div class="col-sm-4">
                    <input id="password" col="Password" type="password" autocomplete="new-password" class="form-control" />
                    <span id="spanPswordTip" style="color: #AEAEAE;">不修改，请留空！</span>
                </div>
            }
           
        </div>

        <div class="form-group row">
            <label class="col-sm-2 control-label">角色<font class="red"> *</font></label>
            <div class="col-sm-10" id="role" col="RoleIds"></div>
        </div>

        <div class="form-group row">
            <label class="col-sm-2 control-label ">性别</label>
            <div class="col-sm-4">
                <div id="gender" col="Gender"></div>
            </div>
            <label class="col-sm-2 control-label ">生日</label>
            <div class="col-sm-4">
                <input id="birthday" col="Birthday" type="text" class="form-control" />
            </div>
        </div>

        <div class="form-group row">
            <label class="col-sm-2 control-label">邮箱</label>
            <div class="col-sm-10">
                <input id="email" col="Email" type="text" class="form-control" />
            </div>
        </div>

        <div class="form-group row">
            <label class="col-sm-2 control-label">所属部门</label>
            <div class="col-sm-10">
                <div id="departmentId" class="ztree"></div>
            </div>
        </div>

    </form>
</div>
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/md5/js/md5.min.js"))


<script type="text/javascript">
    var id = ys.request("id");
    $(function () {

        if (id > 0) {
            $("#spanPswordTip").show();
        } else {
            $("#spanPswordTip").hide();
        }

        $("#gender").ysRadioBox({ data: ys.getJson(@Html.Raw(typeof(Dqy.Syjx.Enum.OrganizationManage.GenderUserTypeEnum).EnumToDictionaryString())) });

        $("#role").ysCheckBox({
            url: '@Url.Content("~/SystemManage/Role/GetRoleListJson")',
            key: "Id",
            value: "RoleName"
        });


        $('#departmentId').ysTree({
            async: false,
            url: '@Url.Content("~/OrganizationManage/Department/GetDepartmentTreeListJson")',
            maxHeight:'300px',
            check: { enable: true, chkboxType: { "Y": "", "N": "" } },
            expandLevel: 2,
        })

        laydate.render({ elem: '#birthday', format: 'yyyy-MM-dd', trigger: 'click', position: 'fixed' });

        getForm(id);

        $("#mobile").change(function () {
            if ($("#form").validate().element($("#mobile"))) {
                if ($("#userName").val() == "") {
                    $("#userName").val($(this).val());
                }
            }
        });

        $("#form").validate({
            rules: {
                userName: { required: true },
                password: {
                    required: true,
                    minlength: 8,
                    maxlength: 20,
                    isLoginpass: true
                },
                realName: {
                    required: true,
                    minlength: 2,
                    maxlength: 20
                },
                mobile: {
                    required: true,
                    isPhone: true
                },
                email: { email: true },

            }
        });
    });

    function getForm() {
        if (id > 0) {
            //$("#password").validate({ required: false });

            $("#form").validate({
                rules: {
                    userName: { required: true },
                    password: {
                        required: false,
                        isLoginpass: true
                    },
                    realName: {
                        required: true,
                        minlength: 2,
                        maxlength: 20
                    },
                    mobile: {
                        required: true,
                        isPhone: true
                    },
                    email: { email: true },

                }
            });

            ys.ajax({
                url: '@Url.Content("~/OrganizationManage/User/GetFormJsonById")' + '?id=' + id,
                type: "get",
                success: function (obj) {
                    if (obj.Tag == 1) {
                        var result = obj.Data;
                        $("#form").setWebControls(result);

                        $('#departmentId').ysTree('setCheckedNodes', result.DepartmentIds)
                    }
                }
            });
        }
        else {
            var defaultData = {};
            defaultData.UserName = "";
            defaultData.Password = ""
            defaultData.UserStatus = "@StatusEnum.Yes.ParseToInt()";
            $("#form").setWebControls(defaultData);
        }
    }

    function saveForm(index) {
        if ($("#form").validate().form()) {
            var postData = $("#form").getWebControls({ Id: id });
            var roleIds = postData.RoleIds;
            if (roleIds == "") {
                ys.msgError("请选择用户角色");
                return;
            }
            var passWord = postData.Password;
            if (passWord != null && passWord != "") {
                postData.Password = $.md5(postData.Password);
            }
            if (id > 0 && (passWord == "" || passWord == undefined)) {
                postData.Password = "NoChange";
            }
            postData.DepartmentIds = $('#departmentId').ysTree("getCheckedNodes");
            ys.ajax({
                url: '@Url.Content("~/OrganizationManage/User/SaveUserFormJson")',
                type: "post",
                data: postData,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        parent.$('#gridTable').ysTable('refresh');parent.resetToolbarStatus();
                        parent.layer.close(index);
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        }
    }
</script>
