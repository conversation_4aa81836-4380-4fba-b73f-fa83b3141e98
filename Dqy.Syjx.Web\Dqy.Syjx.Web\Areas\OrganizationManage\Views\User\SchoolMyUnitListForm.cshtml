﻿@{ Layout = "~/Views/Shared/_FormWhite.cshtml"; }
@inject Microsoft.AspNetCore.Hosting.IWebHostEnvironment HostingEnvironment
@{
    OperatorInfo operatorInfo = ViewBag.OperatorInfo;
}
<style>
    #role .check-box {
        width: 160px;
        white-space: nowrap;
    }
</style>
<div class="wrapper animated fadeInRight">
    <form id="form" class="form-horizontal m">

        <div class="form-group row">
            <label class="col-sm-2 control-label ">姓名<font class="red"> *</font></label>
            <div class="col-sm-10">
                <input id="realName" col="RealName" type="text" class="form-control" />
            </div>

        </div>
        <div class="form-group row">
            <label class="col-sm-2 control-label ">手机号码<font id="spanMobile" class="red"> *</font></label>
            <div class="col-sm-10">
                <input id="mobile" col="Mobile" type="text" class="form-control" />
            </div>
        </div>

        <div class="form-group row">
            <label class="col-sm-2 control-label ">
                <span class="glyphicon glyphicon-question-sign titleprompt inputprompt" aria-hidden="true" data-container="body" data-toggle="tooltip" data-placement="right"
                      data-content="可修改"></span>
                账号<font class="red"> *</font>
            </label>
            <div class="col-sm-10">
                <input id="userName" col="UserName" type="text" autocomplete="new-password" class="form-control" />
            </div>

        </div>

        @if (operatorInfo.IsThirdLogin == 0)
        {
            <div class="form-group row">
                <label class="col-sm-2 control-label ">密码<font class="red"> *</font></label>
                <div class="col-sm-10">
                    <input id="password" col="Password" type="password" autocomplete="new-password" class="form-control" />
                    <span id="spanPswordTip" style="color: #AEAEAE;">不修改，请留空！</span>
                </div>
            </div>
        }
       

        <div class="form-group row">
            <label class="col-sm-2 control-label">角色说明</label>
            <div class="col-sm-10">
                @*<a id="btnSearch" class="btn btn-primary btn-sm" onclick="showRoleDemo()"><i class="fa fa-search"></i>&nbsp;查看</a>*@
                <a class="btn btn-info btn-xs" href="#" onclick="showRoleDemo()" style="margin-top:10px;"><i class="fa fa-eye"></i>查看</a>
            </div>
        </div>

        <div class="form-group row">
            <label class="col-sm-2 control-label ">
                <span class="glyphicon glyphicon-question-sign titleprompt inputprompt" aria-hidden="true" data-container="body" data-toggle="tooltip" data-placement="right"
                      data-content="一个用户可设置多个角色，具体操作权限请点击角色说明查看"></span>
                角色<font class="red"> *</font>
            </label>
            <div class="col-sm-10" id="role" col="RoleIds"></div>
        </div>

        <div class="form-group row">
            <label class="col-sm-2 control-label ">性别</label>
            <div class="col-sm-4">
                <div id="gender" col="Gender"></div>
            </div>
            <label class="col-sm-2 control-label ">生日</label>
            <div class="col-sm-4">
                <input id="birthday" col="Birthday" type="text" class="form-control" />
            </div>
        </div>

        <div class="form-group row">
            <label class="col-sm-2 control-label">邮箱</label>
            <div class="col-sm-10">
                <input id="email" col="Email" type="text" class="form-control" />
            </div>
        </div>

        <div class="form-group row">
            <label class="col-sm-2 control-label ">
                <span class="glyphicon glyphicon-question-sign titleprompt inputprompt" aria-hidden="true" data-container="body" data-toggle="tooltip" data-placement="right"
                      data-content="一个用户可属于多个部门"></span>
                所属部门
            </label>
            <div class="col-sm-10">
                <div id="departmentId" class="ztree"></div>
            </div>
        </div>

        <input type="hidden" id="hidRole" value="" />
    </form>
</div>
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/md5/js/md5.min.js"))


<script type="text/javascript">
    var id = ys.request("id");
    $(function () {
        $(".inputprompt").popover({
            trigger: 'hover',
            html: true
        });

        if (id > 0) {
            $("#spanPswordTip").show();
        } else {
            $("#spanPswordTip").hide();
            $("#form").validate({
                rules: {
                    userName: { required: true },
                    password: {
                        required: true,
                        minlength: 8,
                        maxlength: 20,
                        isLoginpass: true
                    },
                    realName: {
                        required: true,
                        minlength: 2,
                        maxlength: 20
                    },
                    mobile: {
                        required: true,
                        isPhone: true
                    },
                    email: { email: true },

                }
            });
        }

        $("#gender").ysRadioBox({ data: ys.getJson(@Html.Raw(typeof(Dqy.Syjx.Enum.OrganizationManage.GenderUserTypeEnum).EnumToDictionaryString())) });

        $("#role").ysCheckBox({
            url: '@Url.Content("~/SystemManage/Role/GetRoleListJson")',
            key: "Id",
            value: "RoleName"
        });


        $('#departmentId').ysTree({
            async: false,
            url: '@Url.Content("~/OrganizationManage/Department/GetUserSetDepartmentTreeListJson")',
            maxHeight:'300px',
            check: { enable: true, chkboxType: { "Y": "", "N": "" } },
            expandLevel: 2,
        })

        laydate.render({ elem: '#birthday', format: 'yyyy-MM-dd', trigger: 'click',position: 'fixed' });

        getForm(id);

        $("#mobile").change(function () {
            if ($("#form").validate().element($("#mobile"))) {
                if ($("#userName").val() == "") {
                    $("#userName").val($(this).val());
                }
            }
        });

        
    });

    function getForm() {
        if (id > 0) {


            ys.ajax({
                url: '@Url.Content("~/OrganizationManage/User/GetFormJsonById")' + '?id=' + id,
                type: "get",
                success: function (obj) {
                    if (obj.Tag == 1) {
                        var result = obj.Data;
                       
                        if (result.ThirdUserId != null && result.ThirdUserId != "") {
                            $("#spanMobile").hide();
                            $("#form").validate({
                                rules: {
                                    userName: { required: true },
                                    password: {
                                        required: false,
                                        isLoginpass: true
                                    },
                                    realName: {
                                        required: true,
                                        minlength: 2,
                                        maxlength: 20
                                    },
                                    mobile: {
                                        required: false,
                                        isPhone: true
                                    },
                                    email: { email: true },

                                }
                            });
                        } else {
                            $("#spanMobile").show();
                            $("#form").validate({
                                rules: {
                                    userName: { required: true },
                                    password: {
                                        required: false,
                                        isLoginpass: true
                                    },
                                    realName: {
                                        required: true,
                                        minlength: 2,
                                        maxlength: 20
                                    },
                                    mobile: {
                                        required: true,
                                        isPhone: true
                                    },
                                    email: { email: true },

                                }
                            });
                        }
                        $("#form").setWebControls(result);
                        $('#departmentId').ysTree('setCheckedNodes', result.DepartmentIds)
                    }
                }
            });
        }
        else {
            var defaultData = {};
            defaultData.UserName = "";
            defaultData.Password = ""
            defaultData.UserStatus = "@StatusEnum.Yes.ParseToInt()";
            $("#form").setWebControls(defaultData);
        }
    }

    function saveForm(index) {
        if ($("#form").validate().form()) {
            var postData = $("#form").getWebControls({ Id: id });
            var roleIds = postData.RoleIds;
            if (roleIds == "") {
                ys.msgError("请选择用户角色");
                return;
            }
            var passWord = postData.Password;
            if (passWord != null && passWord != "") {
                postData.Password = $.md5(postData.Password);
            }
            if (id > 0 && (passWord == "" || passWord == undefined)) {
                postData.Password = "NoChange";
            }
            postData.DepartmentIds = $('#departmentId').ysTree("getCheckedNodes");
            ys.ajax({
                url: '@Url.Content("~/OrganizationManage/User/SaveUserFormJson")',
                type: "post",
                data: postData,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        parent.$('#gridTable').ysTable('refresh');parent.resetToolbarStatus();
                        parent.layer.close(index);
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        }
    }

    //显示角色备注信息
    function showRoleDemo() {

        var layerTitle = "<b>角色说明</b>";
        var divHtml = "<div id='tipMsg'><table class='table' style='text-align:left;font-size:14px;'><thead><tr><th style='width:150px;text-align:center;font-weight:bold;'>角色名称</th><th style='text-align:center;font-weight:bold;'>操作说明</th></tr></thead>";
        divHtml += "<tbody>";
        ys.ajax({
            url: '@Url.Content("~/SystemManage/Role/GetRoleListJson")',
            type: 'get',
            async: false,
            success: function (obj) {
                if (obj.Tag == 1) {
                    var total = obj.Total;
                    if (total > 0) {
                        $(obj.Data).each(function (index, element) {
                            divHtml += $.Format("<tr><td style='text-align:center;'>{0}</td><td>{1}</td></tr>", element.RoleName, element.Remark);

                        });
                    }

                }
            }
        });
        divHtml += "</tbody></table></div>";

         
        //divHtml += $.Format("<tr><td style='text-align:center;'>{0}</td><td>{1}</td></tr>", "管理员","1、单位基本信息：社会统一信用代码、地址；<br/>2、班级：新学年班级创建与修改；<br/>3、课程节次表（学校作息时间表）：数据初始化，及夏季和冬季时间的修改；<br/>4、用户管理：账号添加和修改，角色添加和修改；<br/>5、组织机构（学校部门）：创建和修改；<br/>6、地点设置：学校楼宇和房间编号设置；<br/>7、管理人员设置：按学科设置实验室和专用室的管理人员；<br/>8、实验员：设置小学科学和中学理化生实验员的信息。<br/>");
        //divHtml += $.Format("<tr><td style='text-align:center;'>{0}</td><td>{1}</td></tr>", "一般人员","1、实验预约和登记：用于小学科学和中学理化生的教师；<br/>2、专用室登记：用于除小学科学和中学理化生以外的教师；");
        //divHtml += $.Format("<tr><td style='text-align:center;'>{0}</td><td>{1}</td></tr>", "实验室管理员", "1、计划填报：小学科学和中学理化生仪器采购计划填报；<br/>2、仪器管理：小学科学和中学理化生仪器入库与运维信息填写；<br/>3、实验室管理：小学科学和中学理化生实验室创建与运维信息填写；<br/>4、实验安排：小学科学和中学理化生实验开课安排。");
        //divHtml += $.Format("<tr><td style='text-align:center;'>{0}</td><td>{1}</td></tr>", "专用室管理员", "1、计划填报：除小学科学和中学理化生以外的仪器采购计划填报；<br/>2、仪器管理：除小学科学和中学理化生以外的仪器入库与运维信息填写；<br/>3、实验室管理：除小学科学和中学理化生以外的专用室创建与运维信息填写。");
        //divHtml += $.Format("<tr><td style='text-align:center;'>{0}</td><td>{1}</td></tr>", "备课组长", "实验计划：小学科学和中学理化生实验计划添加和修改。");
        //divHtml += $.Format("<tr><td style='text-align:center;'>{0}</td><td>{1}</td></tr>", "业务管理员", "计划审批：仪器采购计划审批。");
        //divHtml += $.Format("<tr><td style='text-align:center;'>{0}</td><td>{1}</td></tr>", "安全员", "安全管理：实验（专用）室安全排查与整改、制度与队伍建设、培训与安全教育、应急预案与演练。");

        parent.layer.open({
            title: layerTitle,
            type: 1,
            skin: 'layui-layer-demo',
            closeBtn: 1,
            shift: 2,
            //area: ["720px"],
            area: 'auto',
            maxWidth: '720px',
            maxHeight: '600px',
            shadeClose: true,
            content: divHtml,
         });
    }
</script>
