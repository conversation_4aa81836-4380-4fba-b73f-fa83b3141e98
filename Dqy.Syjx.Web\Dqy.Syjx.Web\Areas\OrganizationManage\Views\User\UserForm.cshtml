@{ Layout = "~/Views/Shared/_FormWhite.cshtml"; }
@inject Microsoft.AspNetCore.Hosting.IWebHostEnvironment HostingEnvironment

<style>
    #role .check-box {
        width: 160px;
    }
</style>
<div class="wrapper animated fadeInRight">
    <form id="form" class="form-horizontal m">
        <div class="form-group row">
            <label class="col-sm-2 control-label">单位名称<font class="red"> *</font></label>
            <div class="col-sm-10" id="unitName" col="UnitId"></div>
            <input type="hidden" id="hidUserExtensionId" col="UserExtensionId" value="0" />
        </div>

        <div class="form-group row">
            <label class="col-sm-2 control-label ">姓名<font class="red"> *</font></label>
            <div class="col-sm-10">
                <input id="realName" col="RealName" type="text" class="form-control" />
            </div>
        </div>

        <div class="form-group row">
            <label class="col-sm-2 control-label ">手机号码<font id="spanMobile" class="red"> *</font></label>
            <div class="col-sm-10">
                <input id="mobile" col="Mobile" type="text" class="form-control" />
            </div>
        </div>

        <div class="form-group row">
            <label class="col-sm-2 control-label ">
                <span class="glyphicon glyphicon-question-sign titleprompt inputprompt" aria-hidden="true" data-container="body" data-toggle="tooltip" data-placement="right"
                      data-content="可修改"></span>
                账号<font class="red"> *</font>
            </label>
            <div class="col-sm-10">
                <input id="userName" col="UserName" type="text" class="form-control" autocomplete="new-password"  />
            </div>
        </div>

        <div class="form-group row">
            <label class="col-sm-2 control-label ">密码<font class="red"> *</font></label>
            <div class="col-sm-10">
                <input id="password" col="Password" type="password" class="form-control" autocomplete="new-password"  />
                <span id="spanPswordTip" style="color: #AEAEAE;">不修改，请留空！</span>
            </div>
        </div>

        <div class="form-group row">
            <label class="col-sm-2 control-label">角色说明</label>
            <div class="col-sm-10">
                <a class="btn btn-info btn-xs" href="#" onclick="showRoleDemo()" style="margin-top:10px;"><i class="fa fa-eye"></i>查看</a>
            </div>
        </div>

        <div class="form-group row">
            <label class="col-sm-2 control-label ">
                <span class="glyphicon glyphicon-question-sign titleprompt inputprompt" aria-hidden="true" data-container="body" data-toggle="tooltip" data-placement="right"
                      data-content="一个用户可设置多个角色，具体操作权限请点击角色说明查看"></span>
                角色<font class="red"> *</font>
            </label>
            <div class="col-sm-10" id="role" col="RoleIds"></div>
        </div>
        <div class="form-group row">
            <label class="col-sm-2 control-label ">性别</label>
            <div class="col-sm-4">
                <div id="gender" col="Gender"></div>
            </div>
            <label class="col-sm-2 control-label">生日</label>
            <div class="col-sm-4">
                <input id="birthday" col="Birthday" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-2 control-label ">邮箱</label>
            <div class="col-sm-4">
                <input id="email" col="Email" type="text" class="form-control" />
            </div>
            <label class="col-sm-2 control-label">状态</label>
            <div class="col-sm-4" id="userStatus" col="UserStatus"></div>
        </div>

        <div class="form-group row">
            <label class="col-sm-2 control-label ">
                <span class="glyphicon glyphicon-question-sign titleprompt inputprompt" aria-hidden="true" data-container="body" data-toggle="tooltip" data-placement="right"
                      data-content="一个用户可属于多个部门"></span>
                所属部门
            </label>
            <div class="col-sm-10">
                <div id="departmentId" class="ztree"></div>
            </div>
        </div>

        <div class="form-group row">
            <label class="col-sm-2 control-label ">有效期</label>
            <div class="col-sm-10">
                <input id="userValidate" col="UserValidate" type="text" class="form-control" />
            </div>
        </div>
    </form>
</div>
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/md5/js/md5.min.js"))

<script type="text/javascript">
    var id = ys.request("id");
    $(function () {

        $(".inputprompt").popover({
            trigger: 'hover',
            html: true
        });

        if (id > 0) {
            $("#spanPswordTip").show();
        } else {
            $("#spanPswordTip").hide();
            $("#form").validate({
                rules: {
                    userName: { required: true },
                    realName: {
                        required: true,
                        minlength: 2,
                        maxlength: 20
                    },
                    password: {
                        required: true,
                        minlength: 8,
                        maxlength: 20,
                        isLoginpass: true
                    },
                    mobile: { required: true, isPhone: true },
                    email: { email: true }
                }
            });
        }

        $("#userStatus").ysRadioBox({ data: ys.getJson(@Html.Raw(typeof(StatusEnum).EnumToDictionaryString())) });
        $("#gender").ysRadioBox({ data: ys.getJson(@Html.Raw(typeof(Dqy.Syjx.Enum.OrganizationManage.GenderUserTypeEnum).EnumToDictionaryString())) });
        $('#unitName').ysComboBox(
            {
                url: '@Url.Content("~/OrganizationManage/Unit/GetListJson")' + "?PageSize=10000",
                class: 'form-control col-sm-8',
                key: 'Id',
                value: 'Name',
                onChange: function () {
                    $("#role").html("");
                    $("#departmentId").html("");
                    var unitId = $('#unitName').ysComboBox('getValue');
                    if (unitId == "") {
                        unitId = 0;
                    }
           ;
                    $("#role").ysCheckBox({
                        url: '@Url.Content("~/SystemManage/Role/GetRoleListJsonByUnitId")' + "?unitId=" + unitId,
                        key: "Id",
                        value: "RoleName"
                    });

                    $('#departmentId').ysTree({
                        async: false,
                        url: '@Url.Content("~/OrganizationManage/Department/GetUserSetDepartmentTreeListJson")' + "?UnitId=" + unitId,
                        maxHeight: '300px',
                        check: { enable: true, chkboxType: { "Y": "", "N": "" } },
                        expandLevel: 2,
                    });
                }
            });

        laydate.render({ elem: '#birthday', format: 'yyyy-MM-dd', trigger: 'click', position: 'fixed' });
        laydate.render({ elem: '#userValidate', format: 'yyyy-MM-dd', trigger: 'click', position: 'fixed' });

        getForm(id);

        $("#mobile").change(function () {
            if ($("#form").validate().element($("#mobile"))) {
                if ($("#userName").val() == "") {
                    $("#userName").val($(this).val());
                }
            }
        });

        
    });

    function getForm() {
        if (id > 0) {
            //$('#password').attr("readonly", "readonly").attr("disabled", "disabled");

            ys.ajax({
                url: '@Url.Content("~/OrganizationManage/User/GetFormJson")' + '?id=' + id,
                type: "get",
                success: function (obj) {
                    if (obj.Tag == 1) {
                        var result = obj.Data;

                        if (result.ThirdUserId != null && result.ThirdUserId != "") {
                            $("#spanMobile").hide();
                            $("#form").validate({
                                rules: {
                                    userName: { required: true },
                                    realName: {
                                        required: true,
                                        minlength: 2,
                                        maxlength: 20
                                    },
                                    password: {
                                        required: false,
                                        isLoginpass: true
                                    },
                                    mobile: { required: false, isPhone: true },
                                    email: { email: true }
                                }
                            });
                        } else {
                            $("#spanMobile").show();
                            $("#form").validate({
                                rules: {
                                    userName: { required: true },
                                    realName: {
                                        required: true,
                                        minlength: 2,
                                        maxlength: 20
                                    },
                                    password: {
                                        required: false,
                                        isLoginpass: true
                                    },
                                    mobile: { required: true, isPhone: true },
                                    email: { email: true }
                                }
                            });
                        }
                        $("#form").setWebControls(result);
                        $('#departmentId').ysTree('setCheckedNodes', result.DepartmentIds);
                        var UserValidate = result.UserValidate;
                        if (UserValidate != null) {
                            UserValidate = ys.formatDate(UserValidate, "yyyy-MM-dd");
                            $("#userValidate").val(UserValidate);
                        }

                    }
                }
            });
        }
        else {
            var defaultData = {};
            defaultData.UserName = "";
            defaultData.Password = ""
            defaultData.UserStatus = "@StatusEnum.Yes.ParseToInt()";
            $("#form").setWebControls(defaultData);
        }
    }

    function saveForm(index) {
        if ($("#form").validate().form()) {
            var postData = $("#form").getWebControls({ Id: id });
            var unitId = postData.UnitId;
            if (unitId == "") {
                ys.msgError("请选择单位名称");
                return;
            }
            var roleIds = "";
            $("input:checkbox[name='role_checkbox']:checked").each(function () {
                roleIds += $(this).val() + ",";
            });
            if (roleIds == "") {
                ys.msgError("请选择用户角色");
                return;
            }
            var passWord = postData.Password;
            if (passWord != null && passWord != "") {
                postData.Password = $.md5(postData.Password);
            }
            if (id > 0 && passWord == "") {
                postData.Password = "NoChange";
            }
            postData.RoleIds = roleIds;
            //postData.DepartmentId = ys.getLastValue(postData.DepartmentId);
            postData.DepartmentIds = $('#departmentId').ysTree("getCheckedNodes");
            ys.ajax({
                url: '@Url.Content("~/OrganizationManage/User/SaveFormJson")',
                type: "post",
                data: postData,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        parent.$('#gridTable').ysTable('refresh');parent.resetToolbarStatus();
                        parent.layer.close(index);
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        }
    }

     //显示角色备注信息
    function showRoleDemo() {

        var layerTitle = "<b>角色说明</b>";
        var divHtml = "<div id='tipMsg'><table class='table' style='text-align:left;font-size:14px;'><thead><tr><th style='width:150px;text-align:center;font-weight:bold;'>角色名称</th><th style='text-align:center;font-weight:bold;'>操作说明</th></tr></thead>";
        divHtml += "<tbody>";
        ys.ajax({
            url: '@Url.Content("~/SystemManage/Role/GetRoleListJson")',
            type: 'get',
            async: false,
            success: function (obj) {
                if (obj.Tag == 1) {
                    var total = obj.Total;
                    if (total > 0) {
                        $(obj.Data).each(function (index, element) {
                            divHtml += $.Format("<tr><td style='text-align:center;'>{0}</td><td>{1}</td></tr>", element.RoleName, element.Remark);

                        });
                    }

                }
            }
        });
        divHtml += "</tbody></table></div>";
        parent.layer.open({
            title: layerTitle,
            type: 1,
            skin: 'layui-layer-demo',
            closeBtn: 1,
            shift: 2,
            area: 'auto',
            maxWidth: '720px',
            maxHeight: '600px',
            shadeClose: true,
            content: divHtml,
         });
    }
</script>
