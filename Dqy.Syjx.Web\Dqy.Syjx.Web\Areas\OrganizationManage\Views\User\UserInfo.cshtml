﻿@using Dqy.Syjx.Entity.SystemManage
@{
    Layout = "~/Views/Shared/_Index.cshtml";
}
@inject Microsoft.AspNetCore.Hosting.IWebHostEnvironment HostingEnvironment
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/laydate/5.0.9/laydate.min.js"))
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/yisha/css/style.min.css"))
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/yisha/js/yisha.min.js"))

@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/jquery.validation/1.14.0/jquery.validate.min.js"))

@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/icheck/1.0.2/skins/custom.min.css"))
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/icheck/1.0.2/icheck.min.js"))

@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/select2/4.0.6/css/select2.min.css"))
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/select2/4.0.6/js/select2.min.js"))

@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/zTree/v3/css/metroStyle/metroStyle.min.css"))
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/zTree/v3/js/ztree.min.js"))

<div class="container-div">
    <div class="btn-group-sm hidden-xs" id="toolbar">
    </div>
    <div class="col-sm-12 select-table table-striped">

        <div class="ibox-title">
            <h5>我的信息</h5>
        </div>

        <div class="card-body">
            <form id="form" class="form-horizontal m">
                <input id="Id" col="Id" type="hidden" />
                <div id="divDepartment" class="form-group row">
                    <label class="col-sm-2 control-label ">所属部门：</label>
                    <div class="col-sm-10">
                        @*<div id="departmentId" class="ztree"></div>*@
                        <input id="DepartmentNames" col="DepartmentNames" type="text" class="form-control" readonly />
                    </div>
                </div>
                <div class="form-group row">
                    <label class="col-sm-2 control-label ">账号：</label>
                    <div class="col-sm-10">
                        <input id="UserName" col="UserName" type="text" class="form-control" readonly />
                    </div>
                </div>
                <div class="form-group row">
                    <label class="col-sm-2 control-label "><font class="red"> *</font>姓名：</label>
                    <div class="col-sm-10">
                        <input id="RealName" col="RealName" type="text" class="form-control" />
                    </div>
                </div>
                <div class="form-group row">
                    <label class="col-sm-2 control-label "><font class="red"> *</font>手机号码：</label>
                    <div class="col-sm-10">
                        <input id="Mobile" col="Mobile" type="text" class="form-control" />
                    </div>
                </div>
                <div class="form-group row">
                    <label class="col-sm-2 control-label ">QQ：</label>
                    <div class="col-sm-10">
                        <input id="QQ" col="QQ" type="text" class="form-control" />
                    </div>
                </div>
            </form>
        </div>
        <div class="btn-group-sm hidden-xs" id="toolbar" style="text-align: center;">
            <a id="btnAdd" class="btn btn-info" onclick="saveForm();"><i class="fa fa-edit"></i> 保存</a>
        </div>
    </div>
    @*</div>*@
</div>
<script type="text/javascript">
    $(function () {
        @*$('#departmentId').ysTree({
            async: false,
            url: '@Url.Content("~/OrganizationManage/Department/GetDepartmentTreeListJson")',
            maxHeight: '300px',
            check: { enable: true, chkboxType: { "Y": "", "N": "" } },
            expandLevel: 2,
        })*@

        getForm();

        $("#form").validate({
            rules: {
                RealName: {
                    required: true,
                    minlength: 2,
                    maxlength: 20
                },
                Mobile: {
                    required: true,
                    isPhone: true
                },
                QQ: {
                    isQQ: true
                }
            }
        });
    });

    function getForm() {
        ys.ajax({
                url: '@Url.Content("~/OrganizationManage/User/GetCurrentUserInfo")',
                type: "get",
                success: function (obj) {
                    if (obj.Tag == 1) {
                        var result = obj.Data;

                        $("#form").setWebControls(result);
                        if (result.UnitType == "@UnitTypeEnum.School.ParseToInt()") {
                            $("#divDepartment").show();
                        } else {
                            $("#divDepartment").hide();
                        }
                        //$('#departmentId').ysTree('setCheckedNodes', result.DepartmentIds)
                    }
                }
            });
    }

    function saveForm() {
        if ($('#form').validate().form()) {
            var postData = $('#form').getWebControls();
            ys.ajax({
                url: '@Url.Content("~/OrganizationManage/User/SaveUserInfo")',
                type: 'post',
                data: postData,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        }
    }

</script>
