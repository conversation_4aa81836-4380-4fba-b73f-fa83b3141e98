﻿@{
    Layout = "~/Views/Shared/_FormWhite.cshtml";
}
<script src='@Url.Content("~/junfei/js/clipboard.min.js")' type="text/javascript"></script>
<div class="wrapper animated fadeInRight">
    <form id="form" class="form-horizontal m">
        <div class="form-group row">
            <label class="col-sm-3 control-label "></label>
            <div class="col-sm-5">
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-1"></label>
            <div class="col-sm-9">
                手机可扫描二维码进行测评，电脑可链接网址进行测评。
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-1"></label>
            <div class="col-sm-10">
                <img width="150" height="170" id="ImgQrCode" />

                <label id="Link" style="margin-right:10px;"></label>

                <input type="button" value="复制链接" id="CopyLink" onclick="copy()"/>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label" style="margin-left:40px;">本二维码和链接地址有效期限</label>
            <div class="col-sm-6">
                <div id="Deadline" col="Deadline" style="width:120px !important;" />
            </div>
        </div>
    </form>
</div>
<script type="text/javascript">
    var examineid = ys.request("examineid"); //考核表(cp_Examine) Id
    var paperid = ys.request("paperid"); //试卷Id
    $(function () {
        getForm();
    });

    //有效期限
    function loadDeadline() {
        var arrDeadline = [
            { key: '07:00', value: '07:00' },
            { key: '08:00', value: '08:00' },
            { key: '09:00', value: '09:00' },
            { key: '10:00',  value: '10:00' },
            { key: '11:00',  value: '11:00' },
            { key: '12:00',  value: '12:00' },
            { key: '13:00',  value: '13:00' },
            { key: '14:00',  value: '14:00' },
            { key: '15:00',  value: '15:00' },
            { key: '16:00', value: '16:00' },
            { key: '17:00', value: '17:00' },
            { key: '18:00', value: '18:00' },
            { key: '19:00', value: '19:00' },
            { key: '20:00', value: '20:00' },
            { key: '21:00', value: '21:00' },
            { key: '22:00', value: '22:00' },
            { key: '23:00', value: '23:00' }
        ];
        $('#Deadline').ysComboBox({
            data: arrDeadline,
            key: 'key',
            value: 'value',
            defaultName: '请选择有效期限'
        });
    }

    //复制链接
    function copy() {
        var localUrl = $("#Link").html();
        var clipboard = new Clipboard(('#CopyLink'), {
            text: function () {
                return localUrl;
            }
        });

        clipboard.on('success', function (e) {
            ys.msgSuccess('已复制到剪贴板！');
        });

        clipboard.on('error', function (e) {
            console.log(e);
        });
    }

    function getForm() {
        loadDeadline();
        if (examineid > 0) {
            ys.ajax({
                url: '@Url.Content("~/PaperExamineManage/ExamineAddress/GetExamineAddressJson")' + '?ExamineId=' + examineid + "&PaperId=" + paperid,
                type: 'get',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        $("#ImgQrCode").attr('src', obj.Data[0].QRCode);
                        $("#Link").html(obj.Data[0].Path);
                        $('#Deadline').ysComboBox('setValue', obj.Data[0].Deadline);
                    }
                }
            });
        }
    }

    function saveForm(index) {
        var msg = '';
        if ($('#form').validate().form()) {
            var postData = $('#form').getWebControls({ ExamineId: examineid });
            postData.ExamineId = examineid;
            postData.PaperId = paperid;
            postData.Deadline = $('#Deadline').ysComboBox('getValue');
            if (!(postData.Deadline != undefined && postData.Deadline !='-1')) {
                msg += '请选择有效期限！<br/>';
            }
            if (msg != '') {
                ys.msgError("验证失败<br/>" + msg);
                return;
            }
            ys.ajax({
                url: '@Url.Content("~/PaperExamineManage/ExamineAddress/SaveFormJson")',
                type: 'post',
                data: postData,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                    }
                    else {
                       ys.msgError(obj.Message);
                    }
                    parent.$('#gridTable').ysTable('refresh');parent.resetToolbarStatus();
                    parent.layer.close(index);
                }
            });
        }
    }
</script>

