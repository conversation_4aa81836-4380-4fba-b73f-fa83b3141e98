﻿@{
    Layout = "~/Views/Shared/_FormWhite.cshtml";
}

<div class="wrapper animated fadeInRight">
    <form id="form" class="form-horizontal m">
        <div class="form-group row">
            <label class="col-sm-3 control-label ">状态</label>
            <div class="col-sm-8">
                <div id="Statuz" col="Statuz"></div>
            </div>
        </div>
    </form>
</div>

<script type="text/javascript">
    $(function () {
        $("#Statuz").ysRadioBox({ data: ys.getJson(@Html.Raw(typeof(StatusEnum).EnumToDictionaryString())), class:"form-control"});
    });
    function saveForm(index) {
        var formData = $('#form').getWebControls();
        var postData = { evaluatestandardid: 0, ids: '', state: 0 };
        var ids = parent.getSelectIds();
        var errorMsg = '';
        if (ids == undefined || ids.length == 0) {
            errorMsg += '请选择需要设置的数据。<br/>';
        } else {
            postData.ids = ids;
        }
        if (formData.Statuz != 1 && formData.Statuz != 2) {
            errorMsg += '请选择状态。<br/>';
        } else {
            postData.state = formData.Statuz;
        }
        if (errorMsg != '') {
            ys.msgError('验证失败，请填写完成再提交！<br/>' + errorMsg);
            return;
        }
        postData.evaluatestandardid = parent.standardid;
        ys.ajax({
            url: '@Url.Content("~/EvaluateManage/FunRoomEvaluateEnorm/SaveStateFormJson")',
            type: 'post',
            data: postData,
            success: function (obj) {
                if (obj.Tag == 1) {
                    ys.msgSuccess(obj.Message);
                    parent.$('#gridTable').ysTable('refresh');parent.resetToolbarStatus();
                    parent.layer.close(index);
                }
                else {
                    ys.msgError(obj.Message);
                }
            }
        });
    }
</script>

