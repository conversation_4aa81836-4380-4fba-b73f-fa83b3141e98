﻿@{
    Layout = "~/Views/Shared/_Index.cshtml";
}
@inject Microsoft.AspNetCore.Hosting.IWebHostEnvironment HostingEnvironment
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/fileinput/5.0.3/css/fileinput.min.css"))
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/fileinput/5.0.3/js/fileinput.min.js"))
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/uploadifive/File.min.css"))
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/uploadifive/File.min.js"))

<style type="text/css">
    .check-box {
        width: 100px;
    }

    .iradio-box {
        width: 100px;
    }

    .iradio-box {
        position: initial;
        display: inline-block;
    }

    .iradio-blue {
        position: inherit;
        display: inline-block;
        /*  top: 6px;*/
    }

    .div-experit {
        border: 1px dashed #999;
        margin: 15px 0px 15px 0px;
        padding-top: 15px;
    }
</style>
<div class="container-div">

    <div class="row" style="height:auto;">
        <div class="col-sm-12">
            <div class="ibox float-e-margins">
                <div class="ibox-title">
                    <h5>实验预约</h5>
                    <div class="ibox-tools">
                        <a class="collapse-link">
                            <i class="fa fa-chevron-up"></i>
                        </a>
                    </div>
                </div>
                <div class="ibox-content">
                    <div class="row">
                        <div class="col-sm-12" style="font-size:14px">
                            <div class="card-body">
                                <form id="baseForm" class="form-horizontal m">
                                    <div class="form-group row">
                                        <label class="col-sm-2 control-label ">学期</label>
                                        <div class="col-sm-8">
                                            <input id="SchoolYearStartTerm" class="form-control" readonly />
                                        </div>
                                        <div class="col-sm-1"></div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-sm-2 control-label ">适用学科</label>
                                        <div class="col-sm-8">
                                            <input id="CourseName" col="CourseName" class="form-control" readonly />
                                        </div>
                                        <div class="col-sm-1"></div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-sm-2 control-label ">课程名称</label>
                                        <div class="col-sm-8">
                                            <input id="Name" col="Name" class="form-control" readonly />
                                        </div>
                                        <div class="col-sm-1"></div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-sm-2 control-label ">上课地点</label>
                                        <div class="col-sm-8">
                                            <input id="FunRoomName" col="FunRoomName" class="form-control" readonly />
                                        </div>
                                        <div class="col-sm-1"></div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-sm-2 control-label ">上课时间</label>
                                        <div class="col-sm-8">
                                            <input id="ClassTimeStr" col="ClassTimeStr" class="form-control" readonly />
                                        </div>
                                        <div class="col-sm-1"></div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-sm-2 control-label ">实验名称</label>
                                        <div class="col-sm-8">
                                            <input id="ExperimentName" col="ExperimentName" class="form-control" readonly />
                                        </div>
                                        <div class="col-sm-1"></div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-sm-2 control-label ">实验类型</label>
                                        <div class="col-sm-8">
                                            <input id="FunRoomName" col="FunRoomName" class="form-control" readonly />
                                        </div>
                                        <div class="col-sm-1"></div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="row" style="height:auto;">
        <div class="col-sm-12">
            <div class="ibox float-e-margins">
                <div class="ibox-title">
                    <h5>实验登记</h5>
                </div>
                <div class="ibox-content">
                    <div class="row">
                        <div class="col-sm-12" style="font-size:14px">
                            <div class="card-body">
                                <form id="fileRecordForm" class="form-horizontal m">
                                    <div class="form-group row">
                                        <label class="col-sm-2 control-label " id="labExperimentSum">实验结果</label>
                                        <div class="col-sm-8">
                                            <textarea id="experimentSummary" col="ExperimentSummary" class="form-control" placeholder="如实验内容、效果、改进意见等（限1000字）"></textarea>
                                        </div>
                                    </div>
                                    <div id="divMoreDj"></div>
                                    <div class="form-group row" id="divImageFile">
                                        <label class="col-sm-2 control-label " id="labImage">
                                            现场照片
                                        </label>
                                        <div class="col-sm-5">
                                            <input type="file" name="uploadify" id="uploadify" />
                                            <div id="spanFile" style="padding: 10px 0px;"></div>
                                            <div style="height: 55px;display:none;" id="fileQueue"></div>
                                        </div>
                                        <span style="color:#999;font-size:12px;">上课场景照片不超三张，文件小于5M，支持图片文件</span>
                                    </div>
                                    <div class="form-group run-info">
                                        <label class="col-sm-2 control-label ">设备运行</label>
                                        <div class="col-sm-8">
                                            <div id="runStatuz"></div>
                                        </div>
                                    </div>
                                    <div class="form-group run-info" id="divProblem" style="display:none;">
                                        <label class="col-sm-2 control-label ">问题描述<font class="red"> *</font></label>
                                        <div class="col-sm-8">
                                            <textarea id="problemDesc" col="ProblemDesc" class="form-control"></textarea>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row" style="text-align:center;" id="divButton">
        <a id="btnAdd" class="btn btn-info" onclick="saveData()"><i class="fa fa-save"></i> 提交</a>
        <span style="margin-left:20px;color:#999;">实验结束</span>
    </div>
</div>

<script type="text/javascript">
    var id = ys.request("id");
    var Com_BookingId = ys.request("bookingid");
    var IsRelation = 0;
    var SaveValiDate = [];
    var IsRequiredExperimentSumNeed = 0;
    $(function () {
        $(".inputprompt").popover({
            trigger: 'hover',
            html: true
        });

        loadRunStatuz();
        loadUploadify();

        getForm();
    });

    function getForm() {
        if (id > 0) {
            ys.ajax({
                url: '@Url.Content("~/ExperimentTeachManage/ExperimentCriticize/GetRecordJson")' + '?id=' + id + '&bookingid=' + Com_BookingId,
                type: 'get',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        obj.Data.ClassTime = ys.formatDate(obj.Data.ClassTime, "yyyy-MM-dd");
                        $('#baseForm').setWebControls(obj.Data);

                        var schoolyearterm = (obj.Data.SchoolYearStart + '').substr(2) + '~' + (obj.Data.SchoolYearEnd + '').substr(2);
                        if (@SchoolTermEnum.NextSemester.ParseToInt()== obj.Data.SchoolTerm) {
                            schoolyearterm += '@SchoolTermEnum.NextSemester.GetDescription()';
                        } else {
                            schoolyearterm += '@SchoolTermEnum.LastSemester.GetDescription()';
                        }
                        $("#SchoolYearStartTerm").val(schoolyearterm);

                        // $.each(obj.Data.ChildList, function (i, v) {
                        //     v.SourceType = v.SourceType == @SourceTypeEnum.ExperimentPlan.ParseToInt() ? "@SourceTypeEnum.ExperimentPlan.GetDescription()" : "@SourceTypeEnum.ExperimentList.GetDescription()";
                        //     v.ExperimentType = v.ExperimentType == @ExperimentTypeEnum.Demo.ParseToInt() ? "@ExperimentTypeEnum.Demo.GetDescription()" : "@ExperimentTypeEnum.Group.GetDescription()";
                        //     var html = $.Format('<div class="{0}">', obj.Data.ChildList.length > 1 ? 'div-experit' : '');
                        //     html += '<div class="form-group row">';
                        //     html += $.Format('<label class="col-sm-2 control-label ">实验来源</label><div class="col-sm-8"><input class="form-control" readonly="readonly" value="{0}" /></div>', v.SourceType);
                        //     html += '</div>';
                        //     html += '<div class="form-group row">';
                        //     html += $.Format('<label class="col-sm-2 control-label ">实验名称</label><div class="col-sm-8"><input class="form-control" readonly="readonly" value="{0}" /></div>', v.ExperimentName);
                        //     html += '</div>';
                        //     html += '<div class="form-group row">';
                        //     html += $.Format('<label class="col-sm-2 control-label ">实验类型</label><div class="col-sm-8"><input class="form-control" readonly="readonly" value="{0}" /></div>', v.ExperimentType);
                        //     html += '</div>';
                        //     if (v.ExperimentType == "@ExperimentTypeEnum.Group.GetDescription()") {
                        //         html += '<div class="form-group row">';
                        //         html += $.Format('<label class="col-sm-2 control-label ">分组数</label><div class="col-sm-8"><input class="form-control" readonly="readonly" value="{0}" /></div>', v.Groupz);
                        //         html += '</div>';
                        //     }
                        //     html += '<div class="form-group row">';
                        //     html += $.Format('<label class="col-sm-2 control-label ">所需仪器</label><div class="col-sm-8"><textarea class="form-control" readonly>{0}</textarea></div>', v.EquipmentNeed ?? '');
                        //     html += '</div>';
                        //     html += '<div class="form-group row">';
                        //     html += $.Format('<label class="col-sm-2 control-label ">所需耗材</label><div class="col-sm-8"><textarea class="form-control" readonly>{0}</textarea></div>', v.MaterialNeed ?? '');
                        //     html += '</div>';
                        //     html += ' </div>';
                        //     $('#divMore').append(html);

                        //     html = $.Format('<div class="{0}">', obj.Data.ChildList.length > 1 ? 'div-experit' : '');
                        //     if (obj.Data.ChildList.length > 1) {
                        //         html += '<div class="form-group row">';
                        //         html += $.Format('<label class="col-sm-2 control-label ">实验名称</label><div class="col-sm-8"><input class="form-control" readonly="readonly" value="{0}" /></div>', v.ExperimentName);
                        //         html += '</div>';
                        //     }
                        //     html += '<div class="form-group row">';
                        //     html += $.Format('<label class="col-sm-2 control-label ">所需仪器</label><div class="col-sm-8"><textarea class="form-control" readonly>{0}</textarea></div>', v.ArrangerEquipmentNeed ?? '');
                        //     html += '</div>';
                        //     html += '<div class="form-group row">';
                        //     html += $.Format('<label class="col-sm-2 control-label ">所需耗材</label><div class="col-sm-8"><textarea class="form-control" readonly>{0}</textarea></div>', v.ArrangerMaterialNeed ?? '');
                        //     html += '</div>';
                        //     html += ' </div>';
                        //     $('#divMoreAp').append(html);

                        //     html = $.Format('<div class="{0}">', obj.Data.ChildList.length > 1 ? 'div-experit' : '');
                        //     if (obj.Data.ChildList.length > 1) {
                        //         html += '<div class="form-group row">';
                        //         html += $.Format('<label class="col-sm-2 control-label ">实验名称</label><div class="col-sm-8"><input class="form-control" readonly="readonly" value="{0}" /></div>', v.ExperimentName);
                        //         html += '</div>';
                        //     }
                        //     html += getExperimentSumHtml(v.Id, v.EquipmentNeed, v.PlanDetailId, v.TextbookVersionDetailId, v.SchoolExperimentId);
                        //     html += ' </div>';
                        //     $('#divMoreDj').append(html);
                        // });

                        // IsRelation = parseInt(obj.Data.IsRelationCamera);
                        // if (IsRelation == 1) {
                        //     $("#divImageFile").hide();
                        // }
                        // if (obj.Data.FunRoomId == 1) {
                        //     //普通教室，不显示设备运行情况  2022-03-28 by lss
                        //     $(".run-info").hide();
                        //     $(".normal").addClass("checked");//普通教室默认运行正常
                        //     $(".abnormal").removeClass("checked");
                        // }
                    }
                }
            });
        }
    }

    function loadRunStatuz() {
        var html = '';
        html += '<label class="iradio-box"><div class="iradio-blue normal" style="vertical-align:bottom;"><input name="inputType_checkbox" type="radio" checked value="@RunStatuzEnum.Normal.ParseToInt()" style="position: absolute; opacity: 0;"></div> @RunStatuzEnum.Normal.GetDescription()</label>';
        html += '<label class="iradio-box"><div class="iradio-blue abnormal" style="vertical-align:bottom;"><input name="inputType_checkbox" type="radio" value="@RunStatuzEnum.Abnormal.ParseToInt()" style="position: absolute; opacity: 0;"></div> @RunStatuzEnum.Abnormal.GetDescription()</label>';
        $("#runStatuz").html(html);
        $(".normal").addClass("checked");

        $('input[name="inputType_checkbox"]').change(function () {
            if ($(this).prop("checked")) {
                var val = $(this).val();
                if (val == @RunStatuzEnum.Normal.ParseToInt()) {
                    $(".normal").addClass("checked");
                    $(".abnormal").removeClass("checked");
                    $('#divProblem').hide();
                } else {
                    $(".abnormal").addClass("checked");
                    $(".normal").removeClass("checked");
                    $('#divProblem').show();
                }
            }
        });
    }

    function loadUploadify() {
        $("#uploadify").uploadifive({
            'uploadScript': '/File/UploadFile?fc=@FileCategoryEnum.ExperimentSceneImg.ParseToInt()',
            'cancelImg': '~/lib/uploadifive/uploadifive-cancel.png',
            'buttonText': '上 传',
            'queueID': 'fileQueue',
            'auto': true,
            'multi': false,
            'fileDesc': '支持格式：*.jpg;*.gif;*.png;*.pdf;',
            'fileExt': '*.jpg;*.gif;*.png;*.pdf;',
            'onAddQueueItem': function (file) {

            },
            'onUploadComplete': function (fileObj, response) {
                if (typeof (response) == "string" && response != "") {
                    response = JSON.parse(response);
                    if (response.Tag == 1) {
                        ys.msgSuccess("上传成功！");
                        var id = response.Description;
                        var filePath = response.Data;
                        var fileTitle = response.Message;
                        var fileExt = filePath.substring(filePath.lastIndexOf('.')).toUpperCase();
                        if (fileExt == ".JPG" || fileExt == ".BMP" || fileExt == ".JPEG" || fileExt == ".PNG" || fileExt == ".GIF") {
                            $("#spanFile").append('<span class="keywords" idValue="' + id + '"> <a type="del" onclick="delFile(this,\'' + id + '\')"></a><a  modal="zoomImg"  href="javascript:void(0);" src="' + filePath + '" >' + fileTitle + '</a></span>');
                        } else {
                            $("#spanFile").append('<span class="keywords" idValue="' + id + '"> <a type="del" onclick="delFile(this,\'' + id + '\')"></a><a target="_blank" href="' + filePath + '" >' + fileTitle + '</a></span>');
                        }
                        imgages.showextalink();
                    } else {
                        ys.msgError(response.Message);
                    }
                }
            },
            'onFallback': function (event) {
                ys.msgError(event);
            }
        });
    }

    function saveData(index) {
        var runStatuz = $('input[name="inputType_checkbox"]:checked').val();
        var experimentSummary = $("#experimentSummary").val();
        var problemDesc = $('#problemDesc').val();

        var msg = '';
        if (experimentSummary && experimentSummary == '') {
            msg += '请填写实验结果';
        }

        if (runStatuz == @RunStatuzEnum.Abnormal.ParseToInt() && problemDesc == '') {
            msg += '设备不正常必须填写问题描述！<br />';
        }

        if (runStatuz != @RunStatuzEnum.Normal.ParseToInt() && runStatuz != @RunStatuzEnum.Abnormal.ParseToInt()) {
            msg += '请选择设备运行情况！';
        }
        if (msg != '') {
            ys.msgError(msg);
            return false;
        }
        var imageArr = [];
        if (IsRelation == 0) {
            $(".keywords").each(function () {
                var idValue = $(this).attr("idValue");
                imageArr.push(idValue);
            })
        }

        ys.confirm('您确认登记提交吗？', function () {
            var postData = {
                Id: Com_BookingId,
                RunStatuz: runStatuz,
                ProblemDesc: $('#problemDesc').val(),
                AttachmentIds: imageArr.join(',')
            }
            ys.ajax({
                url: '@Url.Content("~/ExperimentTeachManage/ExperimentCriticize/RecordJson")',
                type: 'post',
                data: { model: postData },
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        var url = '@Url.Content("~/ExperimentTeachManage/ExperimentCriticize/RecordList")';
                        createMenuAndCloseCurrent(url, "待登记实验");
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        });
    }
    //删除附件
    function delFile(obj, value) {
        $(obj).parent().remove();
        imgages.showextalink();
    }
</script>

