﻿@{
    Layout = "~/Views/Shared/_FormWhite.cshtml";
}

<div class="wrapper animated fadeInRight">
    <form id="form" class="form-horizontal m">
        <div class="form-group row">
            <span id="spContent"></span>
        </div>
    </form>
</div>

<script type="text/javascript">
    var id = ys.request("id");
    $(function () {
        getForm();
    });

    function getForm() {
        ys.ajax({
            url: '@Url.Content("~/InstrumentManage/InstrumentOutList/GetFormJson")' + '?id=' + id,
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1) {
                    $('#spContent').html(obj.Data.Content);
                }
            }
        });
    }

    function saveForm(index) {
        parent.layer.close(index);
    }
</script>

