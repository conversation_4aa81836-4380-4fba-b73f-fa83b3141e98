﻿@{
    Layout = "~/Views/Shared/_FormWhite.cshtml";
}
@inject Microsoft.AspNetCore.Hosting.IWebHostEnvironment HostingEnvironment
@section header{
    <link href="@Url.Content("~/lib/summernote/0.8.15/summernote.min.css")" rel="stylesheet" type="text/css">
    <script src='@Url.Content("~/lib/summernote/0.8.15/summernote.js")' type="text/javascript"></script>
    <script src='@Url.Content("~/lib/summernote/0.8.15/lang/summernote-zh-CN.min.js")' type="text/javascript"></script>

    @BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/imageupload/1.0/css/imgup.min.css"))
    @BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/imageupload/1.0/js/imgup.min.js"))
    @BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/bootstrap.tagsinput/0.8.0/bootstrap-tagsinput.min.css"))
    @BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/bootstrap.tagsinput/0.8.0/bootstrap-tagsinput.min.js"))
    @BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/uploadifive/File.min.css"))
    @BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/uploadifive/File.min.js"))
}
<div class="wrapper animated fadeInRight">
    <form id="form" class="form-horizontal m">

        <div id="divShowReason" class="form-group row" style="display:none;color:red;">
            <label class="col-sm-3 control-label ">不通过原因</label>
            <div class="col-sm-8">
                <textarea id="AuthSuggestion" col="AuthSuggestion" class="form-control" style="height:60px;" readonly></textarea>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">
                <span class="glyphicon glyphicon-question-sign titleprompt inputprompt" aria-hidden="true" data-container="body" data-toggle="tooltip" data-placement="right"
                      data-content="小于20字"></span>
                培训名称<font class="red"> *</font>
            </label>
            <div class="col-sm-8">
                <input id="Name" col="Name" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">培训课时（小时）<font class="red"> *</font></label>
            <div class="col-sm-8">
                <input id="SchoolHour" col="SchoolHour" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">
                <span class="glyphicon glyphicon-question-sign titleprompt inputprompt" aria-hidden="true" data-container="body" data-toggle="tooltip" data-placement="right"
                      data-content="是指培训开始日期"></span>
                培训日期<font class="red"> *</font>
            </label>
            <div class="col-sm-8">
                <input id="TrainDate" col="TrainDate" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">培训地点<font class="red"> *</font></label>
            <div class="col-sm-8">
                <input id="TrainAddresz" col="TrainAddresz" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">组织单位<font class="red"> *</font></label>
            <div class="col-sm-8">
                <input id="TrainInstitution" col="TrainInstitution" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label " id="labTrainContent">主要培训内容</label>
            <div class="col-sm-8">
                <textarea id="TrainContent" col="TrainContent" class="form-control" style="height:60px; max-width:100%"></textarea>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label " id="labRemark">备注</label>
            <div class="col-sm-8">
                <textarea id="Remark" col="Remark" class="form-control" style="height:60px; max-width:100%"></textarea>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label " id="labImage">
                <span class="glyphicon glyphicon-question-sign titleprompt inputprompt" aria-hidden="true" data-container="body" data-toggle="tooltip" data-placement="right"
                      data-content="如结业证书等扫描件，支持PDF和图片格式"></span>
                培训证书
            </label>
            <div class="col-sm-8">
                <div>
                    <input type="file" name="uploadify" id="uploadify" />
                    <div style="height: 55px;display:none;" id="fileQueue"></div>
                </div>
                &nbsp;&nbsp;
                <div id="spanFile_1100">


                </div>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label " id="lab2Image">
                <span class="glyphicon glyphicon-question-sign titleprompt inputprompt" aria-hidden="true" data-container="body" data-toggle="tooltip" data-placement="right"
                      data-content="如培训通知和培训签到等扫描件，支持PDF和图片格式"></span>
                附件
            </label>
            <div class="col-sm-8">
                <div>
                    <input type="file" name="uploadifyFile" id="uploadifyFile" />
                    <div style="height: 55px;display:none;" id="fileQueueFile"></div>
                </div>
                &nbsp;&nbsp;
                <div id="spanFile_1110">


                </div>
            </div>
        </div>


    </form>

</div>

<script type="text/javascript">
    var id = ys.request("id");
    var SaveValiDate = [];
    $(function () {
        $(".inputprompt").popover({
            trigger: 'hover',
            html: true
        });

        getForm();
        loadUploadify();
        laydate.render({ elem: '#TrainDate', format: 'yyyy-MM-dd', trigger: 'click', position: 'fixed' });
        $("#thumbImage").imageUpload({ uploadImage: 'uploadThumbImage', limit: 1, context: ctx });

        loadPageSetValid();
    });

    function loadUploadify() {
        $("#uploadify").uploadifive({
            'uploadScript': '/File/UploadFile?fc=1100',
            'cancelImg': '~/lib/uploadifive/uploadifive-cancel.png',
            'buttonText': '上 传',
            //'formData': { "token": "", "UserId": "0", "folder": "/UploadFile/Attachment" },
            'queueID': 'fileQueue',
            'auto': true,
            'multi': true,
            'fileDesc': '支持格式：*.jpg;*.gif;*.png;',
            'fileExt': '*.jpg;*.gif;*.png;',
            'onAddQueueItem': function (file) {

            },
            'onUploadComplete': function (fileObj, response) {
                if (typeof (response) == "string" && response != "") {
                    response = JSON.parse(response);
                    if (response.Tag == 1) {
                        $('#spanLink').text('上传成功，请单击保存按钮，保存文件。');
                        ys.msgSuccess("上传成功！");

                        var id = response.Description;
                        var filePath = response.Data;
                        var fileTitle = response.Message;
                        var fileExt = filePath.substring(filePath.lastIndexOf('.')).toUpperCase();
                        if (fileExt == ".JPG" || fileExt == ".BMP" || fileExt == ".JPEG" || fileExt == ".PNG" || fileExt == ".GIF") {
                            $("#spanFile_1100").append('<span class="keywords" idValue="' + id+'"> <a type="del" onclick="delFile(this,\'' + id + '\')"></a><a  modal="zoomImg"  href="javascript:void(0);" src="' + filePath + '" >' + fileTitle + '</a></span>');
                        } else {
                            $("#spanFile_1100").append('<span class="keywords" idValue="' + id +'"> <a type="del" onclick="delFile(this,\'' + id + '\')"></a><a target="_blank" href="' + filePath + '" >' + fileTitle + '</a></span>');
                        }
                        imgages.showextalink();
                    } else {
                        $('#spanLink').text(response.Message);
                        ys.msgError(response.Message);
                    }
                }
            },
            'onFallback': function (event) {
                ys.msgError(event);
            }
        });

        $("#uploadifyFile").uploadifive({
            'uploadScript': '/File/UploadFile?fc=1110',
            'cancelImg': '~/lib/uploadifive/uploadifive-cancel.png',
            'buttonText': '上 传',
            'queueID': 'fileQueueFile',
            'auto': true,
            'multi': true,
            'fileDesc': '支持格式：*.jpg;*.gif;*.png;',
            'fileExt': '*.jpg;*.gif;*.png;',
            'onAddQueueItem': function (file) {

            },
            'onUploadComplete': function (fileObj, response) {
                if (typeof (response) == "string" && response != "") {
                    response = JSON.parse(response);
                    if (response.Tag == 1) {
                        $('#spanLink').text('上传成功，请单击保存按钮，保存文件。');
                        ys.msgSuccess("上传成功！");

                        var id = response.Description;
                        var filePath = response.Data;
                        var fileTitle = response.Message;
                        var fileExt = filePath.substring(filePath.lastIndexOf('.')).toUpperCase();
                        if (fileExt == ".JPG" || fileExt == ".BMP" || fileExt == ".JPEG" || fileExt == ".PNG" || fileExt == ".GIF") {
                            $("#spanFile_1110").append('<span class="keywords" idValue="' + id+'"> <a type="del" onclick="delFile(this,\'' + id + '\')"></a><a  modal="zoomImg"  href="javascript:void(0);" src="' + filePath + '" >' + fileTitle + '</a></span>');
                        } else {
                            $("#spanFile_1110").append('<span class="keywords" idValue="' + id +'"> <a type="del" onclick="delFile(this,\'' + id + '\')"></a><a target="_blank" href="' + filePath + '" >' + fileTitle + '</a></span>');
                        }
                        imgages.showextalink();
                    } else {
                        $('#spanLink').text(response.Message);
                        ys.msgError(response.Message);
                    }
                }
            },
            'onFallback': function (event) {
                ys.msgError(event);
            }
        });
    }

    function getForm() {

        if (id > 0) {
            ys.ajax({
                url: '@Url.Content("~/PersonManage/UserTrainInfo/GetFormJson")' + '?id=' + id,
                type: 'get',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        obj.Data.TrainDate = obj.Data.TrainDate.substring(0, 10);
                        $('#form').setWebControls(obj.Data);
                        //console.log("obj:"+JSON.stringify(obj));
                        //调用显示附件信息
                        var fileList = obj.Data.Atts;
                        if (fileList.length > 0) {

                            for (var i = 0; i < fileList.length; i++) {
                                var id = fileList[i].Id;
                                var filePath = fileList[i].Path;
                                var fileExt = fileList[i].Ext.toUpperCase();
                                var fileTitle = fileList[i].Title;
                                var fileFileCategory = fileList[i].FileCategory;
                                var objDiv = $("#spanFile_" + fileFileCategory);

                                if (fileExt == ".JPG" || fileExt == ".BMP" || fileExt == ".JPEG" || fileExt == ".PNG" || fileExt == ".GIF") {
                                    objDiv.append('<span class="keywords" idValue="' + id +'"> <a type="del" onclick="delFile(this,\'' + id + '\')"></a><a  modal="zoomImg"  href="javascript:void(0);" src="' + filePath + '" >' + fileTitle + '</a></span>');
                                } else {
                                    objDiv.append('<span class="keywords" idValue="' + id +'"> <a type="del" onclick="delFile(this,\'' + id + '\')"></a><a target="_blank" href="' + filePath + '" >' + fileTitle + '</a></span>');
                                }
                            }

                            imgages.showextalink();
                        }

                        if (obj.Data.Statuz == @AuthStatuzEnum.AuthNoPass.ParseToInt()) {
                            $("#divShowReason").show();
                        }
                    }
                }
            });
        }
        else {
            var defaultData = {};
            $('#form').setWebControls(defaultData);
        }
    }

    function saveForm(index) {
        if ($('#form').validate().form()) {
            var postData = $('#form').getWebControls({ Id: id });
            //获取附件Id集合
            var objFileId = [];
            //验证
            var errorMsg = '';
            $(".keywords").each(function () {
                var idValue = $(this).attr("idValue");
                //console.log("idValue:" + idValue);
                objFileId.push(idValue);
            });
            if (objFileId.length > 0) {
                postData.ListFileId = objFileId;
            }

            if (SaveValiDate != undefined && SaveValiDate.length > 0) {
                SaveValiDate.map(function (item, i) {
                    if (item.typecode == "TrainContent") {
                        if (!(postData.TrainContent != undefined && postData.TrainContent.length > 0)) {
                            errorMsg += (item.verifyhint + '<br/>');
                        }
                    } else if (item.typecode == "Remark") {
                        if (!(postData.Remark != undefined && postData.Remark.length > 0)) {
                            errorMsg += (item.verifyhint + '<br/>');
                        }
                    } else if (item.typecode == "Image") { //培训证书
                        if (!(objFileId != undefined && objFileId.length > 0 && $("#spanFile_1100").text().trim().length > 0)) {
                            errorMsg += (item.verifyhint + '<br/>');
                        }
                    } else if (item.typecode == "2Image") { //附件
                        if (!(objFileId != undefined && objFileId.length > 0 && $("#spanFile_1110").text().trim().length > 0)) {
                            errorMsg += (item.verifyhint + '<br/>');
                        }
                    }
                });
            }

            if (errorMsg != '') {
                ys.msgError(errorMsg);
                return;
            }

            ys.ajax({
                url: '@Url.Content("~/PersonManage/UserTrainInfo/SaveFormJson")',
                type: 'post',
                data: postData,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        parent.$('#gridTable').ysTable('refresh');parent.resetToolbarStatus();
                        parent.layer.close(index);
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        }
    }

    function clearData() {
        var defaultData = {};
        $('#form').setWebControls(defaultData);
    }

    //删除附件
    function delFile(obj, value) {
        var postData = { fileId: value };
        ys.ajax({
            url: '@Url.Content("~/PersonManage/UserTrainInfo/DeleteUserTrainInfoFile")',
            type: 'post',
            data: postData,
            success: function (obj) {
                console.log("obj" + JSON.stringify(obj));
                if (obj.Tag == 1) {
                    ys.msgSuccess("删除成功");
                }
                else {
                    ys.msgError(obj.Message);
                }
            }
        });
        var vHtml = $(obj).parent().parent().html();
        var divHtml = $(obj).parent().parent().parent().html();
        var objDivId = $(obj).parent().parent().parent().find("div").attr("id");
        $(obj).parent().remove();
        imgages.showextalink();
    }

    /*获取数据库配置的填报验证*/
    function loadPageSetValid() {
        ys.ajax({
            url: '@Url.Content("~/SystemManage/ConfigSet/GetListByCode")',
            type: 'Post',
            data: { typecode:'4RYGL-1GRDA-1PXXX'},
            success: function (obj) {
                if (obj.Tag == 1) {
                    var validateRule = {};
                    var validateMessage = {};
                    validateRule.Name = { required: true, minlength: 2, maxlength: 200};
                    validateMessage.Name = { required: "请填写培训名称" };
                    validateRule.SchoolHour = { required: true, digits: true };
                    validateMessage.SchoolHour = { required: "请填写培训课时", digits:"只能输入数字"};
                    validateRule.TrainDate = { required: true};
                    validateMessage.TrainDate = { required: "请选择培训日期" };
                    validateRule.TrainAddresz = { required: true };
                    validateMessage.TrainAddresz = { required: "请填写培训地点" };
                    validateRule.TrainInstitution = { required: true };
                    validateMessage.TrainInstitution = { required: "请填写组织单位" };

                    if (obj.Data != undefined && obj.Data.length > 0) {
                      
                        for (var i = 0; i < obj.Data.length; i++) {
                            if (obj.Data[i].ConfigValue == "1") {
                                var typecode = obj.Data[i].TypeCode.replaceAll("4RYGL-1GRDA-1PXXX-", "");

                                var verifyhint = "";
                                if (obj.Data[i].VerifyHint != undefined && obj.Data[i].VerifyHint.length > 0) {
                                    verifyhint = obj.Data[i].VerifyHint;
                                }
                                $("#lab" + typecode).append('<font class="red"> *</font>');

                                if (typecode == "TrainContent") {
                                    SaveValiDate.push({ typecode: typecode, verifyhint: verifyhint });
                                } else if (typecode == "Remark") {
                                    SaveValiDate.push({ typecode: typecode, verifyhint: verifyhint });
                                } else if (typecode == "Image") {
                                    SaveValiDate.push({ typecode: typecode, verifyhint: verifyhint });
                                } else if (typecode == "2Image") {
                                    SaveValiDate.push({ typecode: typecode, verifyhint: verifyhint });
                                }
                            }
                        }
                    }
                    $('#form').validate({
                        rules: validateRule,
                        messages: validateMessage
                    });
                }
            }
        });
    }
</script>

