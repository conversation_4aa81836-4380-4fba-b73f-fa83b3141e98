﻿@{
    Layout = "~/Views/Shared/_FormWhite.cshtml";
}

<div class="wrapper animated fadeInRight">
    <form id="form" class="form-horizontal m">
        <div class="form-group row">
            <label class="col-sm-3 control-label ">「必做演示实验」考核数</label>
            <div class="col-sm-8">
                <input class="form-control" id="standardNeedShowNum" col="StandardNeedShowNum" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">「必做分组实验」考核数</label>
            <div class="col-sm-8">
                <input class="form-control" id="standardNeedGroupNum" col="StandardNeedGroupNum" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">「选做演示实验」考核数</label>
            <div class="col-sm-8">
                <input class="form-control" id="standardOptionalShowNum" col="StandardOptionalShowNum" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">「选做分组实验」考核数</label>
            <div class="col-sm-8">
                <input class="form-control" id="standardOptionalGroupNum" col="StandardOptionalGroupNum" />
            </div>
        </div>
    </form>
</div>

<script type="text/javascript">
    var ids = ys.request("ids");
    $(function () {
        $('#form').validate({
            rules: {
                standardNeedShowNum: { required: true, number: true },
                standardNeedGroupNum: { required: true, number: true },
                standardOptionalShowNum: { required: true, number: true },
                standardOptionalGroupNum: { required: true, number: true }
            }
        });
    });

    function saveForm(index) {
        if ($('#form').validate().form()) {
            if (ids == '') {
                ys.msgError('请选择需要修改的数据！');
                return false;
            }
            var postData = $('#form').getWebControls({ Ids: ids });
            ys.ajax({
                url: '@Url.Content("~/ExperimentTeachManage/TextbookVersionParameter/BatchEditParameterNum")',
                type: 'post',
                data: postData,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        parent.$('#gridTable').ysTable('refresh'); parent.resetToolbarStatus();
                        parent.layer.close(index);
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        }
    }
</script>

