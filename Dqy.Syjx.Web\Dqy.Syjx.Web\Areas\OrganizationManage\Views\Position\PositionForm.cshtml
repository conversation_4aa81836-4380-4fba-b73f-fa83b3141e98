﻿@{
    Layout = "~/Views/Shared/_FormWhite.cshtml";
}

<div class="wrapper animated fadeInRight">
    <form id="form" class="form-horizontal m">
        <div class="form-group row">
            <label class="col-sm-3 control-label">职位名称<font class="red"> *</font></label>
            <div class="col-sm-8">
                <input id="positionName" col="PositionName" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label">显示顺序</label>
            <div class="col-sm-8">
                <input id="positionSort" col="PositionSort" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label">状态</label>
            <div id="positionStatus" col="PositionStatus" class="col-sm-8"></div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label">备注</label>
            <div class="col-sm-8">
                <textarea id="remark" col="Remark" class="form-control"></textarea>
            </div>
        </div>
    </form>
</div>

<script type="text/javascript">
    var id = ys.request("id");
    $(function () {
        $("#positionStatus").ysRadioBox({
            data: ys.getJson(@Html.Raw(typeof(StatusEnum).EnumToDictionaryString()))
        });

        getForm();

        $("#form").validate({
            rules: {
                positionName: { required: true }
            }
        });
    });

    function getForm() {
        if (id > 0) {
            ys.ajax({
                url: '@Url.Content("~/OrganizationManage/Position/GetFormJson")' + '?id=' + id,
                type: "get",
                success: function (obj) {
                    if (obj.Tag == 1) {
                        var result = obj.Data;
                        $("#form").setWebControls(result);
                    }
                }
            });
        }
        else {
            ys.ajax({
                url: '@Url.Content("~/OrganizationManage/Position/GetMaxSortJson")',
                type: "get",
                success: function (obj) {
                    if (obj.Tag == 1) {
                        var defaultData = {};
                        defaultData.PositionSort = obj.Data;
                        defaultData.PositionStatus = "@StatusEnum.Yes.ParseToInt()";
                        $("#form").setWebControls(defaultData);
                    }
                }
            });
        }
    }

    function saveForm(index) {
        if ($("#form").validate().form()) {
            var postData = $("#form").getWebControls({ Id: id });
            ys.ajax({
                url: '@Url.Content("~/OrganizationManage/Position/SaveFormJson")',
                type: "post",
                data: postData,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        parent.$('#gridTable').ysTable('refresh');parent.resetToolbarStatus();
                        parent.layer.close(index);
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        }
    }
</script>
