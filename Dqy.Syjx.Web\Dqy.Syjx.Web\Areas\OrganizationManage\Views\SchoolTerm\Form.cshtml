﻿@{
    Layout = "~/Views/Shared/_FormWhite.cshtml";
}

<div class="wrapper animated fadeInRight">
    <form id="form" class="form-horizontal m">
        <div class="form-group row">
            <label class="col-sm-3 control-label ">学年起始年度<font class="red"> *</font></label>
            <div class="col-sm-8">
                <div id="SchoolYearStart" col="SchoolYearStart"></div>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">学年终止年度<font class="red"> *</font></label>
            <div class="col-sm-8">
                <div id="SchoolYearEnd" col="SchoolYearEnd"></div>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">学期<font class="red"> *</font></label>
            <div class="col-sm-8">
                <div id="SchoolTerm" col="SchoolTerm"></div>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">学期起始日<font class="red"> *</font></label>
            <div class="col-sm-8">
                <input id="TermStart" col="TermStart" type="text" class="form-control" readonly autocomplete="off" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">学期终止日<font class="red"> *</font></label>
            <div class="col-sm-8">
                <input id="TermEnd" col="TermEnd" type="text" class="form-control" readonly autocomplete="off" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">第一周开始日<font class="red"> *</font></label>
            <div class="col-sm-8">
                <input id="FirstWeekDate" col="FirstWeekDate" type="text" class="form-control" readonly autocomplete="off" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">备注</label>
            <div class="col-sm-8">
                <textarea id="Remark" col="Remark" style="max-width:100%" class="form-control" autocomplete="off"></textarea>
            </div>
        </div>
    </form>
</div>

<script type="text/javascript">
    var id = ys.request("id");
    $(function () {
        loadSchoolYear();
        $("#SchoolTerm").ysComboBox({
            data: ys.getJson(@Html.Raw(typeof(SchoolTermEnum).EnumToDictionaryString())),
            class: "form-control"
        });
        laydate.render({ elem: '#TermStart', format: 'yyyy-MM-dd', trigger: 'click', position: 'fixed'  });
        laydate.render({ elem: '#TermEnd', format: 'yyyy-MM-dd', trigger: 'click', position: 'fixed' });
        laydate.render({ elem: '#FirstWeekDate', format: 'yyyy-MM-dd', trigger: 'click', position: 'fixed' }); 
        getForm();
    });
    
    function loadSchoolYear() {
        var startArr = []; 
        var currentYear = new Date().getFullYear();
        startArr.push({ id: currentYear, name: currentYear });
        startArr.push({ id: currentYear + 1, name: currentYear + 1 });
        var endArr = [];
        endArr = endArr.concat(startArr);
        startArr.unshift({ id: (currentYear - 1), name: (currentYear - 1) });
        $('#SchoolYearStart').ysComboBox({
            data: startArr,
            class: "form-control",
            key: 'id',
            value: 'name'
        });
        $('#SchoolYearEnd').ysComboBox({
            data: endArr,
            class: "form-control",
            key: 'id',
            value: 'name'
        });
    }
    function getForm() {
        if (id > 0) {
            ys.ajax({
                url: '@Url.Content("~/OrganizationManage/SchoolTerm/GetFormJson")' + '?id=' + id,
                type: 'get',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        $('#form').setWebControls(obj.Data);
                        if (isNaN(obj.Data.TermStart) && obj.Data.TermStart.length >= 10) {
                            $("#TermStart").val(obj.Data.TermStart.substring(0, 10));
                        }
                        if (isNaN(obj.Data.TermEnd) && obj.Data.TermEnd.length >= 10) {
                            $("#TermEnd").val(obj.Data.TermEnd.substring(0, 10));
                        }
                        if (isNaN(obj.Data.FirstWeekDate) && obj.Data.FirstWeekDate.length >= 10) {
                            $("#FirstWeekDate").val(obj.Data.FirstWeekDate.substring(0, 10));
                        }
                    }
                }
            });
        }
        else {
            var defaultData = {};
            $('#form').setWebControls(defaultData);
        }
    }

    function saveForm(index) {
        if ($('#form').validate().form()) {
            var postData = $('#form').getWebControls({ Id: id });
            // /
            /**验证：
             * 1：验证必选，
             *  2：验证开始年份，和开始日期要小于结束的。
             *  3：开始日期要大于开始年份。
             */
            var errorMsg = '';
            if (!(postData.SchoolYearStart != undefined && postData.SchoolYearStart.length > 0)) {
                errorMsg += '请选择学年起始年度。<br/>';
            }
            if (!(postData.SchoolYearEnd != undefined && postData.SchoolYearEnd.length > 0)) {
                errorMsg += '请选择学年终止年度。<br/>';
            } else {
                //判断要大于开始年度。
                if (postData.SchoolYearStart > postData.SchoolYearEnd) {
                    errorMsg += '学年终止年度必须不小于学年起始年度。<br/>';
                }
            }
            if (!(postData.SchoolTerm > 0)) {
                errorMsg += '请选择学期。<br/>';
            }
            if (!(postData.TermStart != undefined && postData.TermStart.length > 0)) {
                errorMsg += '请选择学期起始日 。<br/>';
            }
            if (!(postData.TermEnd != undefined && postData.TermEnd.length > 0)) {
                errorMsg += '请选择学期终止日 。<br/>';
            }
            if (!(postData.FirstWeekDate != undefined && postData.FirstWeekDate.length > 0)) {
                errorMsg += '请选择第一周开始日 。<br/>';
            }
            if (errorMsg != '') {
                ys.msgError(errorMsg);
                return;
            }

            ys.ajax({
                url: '@Url.Content("~/OrganizationManage/SchoolTerm/SaveFormJson")',
                type: 'post',
                data: postData,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        parent.$('#gridTable').ysTable('refresh');parent.resetToolbarStatus();
                        parent.layer.close(index);
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        }
    }
</script>

