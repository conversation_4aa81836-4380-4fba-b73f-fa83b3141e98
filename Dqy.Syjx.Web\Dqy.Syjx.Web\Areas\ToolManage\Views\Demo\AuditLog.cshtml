﻿@{
    Layout = "~/Views/Shared/_Index.cshtml";
}
<div class="container-div">

    <div class="row" style="height:auto;">
        <div class="col-sm-12">
            <div class="ibox float-e-margins">
                <div class="ibox-title">
                    <h5>学校审批</h5>
                    <div class="ibox-tools">
                        <a class="collapse-link">
                            <i class="fa fa-chevron-up"></i>
                        </a>
                        <a class="close-link">
                            <i class="fa fa-times"></i>
                        </a>
                    </div>
                </div>
                <div class="ibox-content">
                    <div class="row">
                        <div class="col-sm-12" style="font-size:14px">
                            <div class="card-body">
                                <form id="fileConfigForm" class="form-horizontal m">
                                    <div class="form-group row">
                                        <div class="col-sm-4">
                                            <label class="col-sm-4 control-label ">类名前缀<font class="red"> *</font></label>
                                            <div class="col-sm-8">
                                                <input id="classPrefix" col="ClassPrefix" type="text" class="form-control" />
                                            </div>
                                        </div>
                                        <div class="col-sm-4">
                                            <label class="col-sm-4 control-label ">类名描述</label>
                                            <div class="col-sm-8">
                                                <input id="classDescription" col="ClassDescription" type="text" class="form-control" />
                                            </div>
                                        </div>
                                        <div class="col-sm-4">
                                            <label class="col-sm-4 control-label ">创建人员</label>
                                            <div class="col-sm-8">
                                                <input id="createName" col="CreateName" type="text" class="form-control" />
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <div class="col-sm-4">
                                            <label class="col-sm-4 control-label ">实体类名<font class="red"> *</font></label>
                                            <div class="col-sm-8">
                                                <input id="entityName" col="EntityName" type="text" data-suffix="Entity" readonly="readonly" class="form-control" />
                                            </div>
                                        </div>
                                        <div class="col-sm-4">
                                            <label class="col-sm-4 control-label ">映射类名<font class="red"> *</font></label>
                                            <div class="col-sm-8">
                                                <input id="entityMapName" col="EntityMapName" type="text" data-suffix="EntityMap" readonly="readonly" class="form-control" />
                                            </div>
                                        </div>
                                        <div class="col-sm-4">
                                            <label class="col-sm-4 control-label ">创建日期</label>
                                            <div class="col-sm-8">
                                                <input id="createDate" col="CreateDate" type="text" class="form-control" />
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <div class="col-sm-4">
                                            <label class="col-sm-4 control-label ">业务类名<font class="red"> *</font></label>
                                            <div class="col-sm-8">
                                                <input id="businessName" col="BusinessName" type="text" data-suffix="BLL" readonly="readonly" class="form-control" />
                                            </div>
                                        </div>
                                        <div class="col-sm-4">
                                            <label class="col-sm-4 control-label ">服务类名<font class="red"> *</font></label>
                                            <div class="col-sm-8">
                                                <input id="serviceName" col="ServiceName" type="text" data-suffix="Service" readonly="readonly" class="form-control" />
                                            </div>
                                        </div>
                                        <div class="col-sm-4">
                                            <label class="col-sm-4 control-label ">查询类名<font class="red"> *</font></label>
                                            <div class="col-sm-8">
                                                <input id="entityParamName" col="EntityParamName" type="text" data-suffix="Param" readonly="readonly" class="form-control" />
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <div class="col-sm-4">
                                            <label class="col-sm-4 control-label ">控制器名<font class="red"> *</font></label>
                                            <div class="col-sm-8">
                                                <input id="controllerName" col="ControllerName" type="text" data-suffix="Controller" readonly="readonly" class="form-control" />
                                            </div>
                                        </div>
                                        <div class="col-sm-4">
                                            <label class="col-sm-4 control-label ">列表页名<font class="red"> *</font></label>
                                            <div class="col-sm-8">
                                                <input id="pageIndexName" col="PageIndexName" type="text" data-suffix="Index" readonly="readonly" class="form-control" />
                                            </div>
                                        </div>
                                        <div class="col-sm-4">
                                            <label class="col-sm-4 control-label ">表单页名<font class="red"> *</font></label>
                                            <div class="col-sm-8">
                                                <input id="pageFormName" col="PageFormName" type="text" data-suffix="Form" readonly="readonly" class="form-control" />
                                            </div>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row" style="height:auto;">
        <div class="col-sm-12">
            <div class="ibox float-e-margins">
                <div class="ibox-title">
                    <h5>校长审批</h5>
                    <div class="ibox-tools">
                        <a class="collapse-link">
                            <i class="fa fa-chevron-up"></i>
                        </a>
                        <a class="close-link">
                            <i class="fa fa-times"></i>
                        </a>
                    </div>
                </div>
                <div class="ibox-content">
                    <div class="row">
                        <div class="col-sm-12" style="font-size:14px">
                            <div class="card-body">
                                <form id="fileConfigForm" class="form-horizontal m">
                                    <div class="form-group row">
                                        <div class="col-sm-4">
                                            <label class="col-sm-4 control-label ">类名前缀<font class="red"> *</font></label>
                                            <div class="col-sm-8">
                                                <input id="classPrefix" col="ClassPrefix" type="text" class="form-control" />
                                            </div>
                                        </div>
                                        <div class="col-sm-4">
                                            <label class="col-sm-4 control-label ">类名描述</label>
                                            <div class="col-sm-8">
                                                <input id="classDescription" col="ClassDescription" type="text" class="form-control" />
                                            </div>
                                        </div>
                                        <div class="col-sm-4">
                                            <label class="col-sm-4 control-label ">创建人员</label>
                                            <div class="col-sm-8">
                                                <input id="createName" col="CreateName" type="text" class="form-control" />
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <div class="col-sm-4">
                                            <label class="col-sm-4 control-label ">实体类名<font class="red"> *</font></label>
                                            <div class="col-sm-8">
                                                <input id="entityName" col="EntityName" type="text" data-suffix="Entity" readonly="readonly" class="form-control" />
                                            </div>
                                        </div>
                                        <div class="col-sm-4">
                                            <label class="col-sm-4 control-label ">映射类名<font class="red"> *</font></label>
                                            <div class="col-sm-8">
                                                <input id="entityMapName" col="EntityMapName" type="text" data-suffix="EntityMap" readonly="readonly" class="form-control" />
                                            </div>
                                        </div>
                                        <div class="col-sm-4">
                                            <label class="col-sm-4 control-label ">创建日期</label>
                                            <div class="col-sm-8">
                                                <input id="createDate" col="CreateDate" type="text" class="form-control" />
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <div class="col-sm-4">
                                            <label class="col-sm-4 control-label ">业务类名<font class="red"> *</font></label>
                                            <div class="col-sm-8">
                                                <input id="businessName" col="BusinessName" type="text" data-suffix="BLL" readonly="readonly" class="form-control" />
                                            </div>
                                        </div>
                                        <div class="col-sm-4">
                                            <label class="col-sm-4 control-label ">服务类名<font class="red"> *</font></label>
                                            <div class="col-sm-8">
                                                <input id="serviceName" col="ServiceName" type="text" data-suffix="Service" readonly="readonly" class="form-control" />
                                            </div>
                                        </div>
                                        <div class="col-sm-4">
                                            <label class="col-sm-4 control-label ">查询类名<font class="red"> *</font></label>
                                            <div class="col-sm-8">
                                                <input id="entityParamName" col="EntityParamName" type="text" data-suffix="Param" readonly="readonly" class="form-control" />
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <div class="col-sm-4">
                                            <label class="col-sm-4 control-label ">控制器名<font class="red"> *</font></label>
                                            <div class="col-sm-8">
                                                <input id="controllerName" col="ControllerName" type="text" data-suffix="Controller" readonly="readonly" class="form-control" />
                                            </div>
                                        </div>
                                        <div class="col-sm-4">
                                            <label class="col-sm-4 control-label ">列表页名<font class="red"> *</font></label>
                                            <div class="col-sm-8">
                                                <input id="pageIndexName" col="PageIndexName" type="text" data-suffix="Index" readonly="readonly" class="form-control" />
                                            </div>
                                        </div>
                                        <div class="col-sm-4">
                                            <label class="col-sm-4 control-label ">表单页名<font class="red"> *</font></label>
                                            <div class="col-sm-8">
                                                <input id="pageFormName" col="PageFormName" type="text" data-suffix="Form" readonly="readonly" class="form-control" />
                                            </div>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row" style="height:auto;">
        <div class="col-sm-12">
            <div class="ibox float-e-margins">
                <div class="ibox-title">
                    <h5>区县审批</h5>
                    <div class="ibox-tools">
                        <a class="collapse-link">
                            <i class="fa fa-chevron-up"></i>
                        </a>
                        <a class="close-link">
                            <i class="fa fa-times"></i>
                        </a>
                    </div>
                </div>
                <div class="ibox-content">
                    <div class="row">
                        <div class="col-sm-12" style="font-size:14px">
                            <div class="card-body">
                                <form id="fileConfigForm" class="form-horizontal m">
                                    <div class="form-group row">
                                        <div class="col-sm-4">
                                            <label class="col-sm-4 control-label ">类名前缀<font class="red"> *</font></label>
                                            <div class="col-sm-8">
                                                <input id="classPrefix" col="ClassPrefix" type="text" class="form-control" />
                                            </div>
                                        </div>
                                        <div class="col-sm-4">
                                            <label class="col-sm-4 control-label ">类名描述</label>
                                            <div class="col-sm-8">
                                                <input id="classDescription" col="ClassDescription" type="text" class="form-control" />
                                            </div>
                                        </div>
                                        <div class="col-sm-4">
                                            <label class="col-sm-4 control-label ">创建人员</label>
                                            <div class="col-sm-8">
                                                <input id="createName" col="CreateName" type="text" class="form-control" />
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <div class="col-sm-4">
                                            <label class="col-sm-4 control-label ">实体类名<font class="red"> *</font></label>
                                            <div class="col-sm-8">
                                                <input id="entityName" col="EntityName" type="text" data-suffix="Entity" readonly="readonly" class="form-control" />
                                            </div>
                                        </div>
                                        <div class="col-sm-4">
                                            <label class="col-sm-4 control-label ">映射类名<font class="red"> *</font></label>
                                            <div class="col-sm-8">
                                                <input id="entityMapName" col="EntityMapName" type="text" data-suffix="EntityMap" readonly="readonly" class="form-control" />
                                            </div>
                                        </div>
                                        <div class="col-sm-4">
                                            <label class="col-sm-4 control-label ">创建日期</label>
                                            <div class="col-sm-8">
                                                <input id="createDate" col="CreateDate" type="text" class="form-control" />
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <div class="col-sm-4">
                                            <label class="col-sm-4 control-label ">业务类名<font class="red"> *</font></label>
                                            <div class="col-sm-8">
                                                <input id="businessName" col="BusinessName" type="text" data-suffix="BLL" readonly="readonly" class="form-control" />
                                            </div>
                                        </div>
                                        <div class="col-sm-4">
                                            <label class="col-sm-4 control-label ">服务类名<font class="red"> *</font></label>
                                            <div class="col-sm-8">
                                                <input id="serviceName" col="ServiceName" type="text" data-suffix="Service" readonly="readonly" class="form-control" />
                                            </div>
                                        </div>
                                        <div class="col-sm-4">
                                            <label class="col-sm-4 control-label ">查询类名<font class="red"> *</font></label>
                                            <div class="col-sm-8">
                                                <input id="entityParamName" col="EntityParamName" type="text" data-suffix="Param" readonly="readonly" class="form-control" />
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <div class="col-sm-4">
                                            <label class="col-sm-4 control-label ">控制器名<font class="red"> *</font></label>
                                            <div class="col-sm-8">
                                                <input id="controllerName" col="ControllerName" type="text" data-suffix="Controller" readonly="readonly" class="form-control" />
                                            </div>
                                        </div>
                                        <div class="col-sm-4">
                                            <label class="col-sm-4 control-label ">列表页名<font class="red"> *</font></label>
                                            <div class="col-sm-8">
                                                <input id="pageIndexName" col="PageIndexName" type="text" data-suffix="Index" readonly="readonly" class="form-control" />
                                            </div>
                                        </div>
                                        <div class="col-sm-4">
                                            <label class="col-sm-4 control-label ">表单页名<font class="red"> *</font></label>
                                            <div class="col-sm-8">
                                                <input id="pageFormName" col="PageFormName" type="text" data-suffix="Form" readonly="readonly" class="form-control" />
                                            </div>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>


    <div class="card " style="margin-bottom:5px">
        <div class="card-heading">
            <h3 class="card-title">
                财务处审批
            </h3>
        </div>
        <div class="card-body">
            <form id="fileConfigForm" class="form-horizontal m">
                <div class="form-group row">
                    <div class="col-sm-4">
                        <label class="col-sm-4 control-label ">类名前缀<font class="red"> *</font></label>
                        <div class="col-sm-8">
                            <input id="classPrefix" col="ClassPrefix" type="text" class="form-control" />
                        </div>
                    </div>
                    <div class="col-sm-4">
                        <label class="col-sm-4 control-label ">类名描述</label>
                        <div class="col-sm-8">
                            <input id="classDescription" col="ClassDescription" type="text" class="form-control" />
                        </div>
                    </div>
                    <div class="col-sm-4">
                        <label class="col-sm-4 control-label ">创建人员</label>
                        <div class="col-sm-8">
                            <input id="createName" col="CreateName" type="text" class="form-control" />
                        </div>
                    </div>
                </div>
                <div class="form-group row">
                    <div class="col-sm-4">
                        <label class="col-sm-4 control-label ">实体类名<font class="red"> *</font></label>
                        <div class="col-sm-8">
                            <input id="entityName" col="EntityName" type="text" data-suffix="Entity" readonly="readonly" class="form-control" />
                        </div>
                    </div>
                    <div class="col-sm-4">
                        <label class="col-sm-4 control-label ">映射类名<font class="red"> *</font></label>
                        <div class="col-sm-8">
                            <input id="entityMapName" col="EntityMapName" type="text" data-suffix="EntityMap" readonly="readonly" class="form-control" />
                        </div>
                    </div>
                    <div class="col-sm-4">
                        <label class="col-sm-4 control-label ">创建日期</label>
                        <div class="col-sm-8">
                            <input id="createDate" col="CreateDate" type="text" class="form-control" />
                        </div>
                    </div>
                </div>
                <div class="form-group row">
                    <div class="col-sm-4">
                        <label class="col-sm-4 control-label ">业务类名<font class="red"> *</font></label>
                        <div class="col-sm-8">
                            <input id="businessName" col="BusinessName" type="text" data-suffix="BLL" readonly="readonly" class="form-control" />
                        </div>
                    </div>
                    <div class="col-sm-4">
                        <label class="col-sm-4 control-label ">服务类名<font class="red"> *</font></label>
                        <div class="col-sm-8">
                            <input id="serviceName" col="ServiceName" type="text" data-suffix="Service" readonly="readonly" class="form-control" />
                        </div>
                    </div>
                    <div class="col-sm-4">
                        <label class="col-sm-4 control-label ">查询类名<font class="red"> *</font></label>
                        <div class="col-sm-8">
                            <input id="entityParamName" col="EntityParamName" type="text" data-suffix="Param" readonly="readonly" class="form-control" />
                        </div>
                    </div>
                </div>
                <div class="form-group row">
                    <div class="col-sm-4">
                        <label class="col-sm-4 control-label ">控制器名<font class="red"> *</font></label>
                        <div class="col-sm-8">
                            <input id="controllerName" col="ControllerName" type="text" data-suffix="Controller" readonly="readonly" class="form-control" />
                        </div>
                    </div>
                    <div class="col-sm-4">
                        <label class="col-sm-4 control-label ">列表页名<font class="red"> *</font></label>
                        <div class="col-sm-8">
                            <input id="pageIndexName" col="PageIndexName" type="text" data-suffix="Index" readonly="readonly" class="form-control" />
                        </div>
                    </div>
                    <div class="col-sm-4">
                        <label class="col-sm-4 control-label ">表单页名<font class="red"> *</font></label>
                        <div class="col-sm-8">
                            <input id="pageFormName" col="PageFormName" type="text" data-suffix="Form" readonly="readonly" class="form-control" />
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <div class="row">
        @*<div id="searchDiv" class="col-sm-12 search-collapse">
            </div>*@
        <div class="btn-group-sm hidden-xs" id="toolbar">
            @*<a id="btnAdd" class="btn btn-success" onclick="showSaveForm(true)"><i class="fa fa-plus"></i> 新增</a>
                <a id="btnEdit" class="btn btn-primary disabled" onclick="showSaveForm(false)"><i class="fa fa-edit"></i> 修改</a>
                <a id="btnDelete" class="btn btn-danger disabled" onclick="deleteForm()"><i class="fa fa-remove"></i> 删除</a>*@
        </div>
        <div class="col-sm-12 select-table table-striped">

            <div class="ibox-title">
                <h5>单位管理</h5>
            </div>

            <div class="card-body">
                <form id="fileConfigForm" class="form-horizontal m">
                    <div class="form-group row">
                        <div class="col-sm-4">
                            <label class="col-sm-4 control-label ">类名前缀<font class="red"> *</font></label>
                            <div class="col-sm-8">
                                <input id="classPrefix" col="ClassPrefix" type="text" class="form-control" />
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <label class="col-sm-4 control-label ">类名描述</label>
                            <div class="col-sm-8">
                                <input id="classDescription" col="ClassDescription" type="text" class="form-control" />
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <label class="col-sm-4 control-label ">创建人员</label>
                            <div class="col-sm-8">
                                <input id="createName" col="CreateName" type="text" class="form-control" />
                            </div>
                        </div>
                    </div>
                    <div class="form-group row">
                        <div class="col-sm-4">
                            <label class="col-sm-4 control-label ">实体类名<font class="red"> *</font></label>
                            <div class="col-sm-8">
                                <input id="entityName" col="EntityName" type="text" data-suffix="Entity" readonly="readonly" class="form-control" />
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <label class="col-sm-4 control-label ">映射类名<font class="red"> *</font></label>
                            <div class="col-sm-8">
                                <input id="entityMapName" col="EntityMapName" type="text" data-suffix="EntityMap" readonly="readonly" class="form-control" />
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <label class="col-sm-4 control-label ">创建日期</label>
                            <div class="col-sm-8">
                                <input id="createDate" col="CreateDate" type="text" class="form-control" />
                            </div>
                        </div>
                    </div>
                    <div class="form-group row">
                        <div class="col-sm-4">
                            <label class="col-sm-4 control-label ">业务类名<font class="red"> *</font></label>
                            <div class="col-sm-8">
                                <input id="businessName" col="BusinessName" type="text" data-suffix="BLL" readonly="readonly" class="form-control" />
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <label class="col-sm-4 control-label ">服务类名<font class="red"> *</font></label>
                            <div class="col-sm-8">
                                <input id="serviceName" col="ServiceName" type="text" data-suffix="Service" readonly="readonly" class="form-control" />
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <label class="col-sm-4 control-label ">查询类名<font class="red"> *</font></label>
                            <div class="col-sm-8">
                                <input id="entityParamName" col="EntityParamName" type="text" data-suffix="Param" readonly="readonly" class="form-control" />
                            </div>
                        </div>
                    </div>
                    <div class="form-group row">
                        <div class="col-sm-4">
                            <label class="col-sm-4 control-label ">控制器名<font class="red"> *</font></label>
                            <div class="col-sm-8">
                                <input id="controllerName" col="ControllerName" type="text" data-suffix="Controller" readonly="readonly" class="form-control" />
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <label class="col-sm-4 control-label ">列表页名<font class="red"> *</font></label>
                            <div class="col-sm-8">
                                <input id="pageIndexName" col="PageIndexName" type="text" data-suffix="Index" readonly="readonly" class="form-control" />
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <label class="col-sm-4 control-label ">表单页名<font class="red"> *</font></label>
                            <div class="col-sm-8">
                                <input id="pageFormName" col="PageFormName" type="text" data-suffix="Form" readonly="readonly" class="form-control" />
                            </div>
                        </div>
                    </div>
                </form>
            </div>

            <div id="step-1" class="div-step">
                <div class="div-step-container">
                    <div class="card " style="margin-bottom:5px">
                        <div class="card-heading">
                            <h3 class="card-title">
                                文件名配置
                            </h3>
                        </div>
                        <div class="card-body">
                            <form id="fileConfigForm" class="form-horizontal m">
                                <div class="form-group row">
                                    <div class="col-sm-4">
                                        <label class="col-sm-4 control-label ">类名前缀<font class="red"> *</font></label>
                                        <div class="col-sm-8">
                                            <input id="classPrefix" col="ClassPrefix" type="text" class="form-control" />
                                        </div>
                                    </div>
                                    <div class="col-sm-4">
                                        <label class="col-sm-4 control-label ">类名描述</label>
                                        <div class="col-sm-8">
                                            <input id="classDescription" col="ClassDescription" type="text" class="form-control" />
                                        </div>
                                    </div>
                                    <div class="col-sm-4">
                                        <label class="col-sm-4 control-label ">创建人员</label>
                                        <div class="col-sm-8">
                                            <input id="createName" col="CreateName" type="text" class="form-control" />
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <div class="col-sm-4">
                                        <label class="col-sm-4 control-label ">实体类名<font class="red"> *</font></label>
                                        <div class="col-sm-8">
                                            <input id="entityName" col="EntityName" type="text" data-suffix="Entity" readonly="readonly" class="form-control" />
                                        </div>
                                    </div>
                                    <div class="col-sm-4">
                                        <label class="col-sm-4 control-label ">映射类名<font class="red"> *</font></label>
                                        <div class="col-sm-8">
                                            <input id="entityMapName" col="EntityMapName" type="text" data-suffix="EntityMap" readonly="readonly" class="form-control" />
                                        </div>
                                    </div>
                                    <div class="col-sm-4">
                                        <label class="col-sm-4 control-label ">创建日期</label>
                                        <div class="col-sm-8">
                                            <input id="createDate" col="CreateDate" type="text" class="form-control" />
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <div class="col-sm-4">
                                        <label class="col-sm-4 control-label ">业务类名<font class="red"> *</font></label>
                                        <div class="col-sm-8">
                                            <input id="businessName" col="BusinessName" type="text" data-suffix="BLL" readonly="readonly" class="form-control" />
                                        </div>
                                    </div>
                                    <div class="col-sm-4">
                                        <label class="col-sm-4 control-label ">服务类名<font class="red"> *</font></label>
                                        <div class="col-sm-8">
                                            <input id="serviceName" col="ServiceName" type="text" data-suffix="Service" readonly="readonly" class="form-control" />
                                        </div>
                                    </div>
                                    <div class="col-sm-4">
                                        <label class="col-sm-4 control-label ">查询类名<font class="red"> *</font></label>
                                        <div class="col-sm-8">
                                            <input id="entityParamName" col="EntityParamName" type="text" data-suffix="Param" readonly="readonly" class="form-control" />
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <div class="col-sm-4">
                                        <label class="col-sm-4 control-label ">控制器名<font class="red"> *</font></label>
                                        <div class="col-sm-8">
                                            <input id="controllerName" col="ControllerName" type="text" data-suffix="Controller" readonly="readonly" class="form-control" />
                                        </div>
                                    </div>
                                    <div class="col-sm-4">
                                        <label class="col-sm-4 control-label ">列表页名<font class="red"> *</font></label>
                                        <div class="col-sm-8">
                                            <input id="pageIndexName" col="PageIndexName" type="text" data-suffix="Index" readonly="readonly" class="form-control" />
                                        </div>
                                    </div>
                                    <div class="col-sm-4">
                                        <label class="col-sm-4 control-label ">表单页名<font class="red"> *</font></label>
                                        <div class="col-sm-8">
                                            <input id="pageFormName" col="PageFormName" type="text" data-suffix="Form" readonly="readonly" class="form-control" />
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>

                    <div class="card " style="margin-bottom:0px">
                        <div class="card-heading">
                            <h3 class="card-title">
                                输出目录
                            </h3>
                        </div>
                        <div class="card-body">
                            <form id="outputConfigForm" class="form-horizontal m">
                                <div class="form-group row">
                                    <label class="col-sm-2 control-label ">输出到所在模块<font class="red"> *</font></label>
                                    <div class="col-sm-10">
                                        <div id="outputModule" col="OutputModule"></div>
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <label class="col-sm-2 control-label ">实体层输出目录<font class="red"> *</font></label>
                                    <div class="col-sm-10">
                                        <input id="outputEntity" col="OutputEntity" type="text" readonly="readonly" class="form-control" />
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <label class="col-sm-2 control-label ">业务层输出目录<font class="red"> *</font></label>
                                    <div class="col-sm-10">
                                        <input id="outputBusiness" col="OutputBusiness" type="text" readonly="readonly" class="form-control" />
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <label class="col-sm-2 control-label ">应用层输出目录<font class="red"> *</font></label>
                                    <div class="col-sm-10">
                                        <input id="outputWeb" col="OutputWeb" type="text" readonly="readonly" class="form-control" />
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
    $(function () {

        $(".modal").appendTo("body"), $("[data-toggle=popover]").popover(), $(".collapse-link").click(function () {
            var div_ibox = $(this).closest("div.ibox"),
                e = $(this).find("i"),
                i = div_ibox.find("div.ibox-content");
            i.slideToggle(200),
                e.toggleClass("fa-chevron-up").toggleClass("fa-chevron-down"),
                div_ibox.toggleClass("").toggleClass("border-bottom"),
                setTimeout(function () {
                    div_ibox.resize();
                }, 50);
        }), $(".close-link").click(function () {
            var div_ibox = $(this).closest("div.ibox");
            div_ibox.remove()
        });

        $('#pay-qrcode').click(function () {
            var html = $(this).html();
            ys.openDialogContent({
                content: html,
                width: '600px',
                height: '321px'
            });
        });

    });

</script>