﻿@{
    Layout = "~/Views/Shared/_FormWhite.cshtml";
}

<div class="wrapper animated fadeInRight">
    <form id="form" class="form-horizontal m">
        @*<div class="form-group row">
            <label class="col-sm-3 control-label ">学段</label>
            <div class="col-sm-8">
                <div id="SchoolStage" col="SchoolStage"></div>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">学科</label>
            <div class="col-sm-8">
                <div id="DictionaryId1005" col="DictionaryId1005"></div>
            </div>
        </div>*@
        <div class="form-group row" id="divStandard">
            <label class="col-sm-3 control-label ">版本名称<font class="red"> *</font></label>
            <div class="col-sm-8">
                <div id="EvaluateStandardId" col="EvaluateStandardId"></div>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">轨数起</label>
            <div class="col-sm-8">
                <input id="RailStart" col="RailStart" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">轨数止</label>
            <div class="col-sm-8">
                <input id="RailEnd" col="RailEnd" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">加权系数（X）</label>
            <div class="col-sm-8">
                <input id="WeightedRatio" col="WeightedRatio" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">备注</label>
            <div class="col-sm-8">
                <textarea id="Remark" col="Remark" style="max-width:100%" class="form-control"></textarea>
            </div>
        </div>
    </form>
    <input type="hidden" id="hidSubjectId" value="" />
</div>

<script type="text/javascript">
    var id = ys.request("id");
    $(function () {

        /**加载版本号*/
        if(id == 0){
            $("#divStandard").show();
            loadSelectVersion();
        }else{
            $("#divStandard").hide();
        }
        
        getForm();

        $('#form').validate({
            rules: {
                RailStart: { required: true, number: true },
                RailEnd: { required: true, number: true },
                weightedRatio: { required: true, number: true }
            }
        });
    });
    function loadSelectVersion() {
        $("#EvaluateStandardId").ysComboBox({
            url: '@Url.Content("~/EvaluateManage/InstrumentEvaluateStandard/GetSelectListJson")',
            class: 'form-control',
            key: 'Id',
            value: 'SelectVersionName'
        });
    }
    function loadSubject(selectid) {
        if (selectid > 0) {
            ys.ajax({
                url: '@Url.Content("~/SystemManage/StaticDictionary/GetChildToListJson")' + '?TypeCode=1005&Pid=' + selectid,
                type: "get",
                success: function (obj) {
                    if (obj.Tag == 1) {
                        loadDictionary1005Data(obj.Data);
                    }
                }
            });
        } else {
            loadDictionary1005Data([]);
        }
    }
    function loadDictionary1005Data(data) {
        $("#DictionaryId1005").ysComboBox({
            data: data,
            class: 'form-control',
            key: 'DictionaryId',
            value: 'DicName'
        });
        if (id > 0 && data.length > 0) {
            var subjectid = $("#hidSubjectId").val();
            if (subjectid != undefined && parseFloat(subjectid) > 0) {
                $("#DictionaryId1005").ysComboBox('setValue', subjectid);
            }
        }
    }
    function getForm() {
        if (id > 0) {
            ys.ajax({
                url: '@Url.Content("~/EvaluateManage/InstrumentEvaluateWeighted/GetFormJson")' + '?id=' + id,
                type: 'get',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        $('#form').setWebControls(obj.Data);
                    }
                }
            });
        }
        else {
            var defaultData = {};
            $('#form').setWebControls(defaultData);
        }
    }

    function saveForm(index) {
        if ($('#form').validate().form()) {
            var postData = $('#form').getWebControls({ Id: id });
            var errorMsg = '';
            if (!(postData.EvaluateStandardId > 0)) {
                errorMsg += "请选择版本名称。<br/>";
            }
            if (postData.RailStart<=0) {
                errorMsg += "轨数起的值必须在0至100之间。<br/>";
            }
            if (postData.RailEnd <= 0) {
                errorMsg += "轨数止的值必须在0至100之间。<br/>";
            }
            if (parseInt(postData.RailStart) >= parseInt(postData.RailEnd)) {
                errorMsg += "轨数起必须小于轨数止的值。<br/>";
            }
            if (errorMsg != '') {
                ys.msgError('验证失败，请填写完成再提交！<br/>' + errorMsg);
                return;
            }
            ys.ajax({
                url: '@Url.Content("~/EvaluateManage/InstrumentEvaluateWeighted/SaveFormJson")',
                type: 'post',
                data: postData,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        parent.$('#gridTable').ysTable('refresh');parent.resetToolbarStatus();
                        parent.layer.close(index);
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        }
    }
</script>

