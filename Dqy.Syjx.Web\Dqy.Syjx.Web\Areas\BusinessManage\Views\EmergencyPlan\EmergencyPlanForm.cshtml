﻿@{
    Layout = "~/Views/Shared/_FormWhite.cshtml";
}

@inject Microsoft.AspNetCore.Hosting.IWebHostEnvironment HostingEnvironment
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/uploadifive/File.min.css"))
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/uploadifive/File.min.js"))

<div class="wrapper animated fadeInRight">
    <form id="form" class="form-horizontal m">
        <div class="form-group row">
            <label class="col-sm-3 control-label ">事故演练项目名称：<font class="red"> *</font></label>
            <div class="col-sm-7">
                <input id="name" col="Name" type="text" class="form-control" />
            </div>
            <div class="col-sm-2">
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">实施负责人：<font class="red"> *</font></label>
            <div class="col-sm-7">
                <input id="personCharge" col="PersonCharge" type="text" class="form-control" />
            </div>
            <div class="col-sm-2">
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">实施地点：<font class="red"> *</font></label>
            <div class="col-sm-7">
                <input id="address" col="Address" type="text" class="form-control" />
            </div>
            <div class="col-sm-2">
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">实施时间：<font class="red"> *</font></label>
            <div class="col-sm-7">
                <input id="effectiveDate" col="EffectiveDate" type="text" class="form-control" />
            </div>
            <div class="col-sm-2">
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">演练对象：<font class="red"> *</font></label>
            <div class="col-sm-7">
                <div id="trainees" col="Trainees"></div>
            </div>
            <div class="col-sm-2">
            </div>
        </div>

        <div class="form-group row">
            <label class="col-sm-3 control-label ">
                <span class="glyphicon glyphicon-question-sign titleprompt inputprompt" aria-hidden="true" data-container="body" data-toggle="tooltip" data-placement="right"
                      data-content="文件小于5M，支持pdf"></span>
                事故应急预案内容：
            </label>
            <div class="col-sm-9">
                <div style="float:left;">
                    <input type="file" name="uploadifyContent" id="uploadifyContent" />
                    <div style="height: 55px;display:none;" id="fileQueueContent"></div>
                </div>
                &nbsp;&nbsp;
                <div id="spanFile_1501" style="float: left; padding-left: 10px;">
                </div>
            </div>
        </div>

        <div class="form-group row">
            <label class="col-sm-3 control-label ">
                <span class="glyphicon glyphicon-question-sign titleprompt inputprompt" aria-hidden="true" data-container="body" data-toggle="tooltip" data-placement="right"
                      data-content="文件小于5M，支持pdf和图片文件"></span>
                演练现场照片：
            </label>
            <div class="col-sm-9">
                <div style="float:left;">
                    <input type="file" name="uploadifyScene" id="uploadifyScene" />
                    <div style="height: 55px;display:none;" id="fileQueueScene"></div>
                </div>
                &nbsp;&nbsp;
                <div id="spanFile_1502" style="float: left; padding-left: 10px;">
                </div>
            </div>
        </div>
    </form>
</div>

<script type="text/javascript">
    var id = ys.request("id");
    $(function () {

        $(".inputprompt").popover({
            trigger: 'hover',
            html: true
        });

         $("#trainees").ysComboBox({
            data: ys.getJson(@Html.Raw(typeof(TrainingObject).EnumToDictionaryString())),
            class: 'form-control'
        });
        laydate.render({ elem: '#effectiveDate', format: 'yyyy-MM-dd', trigger: 'click', position: 'fixed' });

        $("#uploadifyContent").uploadifive({
            'uploadScript': '/File/UploadFile?fc=1501',
            'cancelImg': '~/lib/uploadifive/uploadifive-cancel.png',
            'buttonText': '上 传',
            'queueID': 'fileQueueContent',
            'auto': true,
            'multi': true,
            'fileDesc': '支持格式：*.jpg;*.gif;*.png;',
            'fileExt': '*.jpg;*.gif;*.png;',
            'onAddQueueItem': function (file) {

            },
            'onUploadComplete': function (fileObj, response) {
                if (typeof (response) == "string" && response != "") {
                    response = JSON.parse(response);
                    if (response.Tag == 1) {

                        $('#spanLinkManager').text('上传成功，请单击保存按钮，保存文件。');
                        ys.msgSuccess("上传成功！");

                        var id = response.Description;
                        var filePath = response.Data;
                        var fileTitle = response.Message;
                        var fileExt = filePath.substring(filePath.lastIndexOf('.')).toUpperCase();
                        if (fileExt == ".JPG" || fileExt == ".BMP" || fileExt == ".JPEG" || fileExt == ".PNG" || fileExt == ".GIF") {
                            $("#spanFile_1501").append('<span class="keywords" idValue="' + id+'"> <a type="del" onclick="delFile(this,\'' + id + '\')"></a><a  modal="zoomImg"  href="javascript:void(0);" src="' + filePath + '" >' + fileTitle + '</a></span>');
                        } else {
                            $("#spanFile_1501").append('<span class="keywords" idValue="' + id +'"> <a type="del" onclick="delFile(this,\'' + id + '\')"></a><a target="_blank" href="' + filePath + '" >' + fileTitle + '</a></span>');
                        }
                        imgages.showextalink();
                    } else {
                        $('#spanLinkManager').text(response.Message);
                        ys.msgError(response.Message);
                    }
                }
            },
            'onFallback': function (event) {
                ys.msgError(event);
            }
        });

        $("#uploadifyScene").uploadifive({
            'uploadScript': '/File/UploadFile?fc=1502',
            'cancelImg': '~/lib/uploadifive/uploadifive-cancel.png',
            'buttonText': '上 传',
            'queueID': 'fileQueueScene',
            'auto': true,
            'multi': true,
            'fileDesc': '支持格式：*.jpg;*.gif;*.png;',
            'fileExt': '*.jpg;*.gif;*.png;',
            'onAddQueueItem': function (file) {

            },
            'onUploadComplete': function (fileObj, response) {
                if (typeof (response) == "string" && response != "") {
                    response = JSON.parse(response);
                    if (response.Tag == 1) {

                        $('#spanLinkExamine').text('上传成功，请单击保存按钮，保存文件。');
                        ys.msgSuccess("上传成功！");

                        var id = response.Description;
                        var filePath = response.Data;
                        var fileTitle = response.Message;
                        var fileExt = filePath.substring(filePath.lastIndexOf('.')).toUpperCase();
                        if (fileExt == ".JPG" || fileExt == ".BMP" || fileExt == ".JPEG" || fileExt == ".PNG" || fileExt == ".GIF") {
                            $("#spanFile_1502").append('<span class="keywords" idValue="' + id+'"> <a type="del" onclick="delFile(this,\'' + id + '\')"></a><a  modal="zoomImg"  href="javascript:void(0);" src="' + filePath + '" >' + fileTitle + '</a></span>');
                        } else {
                            $("#spanFile_1502").append('<span class="keywords" idValue="' + id +'"> <a type="del" onclick="delFile(this,\'' + id + '\')"></a><a target="_blank" href="' + filePath + '" >' + fileTitle + '</a></span>');
                        }
                        imgages.showextalink();
                    } else {
                        $('#spanLinkExamine').text(response.Message);
                        ys.msgError(response.Message);
                    }
                }
            },
            'onFallback': function (event) {
                ys.msgError(event);
            }
        });

        getForm();

        $('#form').validate({
            rules: {
                name: { required: true },
                personCharge: { required: true },
                address: { required: true },
                effectiveDate: { required: true },
            }
        });
    });

    function getForm() {
        if (id > 0) {
            ys.ajax({
                url: '@Url.Content("~/BusinessManage/EmergencyPlan/GetFormJson")' + '?id=' + id,
                type: 'get',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        $('#form').setWebControls(obj.Data);

                        var EffectiveDate = ys.formatDate(obj.Data.EffectiveDate, "yyyy-MM-dd");
                        $("#effectiveDate").val(EffectiveDate);

                        //调用显示附件信息
                        var fileList = obj.Data.Atts;
                        if (fileList.length > 0) {
                            for (var i = 0; i < fileList.length; i++) {
                                var id = fileList[i].Id;
                                var filePath = fileList[i].Path;
                                var fileExt = fileList[i].Ext.toUpperCase();
                                var fileTitle = fileList[i].Title;
                                var fileFileCategory = fileList[i].FileCategory;
                                var objDiv = $("#spanFile_" + fileFileCategory);
                                if (fileExt == ".JPG" || fileExt == ".BMP" || fileExt == ".JPEG" || fileExt == ".PNG" || fileExt == ".GIF") {
                                    objDiv.append('<span class="keywords" idValue="' + id + '"> <a type="del" onclick="delFile(this,\'' + id + '\')"></a><a  modal="zoomImg"  href="javascript:void(0);" src="' + filePath + '" >' + fileTitle + '</a></span>');
                                } else {
                                    objDiv.append('<span class="keywords" idValue="' + id + '"> <a type="del" onclick="delFile(this,\'' + id + '\')"></a><a target="_blank" href="' + filePath + '" >' + fileTitle + '</a></span>');
                                }
                            }
                            imgages.showextalink();
                        }
                    }
                }
            });
        }
        else {
            var defaultData = {};
            $('#form').setWebControls(defaultData);
        }
    }

    function saveForm(index) {
        if ($('#form').validate().form()) {
            var postData = $('#form').getWebControls({ Id: id });

            var trainees = postData.Trainees;
            if (trainees == "") {
                ys.msgError("请选择演练对象");
                return;
            }

            //获取附件Id集合
            var objFileId = [];
            $(".keywords").each(function () {
                var idValue = $(this).attr("idValue");
                objFileId.push(idValue);
            });
            if (objFileId.length > 0) {
                postData.ListFileId = objFileId;
            }

            ys.ajax({
                url: '@Url.Content("~/BusinessManage/EmergencyPlan/SaveFormJson")',
                type: 'post',
                data: postData,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        parent.$('#gridTable').ysTable('refresh');parent.resetToolbarStatus();
                        parent.layer.close(index);
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        }
    }

     //删除附件
    function delFile(obj, value) {
        var postData = { fileId: value };
        ys.ajax({
            url: '@Url.Content("~/PersonManage/UserTrainInfo/DeleteUserTrainInfoFile")',
            type: 'post',
            data: postData,
            success: function (obj) {
                //console.log("obj" + JSON.stringify(obj));
                if (obj.Tag == 1) {
                    ys.msgSuccess("删除成功");
                }
                else {
                    ys.msgError(obj.Message);
                }
            }
        });
        var vHtml = $(obj).parent().parent().html();
        var divHtml = $(obj).parent().parent().parent().html();
        var objDivId = $(obj).parent().parent().parent().find("div").attr("id");
        $(obj).parent().remove();
        imgages.showextalink();
    }
</script>

