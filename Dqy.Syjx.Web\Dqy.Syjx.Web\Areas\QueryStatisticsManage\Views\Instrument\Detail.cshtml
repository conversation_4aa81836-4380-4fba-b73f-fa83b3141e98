﻿
@{
    Layout = "~/Views/Shared/_Index.cshtml";
}
@inject Microsoft.AspNetCore.Hosting.IWebHostEnvironment HostingEnvironment
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/fileinput/5.0.3/css/fileinput.min.css"))
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/fileinput/5.0.3/js/fileinput.min.js"))
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/uploadifive/File.min.css"))
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/uploadifive/File.min.js"))

<div class="wrapper animated fadeInRight">
    <div class="btn-group-sm hidden-xs" id="toolbar">
    </div>
    <div class="col-sm-12 select-table table-striped">
        <div class="card-body">
            <form id="form" class="form-horizontal m">

                <div class="form-group row">
                    <label class="col-sm-2 control-label ">适用学段</label>
                    <div class="col-sm-4">
                        <input id="Stage" col="Stage" type="text" class="form-control" readonly />
                    </div>
                    <label class="col-sm-2 control-label ">适用学科</label>
                    <div class="col-sm-4">
                        <input id="Course" col="Course" type="text" class="form-control" readonly />
                    </div>
                </div>


                <div class="form-group row">
                    <label class="col-sm-2 control-label ">分类代码</label>
                    <div class="col-sm-4">
                        <input id="code" col="Code" type="text" class="form-control" readonly />
                    </div>

                    <label class="col-sm-2 control-label ">仪器名称</label>
                    <div class="col-sm-4">
                        <input id="name" col="Name" type="text" class="form-control" readonly />
                    </div>
                    
                </div>

                <div class="form-group row">
                    <label class="col-sm-2 control-label ">规格属性</label>
                    <div class="col-sm-10">
                        @*<input id="Model" col="Model" type="text" class="form-control" readonly />*@
                        <textarea id="Model" col="Model" type="text" class="form-control" style="max-width:100%" readonly></textarea>
                    </div>
                </div>

                <div class="form-group row">
                    <label class="col-sm-2 control-label ">数量</label>
                    <div class="col-sm-4">
                        <input id="Num" col="Num" type="text" class="form-control" readonly />
                    </div>
                    <label class="col-sm-2 control-label ">单位</label>
                    <div class="col-sm-4">
                        <input id="UnitName" col="UnitName" type="text" class="form-control" readonly />
                    </div>
                </div>

                <div class="form-group row">
                    <label class="col-sm-2 control-label ">单价（元）</label>
                    <div class="col-sm-4">
                        <input id="Price" col="Price" type="text" class="form-control" readonly />
                    </div>
                    <label class="col-sm-2 control-label ">金额</label>
                    <div class="col-sm-4">
                        <input id="SumMoney" col="SumMoney" type="text" class="form-control" readonly />
                    </div>
                </div>

                <div class="form-group row">
                    <label class="col-sm-2 control-label ">仪器属性</label>
                    <div class="col-sm-4">
                        <input id="VarietyAttributeName" col="VarietyAttributeName" type="text" class="form-control" readonly />
                    </div>
                    <label class="col-sm-2 control-label ">存放地</label>
                    <div class="col-sm-4">
                        <input id="Address" col="Address" type="text" class="form-control" readonly />
                    </div>
                   
                </div>

                <div class="form-group row">
                    <label class="col-sm-2 control-label ">供应商</label>
                    <div class="col-sm-4">
                        <input id="SupplierName" col="SupplierName" type="text" class="form-control" readonly />
                    </div>
                    <label class="col-sm-2 control-label ">品牌</label>
                    <div class="col-sm-4">
                        <input id="Brand" col="Brand" type="text" class="form-control" readonly />
                    </div>
                </div>

                <div class="form-group row">
                    <label class="col-sm-2 control-label ">管理部门</label>
                    <div class="col-sm-4">
                        <input id="DepartmentName" col="DepartmentName" type="text" class="form-control" readonly />
                    </div>
                    <label class="col-sm-2 control-label ">管理人</label>
                    <div class="col-sm-4">
                        <input id="RealName" col="RealName" type="text" class="form-control" readonly />
                    </div>
                </div>
               

               
                <div class="form-group row">
                    <label class="col-sm-2 control-label ">保修期（月）</label>
                    <div class="col-sm-4">
                        <input id="WarrantyMonth" col="WarrantyMonth" type="text" class="form-control" readonly />
                    </div>
                    <label class="col-sm-2 control-label ">采购日期</label>
                    <div class="col-sm-4">
                        <input id="PurchaseDate" col="PurchaseDate" type="text" class="form-control" readonly />
                    </div>
                </div>

                <div class="form-group row">
                    <label class="col-sm-2 control-label ">仪器图片</label>
                    <div class="col-sm-8">
                        <div style="height: 55px;display:none;" id="fileQueue"></div>
                        <input id="AttachmentId" col="AttachmentId" type="hidden" value="0" />
                        <input id="Path" col="Path" type="hidden" />
                        <a modal="zoomImg" href="javascript:void(0);" id="awardSrc">
                            <img modal="zoomImg" href="javascript:void(0);" src="" id="imgAward" class="col-sm-8" style="max-width:200px;padding: 10px 0px;" />
                        </a>
                    </div>
                </div>
            </form>
        </div>

    </div>
</div>
<script type="text/javascript">
    var id = ys.request("id");
    var opt = ys.request("opt");

    $(function () {

        getForm();

    });


    function getForm() {
        var url = '@Url.Content("~/QueryStatisticsManage/Instrument/GetFormJson")' + '?id=' + id;
        if (opt == 2){
            url = '@Url.Content("~/QueryStatisticsManage/Instrument/GetStatFormJson")' + '?id=' + id;
        }
        if (id > 0) {
            ys.ajax({
                url: url,
                type: 'get',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        obj.Data.PurchaseDate = ys.formatDate(obj.Data.PurchaseDate, "yyyy-MM-dd");
                        obj.Data.Price = ComBox.ToLocaleString(obj.Data.Price);
                        obj.Data.SumMoney = ComBox.ToLocaleString(obj.Data.SumMoney);
                        $('#form').setWebControls(obj.Data);

                        var address = obj.Data.FunRoom;
                        if (obj.Data.Cupboard) {
                            address += '>' + obj.Data.Cupboard;;
                        }
                        if (obj.Data.Floor) {
                            address += '>' + obj.Data.Floor;
                        }
                        $("#Address").val(address);

                        if (obj.Data.AttachmentList.length > 0) {
                            $('#AttachmentId').val(obj.Data.AttachmentList[0].Id);
                            $('#Path').val(obj.Data.AttachmentList[0].Path);
                            $('#imgAward').attr("src", obj.Data.AttachmentList[0].Path);
                            $("#awardSrc").attr("src", obj.Data.AttachmentList[0].Path);
                            imgages.showextalink();
                        }
                    }
                }
            });
        }
    }

</script>
