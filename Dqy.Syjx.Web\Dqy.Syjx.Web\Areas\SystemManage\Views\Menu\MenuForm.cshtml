﻿@{
    Layout = "~/Views/Shared/_FormWhite.cshtml";
}

<div class="wrapper animated fadeInRight">
    <form id="form" class="form-horizontal m">
        <div class="form-group row">
            <label class="col-sm-3 control-label ">上级菜单</label>
            <div class="col-sm-8">
                <input id="parentId" type="hidden" col="ParentId" />
                <input id="parentName" col="ParentName" type="text" class="form-control" readonly="readonly" onclick="showChooseForm()" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label">菜单类型</label>
            <div class="col-sm-8" id="menuType" col="MenuType"></div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label">菜单名称<font class="red"> *</font></label>
            <div class="col-sm-8">
                <input id="menuName" col="MenuName" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <div style="text-align:center;color:red;">修改菜单名称时，请检查该名称是否出现在“首页”—>“快捷入口”，如果出现请联系研发人员修改并更新平台！</div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label">权限标识</label>
            <div class="col-sm-8">
                <input id="authorize" col="Authorize" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label">请求地址</label>
            <div class="col-sm-8">
                <input id="menuUrl" col="MenuUrl" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label">显示排序</label>
            <div class="col-sm-8">
                <input id="menuSort" col="MenuSort" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label">图标</label>
            <div class="col-sm-8">
                <input id="menuIcon" col="MenuIcon" class="form-control" type="text" placeholder="选择图标">
                <div class="ms-parent" style="width: 100%;">
                    <div class="icon-drop animated flipInX" style="display: none;max-height:200px;overflow-y:auto">
                        @Html.PartialAsync("MenuIcon").Result
                    </div>
                </div>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label">菜单状态</label>
            <div class="col-sm-8" id="menuStatus" col="MenuStatus"></div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">
                <span class="glyphicon glyphicon-question-sign titleprompt inputprompt" aria-hidden="true" data-container="body" data-toggle="tooltip" data-placement="right"
                      data-content="非必填，只对菜单类型为“页面”的才有效!"></span>
                显示位置
            </label>
            <div class="col-sm-8">
                <div id="displayLocation" col="DisplayLocation"></div>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label">备注</label>
            <div class="col-sm-8">
                <input id="Remark" col="Remark" type="text" class="form-control" />
            </div>
        </div>
    </form>
</div>

<script type="text/javascript">
    var id = ys.request("id");
    var parentId = ys.request("parentId");
    var parentName = ys.request("parentName");
    $(function () {

        $(".inputprompt").popover({
            trigger: 'hover',
            placement: 'right',
            html: true
        });

        getForm();

        $("#menuIcon").focus(function () {
            $(".icon-drop").show();
        });
        $(".icon-drop").find(".ico-list i").on("click", function () {
            $('#menuIcon').val($(this).attr('class'));
            $(".icon-drop").hide();
        });

        $("#displayLocation").ysComboBox({
            data: ys.getJson(@Html.Raw(typeof(DisplayLocationEnum).EnumToDictionaryString())),
            class: 'form-control'
        });

        $("#menuType").ysRadioBox({ data: ys.getJson(@Html.Raw(typeof(MenuTypeEnum).EnumToDictionaryString())) });
        $("#menuStatus").ysRadioBox({ data: ys.getJson(@Html.Raw(typeof(StatusEnum).EnumToDictionaryString())) });

        $('input[name=menuType_radiobox]').on('ifChecked', function (event) {
            var menuType = $(event.target).val();
            menuVisible(menuType);
        });
        $("#form").validate({
            rules: {
                menuName: { required: true }
            }
        });
    });

    function getForm() {
        if (id > 0) {
            ys.ajax({
                url: '@Url.Content("~/SystemManage/Menu/GetFormJson")' + '?id=' + id,
                type: "get",
                success: function (obj) {
                    if (obj.Tag == 1) {
                        $("#form").setWebControls(obj.Data);
                    }
                }
            });
        }
        else {
            ys.ajax({
                url: '@Url.Content("~/SystemManage/Menu/GetMaxSortJson")' + '?parentId=' + parentId,
                type: "get",
                success: function (obj) {
                    if (obj.Tag == 1) {
                        var defaultData = {};
                        defaultData.ParentId = parentId;
                        if (parentName) {
                            defaultData.ParentName = parentName;
                        }
                        else {
                            defaultData.ParentName = "主目录";
                        }
                        defaultData.MenuStatus = "@StatusEnum.Yes.ParseToInt()";

                        defaultData.MenuSort = obj.Data;
                        $("#form").setWebControls(defaultData);
                    }
                }
            });
        }
    }

    function menuVisible(menuType) {
        if (menuType == "@MenuTypeEnum.Directory.ParseToInt()") {
            $("#menuUrl").parents(".form-group").hide();
            $("#authorize").parents(".form-group").hide();
            $("#menuIcon").parents(".form-group").show();
        } else if (menuType == "@MenuTypeEnum.Menu.ParseToInt()") {
            $("#menuUrl").parents(".form-group").show();
            $("#authorize").parents(".form-group").show();
            $("#menuIcon").parents(".form-group").hide();
        } else if (menuType == "@MenuTypeEnum.Button.ParseToInt()") {
            $("#menuUrl").parents(".form-group").hide();
            $("#authorize").parents(".form-group").show();
            $("#menuIcon").parents(".form-group").hide();
        }
    }

    function saveForm(index) {
        if ($("#form").validate().form()) {
            var postData = $("#form").getWebControls({ Id: id });
            ys.ajax({
                url: '@Url.Content("~/SystemManage/Menu/SaveFormJson")',
                type: "post",
                data: postData,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);;
                        parent.searchTreeGrid(obj.Data);
                        parent.layer.close(index);
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        }
    }

    function showChooseForm() {
        ys.openDialog({
            title: "菜单选择",
            content: '@Url.Content("~/SystemManage/Menu/MenuChoose")' + '?id=' + id,
            width: "300px",
            height: "390px",
            shadeClose: true,
            callback: function (index, layero) {
                var childFrame = window[layero.find('iframe')[0]['name']];
                var treeId = $(childFrame.document.body).find('#treeId').val();
                var treeName = $(childFrame.document.body).find('#treeName').val();
                $("#parentName").val(treeName);
                $("#parentId").val(treeId);
                layer.close(index);
            }
        });
    }
</script>
