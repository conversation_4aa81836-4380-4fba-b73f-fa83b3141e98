﻿@{ Layout = "~/Views/Shared/_FormWhite.cshtml"; }

<div class="wrapper animated fadeInRight">
    <form id="form" class="form-horizontal m">
        <input type="hidden" id="Id" col="Id" value="0" />
        <div class="form-group row">
            <label class="col-sm-3 control-label ">字典类型<font class="red"> *</font></label>
            <div class="col-sm-8">
                <input id="TypeCode" col="TypeCode" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">类别名称<font class="red"> *</font></label>
            <div class="col-sm-8">
                <input id="TypeName" col="TypeName" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">名称<font class="red"> *</font></label>
            <div class="col-sm-8">
                <input id="DicName" col="DicName" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">值<font class="red"> *</font></label>
            <div class="col-sm-8">
                <input id="DictionaryId" col="DictionaryId" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">字典排序<font class="red"> *</font></label>
            <div class="col-sm-8">
                <input id="Sequence" col="Sequence" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">描述<font class="red"> *</font></label>
            <div class="col-sm-8">
                <input id="Memo" col="Memo" type="text" class="form-control" />
            </div>
        </div>
    </form>
</div>

<script type="text/javascript">
    var id = ys.request("id");
    var typeCode = "ZD000";
    var typeName = "字典分类";
    $(function () {
        getForm();

        $("#form").validate({
            rules: {
                TypeCode: { required: true },
                DicName: { required: true },
                DictionaryId: { required: true, number: true},
                Sequence: { required: true, number: true }
            }
        });
    });

    function getForm() {
        if (id > 0) {
            ys.ajax({
                url: '@Url.Content("~/SystemManage/StaticDictionary/GetFormJson")' + '?id=' + id,
                type: "get",
                success: function (obj) {
                    if (obj.Tag == 1) {
                        $("#form").setWebControls(obj.Data);
                    }
                }
            });
        }
        else {
            ys.ajax({
                url: '@Url.Content("~/SystemManage/StaticDictionary/GetMaxSortJson")' + '?TypeCode=' + typeCode,
                type: "get",
                success: function (obj) {
                    if (obj.Tag == 1) {
                        var defaultData = { TypeCode: typeCode, TypeName: typeName };
                        defaultData.DictionaryId = obj.Data;
                        $("#form").setWebControls(defaultData);
                    }
                }
            }); 
        }
    }
    function saveForm(index) {
        if ($("#form").validate().form()) {
            var postData = $("#form").getWebControls({ Id: id });
            ys.ajax({
                url: '@Url.Content("~/SystemManage/StaticDictionary/SaveFormJson")',
                type: "post",
                data: postData,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        parent.$('#gridTable').ysTable('refresh');parent.resetToolbarStatus();
                        parent.layer.close(index);
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        }
    }
</script>

