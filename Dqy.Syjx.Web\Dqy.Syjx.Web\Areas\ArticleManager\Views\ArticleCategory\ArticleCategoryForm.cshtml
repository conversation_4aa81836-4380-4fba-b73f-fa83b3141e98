﻿@{
    Layout = "~/Views/Shared/_FormWhite.cshtml";
}

<div class="wrapper animated fadeInRight">
    <form id="form" class="form-horizontal m">
        <div class="form-group row">
            <label class="col-sm-3 control-label ">显示位置<font class="red"> *</font></label>
            <div class="col-sm-8">
                <div id="CateType" col="CateType"></div>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">上级<font class="red"> *</font></label>
            <div class="col-sm-8">
                <div id="Pid" col="Pid"></div>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">分类名称<font class="red"> *</font></label>
            <div class="col-sm-8">
                <input id="Name" col="Name" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">排序值<font class="red"> *</font></label>
            <div class="col-sm-8">
                <input id="Sort" col="Sort" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">自定义编码</label>
            <div class="col-sm-8">
                <input id="ConfigCode" col="ConfigCode" type="text" class="form-control" />
            </div>
        </div>
    </form>
</div>

<script type="text/javascript">
    var id = ys.request("id");
    $(function () {
        getForm();

        $("#CateType").ysComboBox({
            data: ys.getJson(@Html.Raw(typeof(DisplayLocationEnum).EnumToDictionaryString())),
            class: 'form-control'
        });
        loadCategory(0);

        $('#form').validate({
            rules: {
                CateType: { required: true },
                Pid: { required: true },
                Name: { required: true },
                Sort: { required: true, digits: true,max:9999 }
            }
        });
    });

     function loadCategory(pid) {;
         $('#Pid').ysComboBox({ class: 'form-control'});
        ys.ajax({
            url: '@Url.Content("~/ArticleManager/ArticleCategory/GetListJson?Pid=0")',
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1) {
                    obj.Data.unshift({ Id: 0, Pid:0, Name: "根目录" });
                    $('#Pid').ysComboBox({ data: obj.Data, class: 'form-control', key: 'Id', value: 'Name' });
                    if (pid > 0) {
                        $('#Pid').ysComboBox('setValue', pid);
                    } else {
                        $('#Pid').ysComboBox('setValue', 0);
                    }
                } else {
                    $('#Pid').ysComboBox({ data: [{ Id: 0, Name: "根目录" }], class: 'form-control', key: 'Id', value: 'Name' });
                }
            }
        });
    }

    function getForm() {
        if (id > 0) {
            ys.ajax({
                url: '@Url.Content("~/ArticleManager/ArticleCategory/GetFormJson")' + '?id=' + id,
                type: 'get',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        $('#form').setWebControls(obj.Data);
                        loadCategory(obj.Data.Pid);
                    }
                }
            });
        }
        else {
            var defaultData = {};
            $('#form').setWebControls(defaultData);
        }
    }

    function saveForm(index) {
        if ($('#form').validate().form()) {
            var postData = $('#form').getWebControls({ Id: id });
            if (postData.CateType == "") {
                ys.msgError("请选择显示位置");
                return;
            }
            if (postData.Pid == "") {
                ys.msgError("请选择上级");
                return;
            }
            ys.ajax({
                url: '@Url.Content("~/ArticleManager/ArticleCategory/SaveFormJson")',
                type: 'post',
                data: postData,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        parent.$('#gridTable').ysTable('refresh');parent.resetToolbarStatus();
                        parent.layer.close(index);
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        }
    }
</script>

