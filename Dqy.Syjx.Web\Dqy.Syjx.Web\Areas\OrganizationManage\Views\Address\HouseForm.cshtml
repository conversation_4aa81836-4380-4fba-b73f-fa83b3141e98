﻿@{
    Layout = "~/Views/Shared/_FormWhite.cshtml";
}

<div class="wrapper animated fadeInRight">
    <form id="form" class="form-horizontal m">
        <input type="hidden" id="Id" col="Id" value="0" />
        <input type="hidden" id="Pid" col="Pid" value="0" />
        <input type="hidden" id="Depth" col="Depth" value="0" />
        <input type="hidden" id="UnitId" col="UnitId" value="0" />
        <div class="form-group row">
            <label class="col-sm-3 control-label ">楼宇（场地）<font class="red"> *</font></label>
            <div class="col-sm-7">
                <input id="Name" col="Name" type="text" class="form-control" autocomplete="off"/>
            </div>
            <div class="col-sm-1" style="padding-top:8px;">
                <span id="spanName" class="glyphicon glyphicon-question-sign titleprompt inputprompt" aria-hidden="true" data-container="body" data-toggle="tooltip" data-content="如：1号楼、求知楼、操场等"></span>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">排序号<font class="red"> *</font></label>
            <div class="col-sm-7">
                <input id="Sort" col="Sort" type="text" class="form-control" autocomplete="off" />
            </div>
            <div class="col-sm-1"> 
            </div>
        </div>
    </form>
</div>

<script type="text/javascript">
    var id = ys.request("id");
    $(function () {
        $(".inputprompt").popover({
            trigger: 'hover',
            placement: 'left',
            html: true
        });
        getForm();

        $('#form').validate({
            rules: {
                Name: { required: true },
                Sort: { required: true, number: true  }
            }
        });
    });

    function getForm() {
        if (id > 0) {
            ys.ajax({
                url: '@Url.Content("~/OrganizationManage/Address/GetFormJson")' + '?id=' + id,
                type: 'get',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        $('#form').setWebControls(obj.Data);
                    }
                }
            });
        }
        else {
            var defaultData = { Id: 0, Pid: 0, Depth: 0, Sort: 9999 };
            $('#form').setWebControls(defaultData);
        }
    }

    function saveForm(index) {
        if ($('#form').validate().form()) {
            var postData = $('#form').getWebControls({ Id: id });
            ys.ajax({
                url: '@Url.Content("~/OrganizationManage/Address/SaveFormJson")',
                type: 'post',
                data: postData,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        parent.layer.close(index);
                        parent.searchTreeGrid();
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        }
    }
</script>

