{"version": 2, "dgSpecHash": "v+PWuyzPcht9jfR6A1IYzmE8obw9gte630zPSgev2hfreRpO0yqtDpIraAVD2EA3oX2P76YEQHratitsCvB7tw==", "success": true, "projectFilePath": "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Test\\Dqy.Syjx.DataTest\\Dqy.Syjx.DataTest.csproj", "expectedPackageFiles": ["C:\\NuGetPackages\\automapper\\10.1.1\\automapper.10.1.1.nupkg.sha512", "C:\\NuGetPackages\\bouncycastle.cryptography\\2.2.1\\bouncycastle.cryptography.2.2.1.nupkg.sha512", "C:\\NuGetPackages\\castle.core\\4.4.1\\castle.core.4.4.1.nupkg.sha512", "C:\\NuGetPackages\\castle.core.asyncinterceptor\\1.7.0\\castle.core.asyncinterceptor.1.7.0.nupkg.sha512", "C:\\NuGetPackages\\dmdbms.dmprovider\\1.1.0.16649\\dmdbms.dmprovider.1.1.0.16649.nupkg.sha512", "C:\\NuGetPackages\\dmdbms.microsoft.entityframeworkcore.dm\\6.0.16.16649\\dmdbms.microsoft.entityframeworkcore.dm.6.0.16.16649.nupkg.sha512", "C:\\NuGetPackages\\dynamitey\\2.0.10.189\\dynamitey.2.0.10.189.nupkg.sha512", "C:\\NuGetPackages\\efcore.sharding\\5.0.16\\efcore.sharding.5.0.16.nupkg.sha512", "C:\\NuGetPackages\\efcore.sharding.sqlserver\\5.0.16\\efcore.sharding.sqlserver.5.0.16.nupkg.sha512", "C:\\NuGetPackages\\enums.net\\4.0.1\\enums.net.4.0.1.nupkg.sha512", "C:\\NuGetPackages\\linqkit.microsoft.entityframeworkcore\\5.0.24\\linqkit.microsoft.entityframeworkcore.5.0.24.nupkg.sha512", "C:\\NuGetPackages\\mathnet.numerics.signed\\4.15.0\\mathnet.numerics.signed.4.15.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.aspnetcore.authentication.jwtbearer\\6.0.11\\microsoft.aspnetcore.authentication.jwtbearer.6.0.11.nupkg.sha512", "C:\\NuGetPackages\\microsoft.aspnetcore.jsonpatch\\6.0.19\\microsoft.aspnetcore.jsonpatch.6.0.19.nupkg.sha512", "C:\\NuGetPackages\\microsoft.aspnetcore.mvc.newtonsoftjson\\6.0.19\\microsoft.aspnetcore.mvc.newtonsoftjson.6.0.19.nupkg.sha512", "C:\\NuGetPackages\\microsoft.csharp\\4.7.0\\microsoft.csharp.4.7.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.data.sqlclient\\2.1.4\\microsoft.data.sqlclient.2.1.4.nupkg.sha512", "C:\\NuGetPackages\\microsoft.data.sqlclient.sni.runtime\\2.1.1\\microsoft.data.sqlclient.sni.runtime.2.1.1.nupkg.sha512", "C:\\NuGetPackages\\microsoft.dotnet.internalabstractions\\1.0.0\\microsoft.dotnet.internalabstractions.1.0.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.entityframeworkcore\\6.0.19\\microsoft.entityframeworkcore.6.0.19.nupkg.sha512", "C:\\NuGetPackages\\microsoft.entityframeworkcore.abstractions\\6.0.19\\microsoft.entityframeworkcore.abstractions.6.0.19.nupkg.sha512", "C:\\NuGetPackages\\microsoft.entityframeworkcore.analyzers\\6.0.19\\microsoft.entityframeworkcore.analyzers.6.0.19.nupkg.sha512", "C:\\NuGetPackages\\microsoft.entityframeworkcore.relational\\6.0.19\\microsoft.entityframeworkcore.relational.6.0.19.nupkg.sha512", "C:\\NuGetPackages\\microsoft.entityframeworkcore.sqlserver\\6.0.19\\microsoft.entityframeworkcore.sqlserver.6.0.19.nupkg.sha512", "C:\\NuGetPackages\\microsoft.entityframeworkcore.sqlserver.nettopologysuite\\5.0.5\\microsoft.entityframeworkcore.sqlserver.nettopologysuite.5.0.5.nupkg.sha512", "C:\\NuGetPackages\\microsoft.extensions.caching.abstractions\\7.0.0\\microsoft.extensions.caching.abstractions.7.0.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.extensions.caching.memory\\7.0.0\\microsoft.extensions.caching.memory.7.0.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.extensions.configuration.abstractions\\6.0.0\\microsoft.extensions.configuration.abstractions.6.0.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.extensions.configuration.binder\\6.0.0\\microsoft.extensions.configuration.binder.6.0.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.extensions.dependencyinjection\\6.0.1\\microsoft.extensions.dependencyinjection.6.0.1.nupkg.sha512", "C:\\NuGetPackages\\microsoft.extensions.dependencyinjection.abstractions\\7.0.0\\microsoft.extensions.dependencyinjection.abstractions.7.0.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.extensions.fileproviders.abstractions\\5.0.0\\microsoft.extensions.fileproviders.abstractions.5.0.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.extensions.hosting.abstractions\\5.0.0\\microsoft.extensions.hosting.abstractions.5.0.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.extensions.http\\6.0.0\\microsoft.extensions.http.6.0.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.extensions.http.polly\\6.0.0\\microsoft.extensions.http.polly.6.0.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.extensions.logging\\6.0.0\\microsoft.extensions.logging.6.0.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.extensions.logging.abstractions\\7.0.0\\microsoft.extensions.logging.abstractions.7.0.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.extensions.options\\7.0.0\\microsoft.extensions.options.7.0.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.extensions.options.configurationextensions\\6.0.0\\microsoft.extensions.options.configurationextensions.6.0.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.extensions.primitives\\7.0.0\\microsoft.extensions.primitives.7.0.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.identity.client\\4.21.1\\microsoft.identity.client.4.21.1.nupkg.sha512", "C:\\NuGetPackages\\microsoft.identitymodel.jsonwebtokens\\6.17.0\\microsoft.identitymodel.jsonwebtokens.6.17.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.identitymodel.logging\\6.17.0\\microsoft.identitymodel.logging.6.17.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.identitymodel.protocols\\6.10.0\\microsoft.identitymodel.protocols.6.10.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.identitymodel.protocols.openidconnect\\6.10.0\\microsoft.identitymodel.protocols.openidconnect.6.10.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.identitymodel.tokens\\6.17.0\\microsoft.identitymodel.tokens.6.17.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.io.recyclablememorystream\\2.3.2\\microsoft.io.recyclablememorystream.2.3.2.nupkg.sha512", "C:\\NuGetPackages\\microsoft.net.http.headers\\2.2.8\\microsoft.net.http.headers.2.2.8.nupkg.sha512", "C:\\NuGetPackages\\microsoft.netcore.platforms\\5.0.0\\microsoft.netcore.platforms.5.0.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.netcore.targets\\1.1.0\\microsoft.netcore.targets.1.1.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.win32.primitives\\4.3.0\\microsoft.win32.primitives.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.win32.registry\\4.7.0\\microsoft.win32.registry.4.7.0.nupkg.sha512", "C:\\NuGetPackages\\microsoft.win32.systemevents\\7.0.0\\microsoft.win32.systemevents.7.0.0.nupkg.sha512", "C:\\NuGetPackages\\mysqlconnector\\2.2.5\\mysqlconnector.2.2.5.nupkg.sha512", "C:\\NuGetPackages\\namotion.reflection\\1.0.19\\namotion.reflection.1.0.19.nupkg.sha512", "C:\\NuGetPackages\\netdevpack.security.jwtextensions\\6.0.2\\netdevpack.security.jwtextensions.6.0.2.nupkg.sha512", "C:\\NuGetPackages\\netstandard.library\\2.0.0\\netstandard.library.2.0.0.nupkg.sha512", "C:\\NuGetPackages\\nettopologysuite\\2.1.0\\nettopologysuite.2.1.0.nupkg.sha512", "C:\\NuGetPackages\\nettopologysuite.io.sqlserverbytes\\2.0.0\\nettopologysuite.io.sqlserverbytes.2.0.0.nupkg.sha512", "C:\\NuGetPackages\\newtonsoft.json\\13.0.3\\newtonsoft.json.13.0.3.nupkg.sha512", "C:\\NuGetPackages\\newtonsoft.json.bson\\1.0.2\\newtonsoft.json.bson.1.0.2.nupkg.sha512", "C:\\NuGetPackages\\nlog\\4.7.11\\nlog.4.7.11.nupkg.sha512", "C:\\NuGetPackages\\nlog.extensions.logging\\1.7.4\\nlog.extensions.logging.1.7.4.nupkg.sha512", "C:\\NuGetPackages\\nlog.web.aspnetcore\\4.14.0\\nlog.web.aspnetcore.4.14.0.nupkg.sha512", "C:\\NuGetPackages\\npoi\\2.6.2\\npoi.2.6.2.nupkg.sha512", "C:\\NuGetPackages\\nunit\\3.12.0\\nunit.3.12.0.nupkg.sha512", "C:\\NuGetPackages\\nunit3testadapter\\3.16.1\\nunit3testadapter.3.16.1.nupkg.sha512", "C:\\NuGetPackages\\oracle.manageddataaccess.core\\3.21.100\\oracle.manageddataaccess.core.3.21.100.nupkg.sha512", "C:\\NuGetPackages\\pipelines.sockets.unofficial\\2.2.0\\pipelines.sockets.unofficial.2.2.0.nupkg.sha512", "C:\\NuGetPackages\\polly\\7.2.2\\polly.7.2.2.nupkg.sha512", "C:\\NuGetPackages\\polly.extensions.http\\3.0.0\\polly.extensions.http.3.0.0.nupkg.sha512", "C:\\NuGetPackages\\pomelo.entityframeworkcore.mysql\\6.0.2\\pomelo.entityframeworkcore.mysql.6.0.2.nupkg.sha512", "C:\\NuGetPackages\\qrcoder\\1.4.1\\qrcoder.1.4.1.nupkg.sha512", "C:\\NuGetPackages\\quartz\\3.3.2\\quartz.3.3.2.nupkg.sha512", "C:\\NuGetPackages\\refit\\8.0.0\\refit.8.0.0.nupkg.sha512", "C:\\NuGetPackages\\refit.newtonsoft.json\\8.0.0\\refit.newtonsoft.json.8.0.0.nupkg.sha512", "C:\\NuGetPackages\\runtime.native.system\\4.3.0\\runtime.native.system.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\senparc.co2net\\*******\\senparc.co2net.*******.nupkg.sha512", "C:\\NuGetPackages\\senparc.co2net.apm\\*******\\senparc.co2net.apm.*******.nupkg.sha512", "C:\\NuGetPackages\\senparc.neuchar\\*******\\senparc.neuchar.*******.nupkg.sha512", "C:\\NuGetPackages\\senparc.neuchar.app\\*******\\senparc.neuchar.app.*******.nupkg.sha512", "C:\\NuGetPackages\\senparc.weixin\\********\\senparc.weixin.********.nupkg.sha512", "C:\\NuGetPackages\\senparc.weixin.mp\\**********\\senparc.weixin.mp.**********.nupkg.sha512", "C:\\NuGetPackages\\senparc.weixin.wxopen\\3.15.13\\senparc.weixin.wxopen.3.15.13.nupkg.sha512", "C:\\NuGetPackages\\sharpziplib\\1.3.3\\sharpziplib.1.3.3.nupkg.sha512", "C:\\NuGetPackages\\sixlabors.fonts\\1.0.0\\sixlabors.fonts.1.0.0.nupkg.sha512", "C:\\NuGetPackages\\sixlabors.imagesharp\\2.1.4\\sixlabors.imagesharp.2.1.4.nupkg.sha512", "C:\\NuGetPackages\\skiasharp\\2.88.8\\skiasharp.2.88.8.nupkg.sha512", "C:\\NuGetPackages\\skiasharp.nativeassets.macos\\2.88.8\\skiasharp.nativeassets.macos.2.88.8.nupkg.sha512", "C:\\NuGetPackages\\skiasharp.nativeassets.win32\\2.88.8\\skiasharp.nativeassets.win32.2.88.8.nupkg.sha512", "C:\\NuGetPackages\\stackexchange.redis\\2.2.50\\stackexchange.redis.2.2.50.nupkg.sha512", "C:\\NuGetPackages\\system.appcontext\\4.1.0\\system.appcontext.4.1.0.nupkg.sha512", "C:\\NuGetPackages\\system.buffers\\4.5.0\\system.buffers.4.5.0.nupkg.sha512", "C:\\NuGetPackages\\system.collections\\4.3.0\\system.collections.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.collections.immutable\\6.0.0\\system.collections.immutable.6.0.0.nupkg.sha512", "C:\\NuGetPackages\\system.collections.nongeneric\\4.3.0\\system.collections.nongeneric.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.collections.specialized\\4.3.0\\system.collections.specialized.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.componentmodel\\4.3.0\\system.componentmodel.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.componentmodel.eventbasedasync\\4.3.0\\system.componentmodel.eventbasedasync.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.componentmodel.primitives\\4.3.0\\system.componentmodel.primitives.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.componentmodel.typeconverter\\4.3.0\\system.componentmodel.typeconverter.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.configuration.configurationmanager\\6.0.0\\system.configuration.configurationmanager.6.0.0.nupkg.sha512", "C:\\NuGetPackages\\system.diagnostics.debug\\4.3.0\\system.diagnostics.debug.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.diagnostics.diagnosticsource\\6.0.1\\system.diagnostics.diagnosticsource.6.0.1.nupkg.sha512", "C:\\NuGetPackages\\system.diagnostics.performancecounter\\6.0.1\\system.diagnostics.performancecounter.6.0.1.nupkg.sha512", "C:\\NuGetPackages\\system.diagnostics.process\\4.3.0\\system.diagnostics.process.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.diagnostics.tracesource\\4.3.0\\system.diagnostics.tracesource.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.directoryservices\\6.0.1\\system.directoryservices.6.0.1.nupkg.sha512", "C:\\NuGetPackages\\system.directoryservices.protocols\\6.0.1\\system.directoryservices.protocols.6.0.1.nupkg.sha512", "C:\\NuGetPackages\\system.drawing.common\\7.0.0\\system.drawing.common.7.0.0.nupkg.sha512", "C:\\NuGetPackages\\system.dynamic.runtime\\4.3.0\\system.dynamic.runtime.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.formats.asn1\\6.0.0\\system.formats.asn1.6.0.0.nupkg.sha512", "C:\\NuGetPackages\\system.globalization\\4.3.0\\system.globalization.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.globalization.extensions\\4.3.0\\system.globalization.extensions.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.identitymodel.tokens.jwt\\6.17.0\\system.identitymodel.tokens.jwt.6.17.0.nupkg.sha512", "C:\\NuGetPackages\\system.io\\4.3.0\\system.io.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.io.filesystem\\4.3.0\\system.io.filesystem.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.io.filesystem.primitives\\4.3.0\\system.io.filesystem.primitives.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.io.pipelines\\5.0.0\\system.io.pipelines.5.0.0.nupkg.sha512", "C:\\NuGetPackages\\system.linq\\4.3.0\\system.linq.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.linq.dynamic.core\\1.3.5\\system.linq.dynamic.core.1.3.5.nupkg.sha512", "C:\\NuGetPackages\\system.linq.expressions\\4.3.0\\system.linq.expressions.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.memory\\4.5.3\\system.memory.4.5.3.nupkg.sha512", "C:\\NuGetPackages\\system.numerics.vectors\\4.5.0\\system.numerics.vectors.4.5.0.nupkg.sha512", "C:\\NuGetPackages\\system.objectmodel\\4.3.0\\system.objectmodel.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.private.servicemodel\\4.8.1\\system.private.servicemodel.4.8.1.nupkg.sha512", "C:\\NuGetPackages\\system.reflection\\4.3.0\\system.reflection.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.reflection.dispatchproxy\\4.7.1\\system.reflection.dispatchproxy.4.7.1.nupkg.sha512", "C:\\NuGetPackages\\system.reflection.emit\\4.7.0\\system.reflection.emit.4.7.0.nupkg.sha512", "C:\\NuGetPackages\\system.reflection.emit.ilgeneration\\4.3.0\\system.reflection.emit.ilgeneration.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.reflection.emit.lightweight\\4.3.0\\system.reflection.emit.lightweight.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.reflection.extensions\\4.3.0\\system.reflection.extensions.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.reflection.primitives\\4.3.0\\system.reflection.primitives.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.reflection.typeextensions\\4.3.0\\system.reflection.typeextensions.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.resources.resourcemanager\\4.3.0\\system.resources.resourcemanager.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.runtime\\4.3.0\\system.runtime.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.runtime.caching\\4.7.0\\system.runtime.caching.4.7.0.nupkg.sha512", "C:\\NuGetPackages\\system.runtime.compilerservices.unsafe\\6.0.0\\system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512", "C:\\NuGetPackages\\system.runtime.extensions\\4.3.0\\system.runtime.extensions.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.runtime.handles\\4.3.0\\system.runtime.handles.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.runtime.interopservices\\4.3.0\\system.runtime.interopservices.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.runtime.interopservices.runtimeinformation\\4.3.0\\system.runtime.interopservices.runtimeinformation.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.security.accesscontrol\\6.0.0\\system.security.accesscontrol.6.0.0.nupkg.sha512", "C:\\NuGetPackages\\system.security.cryptography.cng\\4.5.0\\system.security.cryptography.cng.4.5.0.nupkg.sha512", "C:\\NuGetPackages\\system.security.cryptography.pkcs\\6.0.1\\system.security.cryptography.pkcs.6.0.1.nupkg.sha512", "C:\\NuGetPackages\\system.security.cryptography.protecteddata\\6.0.0\\system.security.cryptography.protecteddata.6.0.0.nupkg.sha512", "C:\\NuGetPackages\\system.security.cryptography.xml\\6.0.1\\system.security.cryptography.xml.6.0.1.nupkg.sha512", "C:\\NuGetPackages\\system.security.permissions\\6.0.0\\system.security.permissions.6.0.0.nupkg.sha512", "C:\\NuGetPackages\\system.security.principal.windows\\4.7.0\\system.security.principal.windows.4.7.0.nupkg.sha512", "C:\\NuGetPackages\\system.servicemodel.primitives\\4.8.1\\system.servicemodel.primitives.4.8.1.nupkg.sha512", "C:\\NuGetPackages\\system.text.encoding\\4.3.0\\system.text.encoding.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.text.encoding.codepages\\5.0.0\\system.text.encoding.codepages.5.0.0.nupkg.sha512", "C:\\NuGetPackages\\system.text.encoding.extensions\\4.3.0\\system.text.encoding.extensions.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.text.encodings.web\\6.0.0\\system.text.encodings.web.6.0.0.nupkg.sha512", "C:\\NuGetPackages\\system.text.json\\6.0.3\\system.text.json.6.0.3.nupkg.sha512", "C:\\NuGetPackages\\system.text.regularexpressions\\4.3.0\\system.text.regularexpressions.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.threading\\4.3.0\\system.threading.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.threading.tasks\\4.3.0\\system.threading.tasks.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.threading.tasks.extensions\\4.3.0\\system.threading.tasks.extensions.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.threading.thread\\4.3.0\\system.threading.thread.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.threading.threadpool\\4.3.0\\system.threading.threadpool.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.windows.extensions\\6.0.0\\system.windows.extensions.6.0.0.nupkg.sha512", "C:\\NuGetPackages\\system.xml.readerwriter\\4.3.0\\system.xml.readerwriter.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.xml.xmldocument\\4.3.0\\system.xml.xmldocument.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.xml.xmlserializer\\4.3.0\\system.xml.xmlserializer.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.xml.xpath\\4.3.0\\system.xml.xpath.4.3.0.nupkg.sha512", "C:\\NuGetPackages\\system.xml.xpath.xmldocument\\4.3.0\\system.xml.xpath.xmldocument.4.3.0.nupkg.sha512"], "logs": []}