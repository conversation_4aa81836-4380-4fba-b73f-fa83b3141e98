﻿@using Dqy.Syjx.Entity.SystemManage
@{
    Layout = "~/Views/Shared/_Index.cshtml";
}
@inject Microsoft.AspNetCore.Hosting.IWebHostEnvironment HostingEnvironment
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/laydate/5.0.9/laydate.min.js"))
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/yisha/css/style.min.css"))
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/yisha/js/yisha.min.js"))

@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/jquery.validation/1.14.0/jquery.validate.min.js"))

@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/icheck/1.0.2/skins/custom.min.css"))
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/icheck/1.0.2/icheck.min.js"))

@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/select2/4.0.6/css/select2.min.css"))
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/select2/4.0.6/js/select2.min.js"))

@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/zTree/v3/css/metroStyle/metroStyle.min.css"))
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/zTree/v3/js/ztree.min.js"))

<div class="container-div">
    <div class="btn-group-sm hidden-xs" id="toolbar">
    </div>
    <div class="col-sm-12 select-table table-striped">

        <div class="ibox-title">
            <h5>密码修改</h5>
        </div>

        <div class="card-body">
            <form id="form" class="form-horizontal m">
                <input id="Id" col="Id" type="hidden" />
                <div class="form-group row">
                    <label class="col-sm-2 control-label "><font class="red"> *</font>原始密码：</label>
                    <div class="col-sm-10">
                        <input id="OldPswd" col="OldPswd" type="password" autocomplete="new-password"  class="form-control" />
                    </div>
                </div>
                <div class="form-group row">
                    <label class="col-sm-2 control-label "><font class="red"> *</font>新密码：</label>
                    <div class="col-sm-10">
                        <input id="NewPswd" col="NewPswd" type="password" autocomplete="new-password"  class="form-control" />
                    </div>
                </div>
                <div class="form-group row">
                    <label class="col-sm-2 control-label "><font class="red"> *</font>重复新密码：</label>
                    <div class="col-sm-10">
                        <input id="ConfirmNewPswd" col="ConfirmNewPswd" type="password" autocomplete="new-password"  class="form-control" />
                    </div>
                </div>
            </form>
        </div>
        <div class="btn-group-sm hidden-xs" id="toolbar" style="text-align: center;">
            <a id="btnAdd" class="btn btn-info" onclick="saveForm();"><i class="fa fa-edit"></i>重置密码</a>
        </div>
    </div>
</div>
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/md5/js/md5.min.js"))

<script type="text/javascript">
    $(function () {


        $("#form").validate({
            rules: {
                OldPswd: {
                    required: true,
                },
                NewPswd: {
                    required: true,
                    minlength: 8,
                    maxlength: 20,
                    isLoginpass:true
                },
                ConfirmNewPswd: {
                    required: true,
                    minlength: 8,
                    maxlength: 20,
                    equalTo:"#NewPswd"
                },
            },
            messages: {
                ConfirmNewPswd: {
                    required: "重复新密码不能为空",
                    equalTo: "重复新密码和新密码不一致"
                },
            }
        });
    });


    function saveForm() {
        if ($('#form').validate().form()) {
            var postData = $('#form').getWebControls();
            postData.OldPswd = $.md5(postData.OldPswd);
            postData.NewPswd = $.md5(postData.NewPswd);
            postData.ConfirmNewPswd = $.md5(postData.ConfirmNewPswd);
            ys.ajax({
                url: '@Url.Content("~/OrganizationManage/User/SavePassWord")',
                type: 'post',
                data: postData,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess("修改密码成功！为了您的安全请重新登录!");
                        setTimeout(goOut, 2000);

                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        }
    }


    function goOut() {
        parent.location.href = "@Url.Content("~/Home/LoginOff")";
    }
</script>
