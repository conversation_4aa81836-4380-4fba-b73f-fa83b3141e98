﻿@{ Layout = "~/Views/Shared/_FormWhite.cshtml"; }

<div class="wrapper animated fadeInRight">
    <form id="form" class="form-horizontal m">
        <input type="hidden" id="Id" col="Id" value="0" />
        <div class="form-group row">
            <label class="col-sm-3 control-label ">字典类型<font class="red"> *</font></label>
            <div class="col-sm-8">
                <input id="TypeCode" col="TypeCode" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">类别名称<font class="red"> *</font></label>
            <div class="col-sm-8">
                <input id="TypeName" col="TypeName" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">名称<font class="red"> *</font></label>
            <div class="col-sm-8">
                <input id="DicName" col="DicName" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">值<font class="red"> *</font></label>
            <div class="col-sm-8">
                <input id="DictionaryId" col="DictionaryId" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">父级值<font class="red"> *</font></label>
            <div class="col-sm-8">
                <div id="Pid" col="Pid"></div>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">字典排序<font class="red"> *</font></label>
            <div class="col-sm-8">
                <input id="Sequence" col="Sequence" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">描述<font class="red"> *</font></label>
            <div class="col-sm-8">
                <input id="Memo" col="Memo" type="text" class="form-control" />
            </div>
        </div>
    </form>
</div>

<script type="text/javascript">
    var id = ys.request("id");
    $(function () {
        loadPidDicData();
        getForm();

        $("#form").validate({
            rules: {
                dictKey: { required: true, digits: true },
                dictValue: { required: true }
            }
        });
    });
    function loadPidDicData() {
        $('#Pid').ysComboBoxTree({
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetTreeJson")' + "?OptType=2&TypeCode=" + parent.typeCode,
            expandLevel: 0,
            callback: {
                customOnClick: function (event, treeId, treeNode) {
                }
            }
        });
    }
    function getForm() {
        if (id > 0) {
            ys.ajax({
                url: '@Url.Content("~/SystemManage/StaticDictionary/GetFormJson")' + '?id=' + id,
                type: "get",
                success: function (obj) {
                    if (obj.Tag == 1) {
                        $("#form").setWebControls(obj.Data);
                        if (!isNaN(obj.Data.Pid) && parseInt(obj.Data.Pid) > 0) {

                        } else {
                            $('#Pid').ysComboBoxTree('setValue', '-1');
                        }
                    }
                }
            });
        }
        else {
            ys.ajax({
                url: '@Url.Content("~/SystemManage/StaticDictionary/GetMaxSortJson")' + '?TypeCode=' + parent.typeCode,
                type: "get",
                success: function (obj) {
                    if (obj.Tag == 1) {
                        var defaultData = { TypeCode: parent.typeCode, TypeName: parent.typeName };
                        defaultData.DictionaryId = obj.Data;
                        $("#form").setWebControls(defaultData);
                    }
                }
            });
        }
    }

    function saveForm(index) {
        if ($("#form").validate().form()) {
            var postData = $("#form").getWebControls({ Id: id });
            var pids = ys.getLastValue(postData.Pid);
            if (isNaN(pids) || pids.length == "" || pids == -1) {
                postData.Pid = 0;
            }
            ys.ajax({
                url: '@Url.Content("~/SystemManage/StaticDictionary/SaveFormJson")',
                type: "post",
                data: postData,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        if (top.initDataDict) {
                            top.initDataDict();
                        }
                        ys.msgSuccess(obj.Message);
                        parent.$('#gridTable').ysTable('refresh');parent.resetToolbarStatus();
                        parent.layer.close(index);
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        }
    }
</script>

