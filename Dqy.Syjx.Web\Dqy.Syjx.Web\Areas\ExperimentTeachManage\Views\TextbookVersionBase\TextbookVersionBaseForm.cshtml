﻿@{
    Layout = "~/Views/Shared/_FormWhite.cshtml";
}

<div class="wrapper animated fadeInRight">
    <form id="form" class="form-horizontal m">
        <div class="form-group row">
            <label class="col-sm-3 control-label ">实验教材版本<font class="red"> *</font></label>
            <div class="col-sm-8">
                <input id="versionName" col="VersionName" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">学段<font class="red"> *</font></label>
            <div class="col-sm-8">
                <div id="schoolStage" col="SchoolStage"></div>
            </div>
        </div>
        <div class="form-group nongaozhong">
            <label class="col-sm-3 control-label ">年级<font class="red"> *</font></label>
            <div class="col-sm-8">
                <div id="gradeId" col="GradeId"></div>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">学科<font class="red"> *</font></label>
            <div class="col-sm-8">
                <div id="courseId" col="CourseId"></div>
            </div>
        </div>
        <div class="form-group nongaozhong">
            <label class="col-sm-3 control-label ">学期<font class="red"> *</font></label>
            <div class="col-sm-8">
                <div id="schoolTerm" col="SchoolTerm"></div>
            </div>
        </div>
        <div class="form-group spancompulsorytype">
            <label class="col-sm-3 control-label ">高中教材类型<font class="red"> *</font></label>
            <div class="col-sm-8">
                <div id="CompulsoryType" col="CompulsoryType"></div>
            </div>
        </div>
    </form>
</div>

<script type="text/javascript">
    var id = ys.request("id");
    var CurrentSelectSchoolStageId = 0;
    var CurrentSelectGradeId = 0;
    $(function () {
        initBindComboBox();
        getForm(); 
        $('#form').validate({
            rules: {
                versionName: { required: true }
            }
        });
    });

    function initBindComboBox() {
        $("#schoolTerm").ysComboBox({ data: ys.getJson(@Html.Raw(typeof(SchoolTermEnum).EnumToDictionaryString())), class: 'form-control' });
        $("#CompulsoryType").ysComboBox({ data: ys.getJson(@Html.Raw(typeof(TextbookCompulsoryTypeEnum).EnumToDictionaryString())), class: 'form-control' });
        $('#schoolStage').ysComboBox({
            //url: '@Url.Content("~/SystemManage/StaticDictionary/GetListJson")' + '?TypeCode=@DicTypeCodeEnum.SchoolStage.ParseToInt()',
            key: 'DictionaryId',
            value: 'DicName',
            class: 'form-control',
            onChange: function () {
                var selectSchoolStage = $('#schoolStage').ysComboBox('getValue');
                if (CurrentSelectSchoolStageId != selectSchoolStage) {
                    CurrentSelectSchoolStageId = selectSchoolStage;
                    loadGaoZhongShow();
                    if (selectSchoolStage == @SchoolStageEnum.GaoZhong.ParseToInt()) {
                        //加载学科
                        loadCourse(selectSchoolStage);
                    } else {
                        CurrentSelectGradeId = 0;
                        loadGrade(selectSchoolStage);
                    }
                }
            }
        });

        $('#gradeId').ysComboBox({
            //url: '@Url.Content("~/SystemManage/StaticDictionary/GetChildListJson")' + '?TypeCode=@DicTypeCodeEnum.Grade.ParseToInt()&Pid=' + schoolStage,
            key: 'DictionaryId',
            value: 'DicName',
            class: 'form-control',
            onChange: function () {
                var selectGradeId = $('#gradeId').ysComboBox('getValue');
                if (CurrentSelectGradeId != selectGradeId) {
                    CurrentSelectGradeId = selectGradeId;
                    loadCourse(selectGradeId);
                }
            }
        });

        $('#courseId').ysComboBox({
            //url: '@Url.Content("~/SystemManage/StaticDictionary/GetChildToListJson")' + '?TypeCode=@DicTypeCodeEnum.Course.ParseToInt()&Pid=' + gradeId + '&Ids=1005002,1005003,1005004,1005005',
            key: 'DictionaryId',
            value: 'DicName',
            class: 'form-control',
        });
    }

    function getForm() {
        if (id > 0) {
            ys.ajax({
                url: '@Url.Content("~/ExperimentTeachManage/TextbookVersionBase/GetFormJson")' + '?id=' + id,
                type: 'get',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        /*$('#form').setWebControls(obj.Data);*/
                        $('#versionName').val(obj.Data.VersionName); 
                        CurrentSelectSchoolStageId = obj.Data.SchoolStage;
                        loadSchoolStage(obj.Data.SchoolStage);
                        if (obj.Data.SchoolStage == @SchoolStageEnum.GaoZhong.ParseToInt()) {
                            loadCourse(obj.Data.SchoolStage, obj.Data.CourseId);
                        } else {
                            CurrentSelectGradeId = obj.Data.GradeId;
                            loadGrade(obj.Data.SchoolStage, obj.Data.GradeId);
                            loadCourse(obj.Data.GradeId, obj.Data.CourseId);
                        } 
                        if (obj.Data.SchoolTerm > 0) $('#schoolTerm').ysComboBox('setValue', obj.Data.SchoolTerm);
                        if (obj.Data.CompulsoryType > 0) { $('#CompulsoryType').ysComboBox('setValue', obj.Data.CompulsoryType); }
                      
                        loadGaoZhongShow();
                    }
                }
            });
        }
        else {
            loadSchoolStage(); 
            loadGrade();
            loadCourse();
            loadGaoZhongShow();
        }
    }

    function loadGaoZhongShow() {
        var selectSchoolStage = $('#schoolStage').ysComboBox('getValue');
        if (selectSchoolStage != undefined && selectSchoolStage > 0) {
            if (selectSchoolStage == @SchoolStageEnum.GaoZhong.ParseToInt()) {
                $(".nongaozhong").hide();
                $('.spancompulsorytype').show();
            } else {
                $(".nongaozhong").show();
                $('.spancompulsorytype').hide();
            }
        } else {
            $(".nongaozhong").hide();
            $('.spancompulsorytype').hide();
        }
    }

    function loadSchoolStage(defaultValue) {
        $('#schoolStage').ysComboBox({
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetListJson")' + '?TypeCode=@DicTypeCodeEnum.SchoolStage.ParseToInt()',
            key: 'DictionaryId',
            value: 'DicName',
            class: 'form-control' 
        });
        if (defaultValue > 0) $('#schoolStage').ysComboBox('setValue', defaultValue);
    }

    function loadGrade(schoolStage, defaultValue) {
        if (schoolStage > 0) {
            ys.ajax({
                url: '@Url.Content("~/SystemManage/StaticDictionary/GetChildListJson")' + '?TypeCode=@DicTypeCodeEnum.Grade.ParseToInt()&Pid=' + schoolStage,
                type: 'get',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        bindCourseData({});
                        bindGradeData(obj.Data, defaultValue);
                    } else {
                        bindGradeData({});
                    }
                }
            });
        }else{
            bindGradeData({});
        }
    }

    function bindGradeData(data, defaultValue) {
        $('#gradeId').ysComboBox({
            class: 'form-control',
            data: data,
            key: 'DictionaryId',
            value: 'DicName'
        });
        if (defaultValue > 0) $('#gradeId').ysComboBox('setValue', defaultValue);
    }

    function loadCourse(pid, defaultValue) {
        if (pid > 0) {
            ys.ajax({
                url: '@Url.Content("~/SystemManage/StaticDictionary/GetChildToListJson")' + '?TypeCode=@DicTypeCodeEnum.Course.ParseToInt()&Pid=' + pid + '&Ids=1005002,1005003,1005004,1005005',
                type: 'get',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        bindCourseData(obj.Data, defaultValue);
                    } else {
                        bindCourseData({});
                    }
                }
            });
        } else {
            bindCourseData({});
        }
    }
    function bindCourseData(data, defaultValue) {
        $('#courseId').ysComboBox({
            class: 'form-control',
            data: data,
            key: 'DictionaryId',
            value: 'DicName'
        });
        if (defaultValue > 0) $('#courseId').ysComboBox('setValue', defaultValue);
    }

    function saveForm(index) {
        if ($('#form').validate().form()) {
            var postData = $('#form').getWebControls({ Id: id });
            if (!postData.SchoolStage) {
                ys.msgError('请选择学段！');
                return false;
            }
           
            if (!postData.CourseId) {
                ys.msgError('请选择学科！');
                return false;
            }
            if (postData.SchoolStage == @SchoolStageEnum.GaoZhong.ParseToInt()) {
                if (!postData.CompulsoryType) {
                    ys.msgError('请选择教材类型！');
                    return false;
                }
            } else {
                if (!postData.GradeId) {
                    ys.msgError('请选择年级！');
                    return false;
                }
                if (!postData.SchoolTerm) {
                    ys.msgError('请选择学期！');
                    return false;
                }
            }
            ys.ajax({
                url: '@Url.Content("~/ExperimentTeachManage/TextbookVersionBase/SaveFormJson")',
                type: 'post',
                data: postData,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        parent.$('#gridTable').ysTable('refresh');parent.resetToolbarStatus();
                        parent.layer.close(index);
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        }
    }
</script>

