﻿@{
    Layout = "~/Views/Shared/_FormWhite.cshtml";
}

<div class="wrapper animated fadeInRight">
    <form id="form" class="form-horizontal m">
        <div class="form-group row">
            <label class="col-sm-3 control-label ">评估项目名称<font class="red"> *</font></label>
            <div class="col-sm-6">
                <div id="EvaluateProjectId" col="EvaluateProjectId"></div>
            </div>
            <div class="col-sm-2">
                <a class="btn btn-primary btn-sm" onclick="showEditProjectForm()"><i class="fa fa-edit"></i> 修改名称</a>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">学段</label>
            <div class="col-sm-6">
                <div id="SchoolStage" col="SchoolStage"></div>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">适用学科</label>
            <div class="col-sm-6">
                <div id="DictionaryId1005" col="DictionaryId1005"></div>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">一级分类</label>
            <div class="col-sm-6">
                <div id="DictionaryId1006A" col="DictionaryId1006A"></div>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">二级分类</label>
            <div class="col-sm-6">
                <div id="DictionaryId1006B" col="DictionaryId1006B"></div>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">指标名称</label>
            <div class="col-sm-6">
                <input id="TargetName" col="TargetName" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">版本名称</label>
            <div class="col-sm-6">
                <div id="EvaluateStandardId" col="EvaluateStandardId"></div>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">备注</label>
            <div class="col-sm-6">
                <textarea id="Remark" col="Remark" style="max-width:100%" class="form-control"></textarea>
            </div>
        </div>
    </form>
    <input type="hidden" id="hidSubjectId" value="" />
    <input type="hidden" id="hidClassOneId" value="" />
    <input type="hidden" id="hidClassTwoId" value="" />
    <input type="hidden" id="hidEvaluateStandardId" value="" />
    <input type="hidden" id="hidEvaluateProjectId" value="" />
</div>

<script type="text/javascript">
    var id = ys.request("id");
    $(function () {
        loadEvaluateProject();
        $('#SchoolStage').ysComboBox({
            url:  '@Url.Content("~/SystemManage/StaticDictionary/GetListJson")' + '?TypeCode=1002',
            class: 'form-control',
            key: 'DictionaryId',
            value: 'DicName',
            onChange: function () {
                var selectid = $('#SchoolStage').ysComboBox('getValue');
                loadSubject(selectid);
            }
        });
        loadSubject(0);
        loadDictionaryId1006A(0);
        loadDictionaryId1006B(0);
        loadEvaluateStandard(0);
        getForm();

        $('#form').validate({
            rules: {
                TargetName: { required: true }
            }
        });
    });
    function loadEvaluateProject() {
        $("#EvaluateProjectId").ysComboBox({
            url: '@Url.Content("~/EvaluateManage/FunRoomEvaluateProject/GetProjectListJson")',
            class: 'form-control',
            key: 'Id',
            value: 'EvaluateName',
        });
        var hidProjectid = $("#hidEvaluateProjectId").val();
        if (hidProjectid != undefined && parseInt(hidProjectid) > 0) {
            $("#EvaluateProjectId").ysComboBox("setValue", hidProjectid);
        }
    }
    function loadSubject(selectid) {
        if (selectid > 0) {
            ys.ajax({
                url: '@Url.Content("~/SystemManage/StaticDictionary/GetChildToListJson")' + '?TypeCode=1005&Pid=' + selectid,
                type: "get",
                success: function (obj) {
                    if (obj.Tag == 1) {
                        loadDictionary1005Data(obj.Data);
                    }
                }
            });
        } else {
            loadDictionary1005Data([]);
        }
    }
    function loadDictionary1005Data(data) {
        $("#DictionaryId1005").ysComboBox({
            data: data,
            class: 'form-control',
            key: 'DictionaryId',
            value: 'DicName',
            onChange: function () {
                var selectid = $('#DictionaryId1005').ysComboBox('getValue');
                loadDictionaryId1006A(selectid);
            }

        });
        if (id > 0 && data.length > 0) {
            var subjectid = $("#hidSubjectId").val();
            if (subjectid != undefined && parseFloat(subjectid)>0) {
                $("#DictionaryId1005").ysComboBox('setValue', subjectid);
            }
        }
    }
    function loadDictionaryId1006A(subjectid) {
        if (subjectid > 0) {
            ys.ajax({
                url: '@Url.Content("~/SystemManage/StaticDictionary/GetChildToListJson")' + '?TypeCode=1006&OptType=7&Pid=' + subjectid,
                type: "get",
                success: function (obj) {
                    if (obj.Tag == 1) {
                        loadDictionaryId1006AData(obj.Data);
                    }
                }
            });
        } else {
               loadDictionaryId1006AData([]);
        }
    }
    function loadDictionaryId1006AData(data) {
        $("#DictionaryId1006A").ysComboBox({
            data: data,
            class: 'form-control',
            key: 'DictionaryId',
            value: 'DicName',
            onChange: function () {
                var selectid = $('#DictionaryId1006A').ysComboBox('getValue');
                loadDictionaryId1006B(selectid);
            }

        });
        if (id > 0 && data.length > 0) {
            var classoneid = $("#hidClassOneId").val();
            if (classoneid != undefined && parseFloat(classoneid) > 0) {
                $("#DictionaryId1006A").ysComboBox('setValue', classoneid);
            }
        }
    }
    function loadDictionaryId1006B(classid) {
        if (classid > 0) {
            var subjectid = $('#DictionaryId1005').ysComboBox('getValue');
            ys.ajax({
                url: '@Url.Content("~/SystemManage/StaticDictionary/GetChildToListJson")' + '?TypeCode=1006&Pid=' + subjectid + '&DictionaryPid=' + classid,
                type: "get",
                success: function (obj) {
                    if (obj.Tag == 1) {
                        loadDictionaryId1006BData(obj.Data);
                    }
                }
            });
        } else {
            loadDictionaryId1006BData({});
        }
    }
    function loadDictionaryId1006BData(data) {
        $("#DictionaryId1006B").ysComboBox({
            data: data,
            class: 'form-control',
            key: 'DictionaryId',
            value: 'DicName',
            onChange: function () {
                var selectid = $('#DictionaryId1006B').ysComboBox('getValue');
                loadEvaluateStandard(selectid);
            }
        });
        if (id > 0 && data.length > 0) {
            var classtwoid = $("#hidClassTwoId").val();
            if (classtwoid != undefined && parseFloat(classtwoid) > 0) {
                $("#DictionaryId1006B").ysComboBox('setValue', classtwoid);
            }
        }
    }
    function loadEvaluateStandard(classtwoid) {
        if (classtwoid > 0) {
            var schoolstage = $('#SchoolStage').ysComboBox('getValue');
            var subjectid = $('#DictionaryId1005').ysComboBox('getValue');
            var classoneid = $("#DictionaryId1006A").ysComboBox('getValue');
            ys.ajax({
                url: '@Url.Content("~/EvaluateManage/FunRoomEvaluateStandard/GetListJson")' + '?SchoolStage=' + schoolstage + '&DictionaryId1005=' + subjectid + '&DictionaryId1006A=' + classoneid + '&DictionaryId1006B=' + classtwoid,
                type: "get",
                success: function (obj) {
                    if (obj.Tag == 1) {
                        loadEvaluateStandardData(obj.Data);
                    }
                }
            });
        } else {
            loadEvaluateStandardData([]);
        }
    }
    function loadEvaluateStandardData(data) {
        $("#EvaluateStandardId").ysComboBox({
            data: data,
            class: 'form-control',
            key: 'Id',
            value: 'VersionName'
        });
        if (id > 0 && data.length > 0) {
            var standardid = $("#hidEvaluateStandardId").val();
            if (standardid != undefined && parseFloat(standardid) > 0) {
                $("#EvaluateStandardId").ysComboBox('setValue', standardid);
            }
        }
    }
    function getForm() {
        if (id > 0) {
            ys.ajax({
                url: '@Url.Content("~/EvaluateManage/FunRoomEvaluateProject/GetFormJson")' + '?id=' + id,
                type: 'get',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        $('#form').setWebControls(obj.Data);
                        $("#hidSubjectId").val(obj.Data.DictionaryId1005);
                        $("#hidClassOneId").val(obj.Data.DictionaryId1006A);
                        $("#hidClassTwoId").val(obj.Data.DictionaryId1006B);
                        $("#hidEvaluateStandardId").val(obj.Data.EvaluateStandardId);
                        $("#hidEvaluateProjectId").val(obj.Data.EvaluateProjectId);
                    }
                }
            });
        }
        else {
            var defaultData = {};
            $('#form').setWebControls(defaultData);
        }
    }
    function saveForm(index) {
        if ($('#form').validate().form()) {
            var postData = $('#form').getWebControls({ Id: id });
            var errorMsg = '';
            if (!(parseInt(postData.EvaluateProjectId) > 0)) {
                errorMsg = '请选择评估项目。<br/>';
            }
            if (!(parseInt(postData.SchoolStage) > 0)) {
                errorMsg = '请选择学段。<br/>';
            }
            if (!(parseInt(postData.DictionaryId1005) > 0)) {
                errorMsg = '请选择适用学科。<br/>';
            }
            if (!(parseInt(postData.DictionaryId1006A) > 0)) {
                errorMsg = '请选择一级分类。<br/>';
            }
            if (!(parseInt(postData.DictionaryId1006B) > 0)) {
                errorMsg = '请选择二级分类。<br/>';
            }
            if (!(parseInt(postData.EvaluateStandardId) > 0)) {
                errorMsg = '请选择版本名称。<br/>';
            }
            if (errorMsg != '') {
                ys.msgError('验证失败，请填写完成再提交！<br/>' + errorMsg);
                return;
            }
            ys.ajax({
                url: '@Url.Content("~/EvaluateManage/FunRoomEvaluateProject/SaveFormJson")',
                type: 'post',
                data: postData,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        parent.$('#gridTable').ysTable('refresh');parent.resetToolbarStatus();
                        parent.layer.close(index);
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        }
    }
    function showEditProjectForm() {
        var projectid = $("#EvaluateProjectId").ysComboBox('getValue');
        if (!(parseInt(projectid) > 0)) {
            ys.msgError('请选择评估项目,才可修改项目名称！<br/>');
            return;
        }
        $("#hidEvaluateProjectId").val(projectid);
        parent.showAddProjectForm(projectid, loadEvaluateProject);
    }
</script>

