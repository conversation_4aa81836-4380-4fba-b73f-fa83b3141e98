﻿@{
    Layout = "~/Views/Shared/_FormWhite.cshtml";
}
@inject Microsoft.AspNetCore.Hosting.IWebHostEnvironment HostingEnvironment
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/uploadifive/File.min.css"))
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/uploadifive/File.min.js"))
<style type="text/css">
    .check-box {
        width: 100px;
    }

    .iradio-box {
        width: 100px;
    }

    .iradio-box {
        position: initial;
        display: inline-block;
    }

    .iradio-blue {
        position: inherit;
        display: inline-block;
        /*  top: 6px;*/
    }

    .tag_msg_color {
        color: #999;
    }

    .select2-container {
        display: block;
        width: auto !important;
    }
</style>
<div class="container-div">
    <div class="row" style="height:auto;">
        <div class="ibox float-e-margins" style="margin-bottom:0px;">
            @*<div class="ibox-title">
            <h5 class="table-tswz">友情提示</h5>
            <div class="ibox-tools">
            <a class="collapse-link">
            <i class="fa fa-chevron-down"></i>
            </a>
            </div>
            </div>*@
            <div style="padding:0px;display:none;">
                <div class="row">
                    <div class="col-sm-12">
                        <div class="card-body table-tswz">
                            注：如还未更新本学期“个人任课信息”，请点击【 <a class="btn btn-secondary btn-sm" onclick="updateClassInfo()"><i class="fa fa-default"></i>&nbsp;设置&nbsp;</a>】按钮，这将便捷你以下信息的填写！
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="btn-group-sm hidden-xs" id="toolbar">
        </div>
        <div class="fixed-table-container">
            <div class="ibox-title">
                <h5>专用室使用登记</h5>
            </div> 
            <div class="form-group row" style="max-width:800px;text-align:center;font-size:15px;font-weight: bold;">
                <div id="RecordType" col="RecordType" style="padding-top:7px;"></div>
            </div>  
            <div class="wrapper animated fadeInRight"> 
                <form id="form" class="form-horizontal m">
                    <div class="form-group row">
                        <label class="col-sm-2 control-label ">登记人<font class="red"> *</font></label>
                        <div class="col-sm-6">
                            <div id="divUseUserName" style="border: 0px solid #e5e6e7;" class="form-control"></div>
                        </div>
                        <div class="col-sm-4">
                        </div>
                    </div>
                    <div class="form-group row">
                        <label class="col-sm-2 control-label ">学段<font class="red"> *</font></label>
                        <div class="col-sm-6">
                            <div id="SchoolStage" col="SchoolStage"></div>
                        </div>
                        <div class="col-sm-4">
                        </div>
                    </div>
                    <div class="form-group courserecord">
                        <label class="col-sm-2 control-label ">学科<font class="red"> *</font></label>
                        <div class="col-sm-6">
                            <div id="DictionaryId1005" col="DictionaryId1005"></div>
                        </div>
                        <div class="col-sm-4 tag_msg_color">
                            新学年需更新任课信息【 <a class="btn btn-secondary btn-sm btn-xs" onclick="updateClassInfo()"><i class="fa fa-default"></i>&nbsp;设置&nbsp;</a>】
                        </div>
                    </div>
                    <div class="form-group courserecord">
                        <label class="col-sm-2 control-label ">上课班级<font class="red"> *</font></label>
                        <div class="col-sm-6">
                            <div id="UseClass" col="UseClass"></div>
                        </div>
                        <div class="col-sm-4">
                        </div>
                    </div>
                    <div class="form-group otherrecord" style="display:none;">
                        <label class="col-sm-2 control-label ">学科</label>
                        <div class="col-sm-6">
                            <div id="OtherDictionaryId1005" col="OtherDictionaryId1005"></div>
                        </div>
                        <div class="col-sm-4 tag_msg_color"> 
                        </div>
                    </div>
                    <div class="form-group otherrecord" style="display:none;">
                        <label class="col-sm-2 control-label ">上课班级</label>
                        <div class="col-sm-6">
                            <div id="OtherUseClass" col="OtherUseClass"></div>
                        </div>
                        <div class="col-sm-4">
                        </div>
                    </div>
                    <div class="form-group row">
                        <label class="col-sm-2 control-label ">上课时间<font class="red"> *</font></label>
                        <div class="col-sm-6">
                            <input id="UseDate" col="UseDate" type="text" class="form-control" readonly />
                        </div>
                        <div class="col-sm-4 tag_msg_color">
                            只能选择本学期内的时间
                        </div>
                    </div>
                    <div class="form-group courserecord">
                        <label class="col-sm-2 control-label ">课程节次<font class="red"> *</font></label>
                        <div class="col-sm-6">
                            <div id="CourseSectionId" col="CourseSectionId"></div>
                        </div>
                    </div>
                    <div class="form-group courserecord">
                        <label class="col-sm-2 control-label ">上课地点<font class="red"> *</font></label>
                        <div class="col-sm-6">
                            <div id="FunRoomId" col="FunRoomId"></div>
                        </div>
                        <div class="col-sm-4">
                        </div>
                    </div>
                    <div class="form-group otherrecord" style="display:none;">
                        <label class="col-sm-2 control-label ">课程节次</label>
                        <div class="col-sm-6">
                            <div id="OtherCourseSectionId" col="OtherCourseSectionId"></div>
                        </div>
                    </div>
                    <div class="form-group otherrecord" style="display:none;">
                        <label class="col-sm-2 control-label ">上课地点<font class="red"> *</font></label>
                        <div class="col-sm-6">
                            <div id="OtherFunRoomId" col="OtherFunRoomId"></div>
                        </div>
                        <div class="col-sm-4">
                        </div>
                    </div>
                    <div class="form-group row">
                        <label class="col-sm-2 control-label " id="labClassContent">上课内容</label>
                        <div class="col-sm-6">
                            <textarea id="ClassContent" style="max-width:100%" col="ClassContent" class="form-control"></textarea>
                        </div>
                        <div class="col-sm-4">
                        </div>
                    </div>
                    <div class="form-group row" id="divFunRoom">
                        <label class="col-sm-2 control-label " id="labImage">现场照片</label>
                        <div class="col-sm-6">
                            <input type="file" name="uploadify" id="uploadify" />
                            <div id="spanFile" style="padding: 10px 0px;"></div>
                            <div style="height: 55px;display:none;" id="fileQueue"></div>
                        </div>
                        <div class="col-sm-4 tag_msg_color">
                            文件小于5M，支持图片文件
                        </div>
                    </div>
                    <div class="form-group row">
                        <label class="col-sm-2 control-label ">设备运行<font class="red"> *</font></label>
                        <div class="col-sm-6">
                            <div id="Statuz" col="Statuz" style="padding-top:7px;"></div>
                        </div>
                        <div class="col-sm-4">
                        </div>
                    </div>
                    <div class="form-group row" id="divRemark">
                        <label class="col-sm-2 control-label ">问题描述<font class="red"> *</font></label>
                        <div class="col-sm-6">
                            <textarea id="RepairContent" style="max-width:100%" col="RepairContent" class="form-control"></textarea>
                        </div>
                        <div class="col-sm-4">
                        </div>
                    </div>
                </form>
            </div>
            <div class="btn-group-sm hidden-xs  col-sm-8" id="toolbar" style="text-align: center;">
                <a id="btnAdd" class="btn btn-info" onclick="saveForm();"><i class="fa fa-edit"></i> 保存</a>
            </div>
        </div>
    </div>
</div>
<script type="text/javascript">
    var id = ys.request("id");
    var OptType = ys.request("opttype");
    var IsRelation = 0;
    var SaveValiDate = [];
    var EditFormObjData = {};
    var ComClassData = [];//存储班级数据，用来设置年级Id
    var CurrentSelectIds = {};
    var ClassGradeId = 0;
    $(function () {
        var thisIfromObj = $("#divUseUserName").parent('.YiSha_iframe').first();
        if (isNaN(id) || id == undefined) {
            id = 0;
        }
        loadRecordType();
        initysComboBox();
        getForm();

        loadFormVali(); //配置验证信息
        loadUploadify();

        // loadUseDate();
        // loadStatuz();
        // loadClassData([]); //上课年级班级
    });

    function initysComboBox() {
        //绑定学段
        $('#SchoolStage').ysComboBox({
            class: 'form-control',
            key: 'DictionaryId',
            value: 'DicName',
            onChange: function () {
                var thisval = $('#SchoolStage').ysComboBox('getValue');
                if (CurrentSelectIds.SchoolStage != thisval) {
                    CurrentSelectIds.SchoolStage = thisval;
                    loadSubject();

                    //加载其他
                    loadOtherSection();
                    loadOtherFunroom()
                    loadOtherGradeClass();
                    loadOtherCourse();
                }
            }
        });
        //绑定学科
        $('#DictionaryId1005').ysComboBox({
            class: 'form-control',
            key: 'DictionaryId',
            value: 'DicName',
            onChange: function () {
                var thisval = $('#DictionaryId1005').ysComboBox('getValue');
                if (CurrentSelectIds.DictionaryId1005 != thisval) {
                    CurrentSelectIds.DictionaryId1005 = thisval;
                    loadGradeClassByCourse();
                    loadFunRoom();
                }
            }
        });

        //上课班级信息
        $('#UseClass').ysComboBox({
            class: 'form-control',
            key: 'Id',
            value: 'ClassName',
            defaultName: '请选择',
            onChange: function (e, i, j, n) {
                var id = $('#UseClass').ysComboBox('getValue');
                var selectGrade = 0;
                $.each(ComClassData, function (i, item) {
                    if (item.Id == id) {
                        selectGrade = item.GradeId;
                    }
                })
                ClassGradeId = selectGrade;
                var useDate = $("#UseDate").val();
                if (CurrentSelectIds.GradeId != ClassGradeId && useDate && useDate.length > 0) {
                    CurrentSelectIds.GradeId = ClassGradeId;
                    CurrentSelectIds.UseDate = useDate;
                    loadCourseSection();
                }
            }
        });

        //上课时间
        laydate.render({
            elem: '#UseDate',
            format: 'yyyy-MM-dd',
            trigger: 'click',
            position: 'fixed',
            done: function (value, date) {
                //根据上课时间加载课程节次
                $("#UseDate").val(value);
                if (CurrentSelectIds.UseDate != value && ClassGradeId && Number(ClassGradeId) > 0) {
                    CurrentSelectIds.GradeId = ClassGradeId;
                    CurrentSelectIds.UseDate = value;
                    loadCourseSection();
                }
            }
        });
        //上课节次
        $('#CourseSectionId').ysComboBox({
            class: 'form-control',
            key: 'Id',
            value: 'SectionName',
            defaultName: '请选择'
        });
        loadStatuz();
        //上课地点
        $('#FunRoomId').ysComboBox({
            class: 'form-control',
            key: 'Id',
            value: 'Name',
            onChange: function (even, node, e) {
                var thisval = $('#FunRoomId').ysComboBox('getValue');
                if (CurrentSelectIds.FunRoomId != thisval) {
                    CurrentSelectIds.FunRoomId = thisval;

                    //调用是否关联摄像头方法。
                    ys.ajax({
                        url: '@Url.Content("~/CameraManage/SchoolCamera/GetVerifyJson")' + '?funroomid=' + thisval,
                        type: 'get',
                        success: function (obj) {
                            if (obj.Tag == 1 && obj.Data != undefined && parseInt(obj.Data) == 1) {
                                IsRelation = 1;
                                $("#divFunRoom").hide();
                            } else {
                                $("#divFunRoom").show();
                            }
                        }
                    });

                }
            }
        }); 
    }


    //#region 登记类型
    function loadRecordType() {
        var html = '';
        html += '<label class="iradio-box" style="margin: 0px 50px;width:150px;"><div class="iradio-blue singletype" style="vertical-align:bottom;"><input name="inputTypeRecord_checkbox" type="radio" value="1" style="position: absolute; opacity: 0;"></div> 学科教学登记</label>';
        html += '<label class="iradio-box" style="margin: 0px 50px;width:150px;"><div class="iradio-blue batchtype" style="vertical-align:bottom;"><input name="inputTypeRecord_checkbox" type="radio" value="2" style="position: absolute; opacity: 0;"></div> 其它使用登记</label>';
        $("#RecordType").html(html);
        $(".singletype").addClass("checked");
        $('.singletype > input[name="inputTypeRecord_checkbox"]').prop("checked", "checked");
        $('input[name="inputTypeRecord_checkbox"]').change(function () {
            if ($(this).prop("checked")) {
                var val1 = $(this).val();
                recordChange(val1);
            }
        });
    }
    function recordChange(selectid) {
        if (selectid == 1) {
            $(".singletype").addClass("checked");
            $(".batchtype").removeClass("checked");
            $(".courserecord").show();
            $(".otherrecord").hide();
        } else {
            $(".batchtype").addClass("checked");
            $(".singletype").removeClass("checked"); 
            $(".courserecord").hide();
            $(".otherrecord").show();
     
        }
    }
    //#endregion

    function getForm() {
        ys.ajax({
            url: '@Url.Content("~/BusinessManage/FunRoomUse/GetFormJson")' + '?id=' + id,
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1) {
                    $("#divUseUserName").text(obj.Data.RealName);
                    if (id > 0) {
                       
                        $("#ClassContent").val(obj.Data.ClassContent);
                        $("#RepairContent").val(obj.Data.RepairContent);

                        EditFormObjData = obj.Data;

                        if (isNaN(obj.Data.UseDate) && obj.Data.UseDate.length >= 10) {
                            $("#UseDate").val(obj.Data.UseDate.substring(0, 10));
                        }
                          recordChange(obj.Data.RecordType);
                        if (obj.Data.RecordType == 1) {
                           
                        } else {
                            //其他登记，赋值
                        }

                        loadSchoolStage();
                        if (OptType == 2) {
                            statuzChange(1);
                            $("#RepairContent").val('');
                        } else {
                            statuzChange(obj.Data.Statuz);
                            //调用显示附件信息
                            if (isNaN(obj.Data.AttachmentList) && obj.Data.AttachmentList != undefined) {
                                var fileList = obj.Data.AttachmentList;
                                //先清空
                                var objDiv = $("#spanFile");
                                objDiv.html("");
                                if (fileList.length > 0) {
                                    for (var i = 0; i < fileList.length; i++) {
                                        var attachmentid = fileList[i].Id;
                                        var filePath = fileList[i].Path;
                                        var fileExt = fileList[i].Ext.toUpperCase();
                                        var fileTitle = fileList[i].Title;
                                        if (fileExt == ".JPG" || fileExt == ".BMP" || fileExt == ".JPEG" || fileExt == ".PNG" || fileExt == ".GIF") {
                                            objDiv.append('<span class="keywords" idValue="' + attachmentid + '"> <a type="del" onclick="delFile(this,\'' + attachmentid + '\')"></a><a  modal="zoomImg"  href="javascript:void(0);" src="' + filePath + '" >' + fileTitle + '</a></span>');
                                        } else {
                                            objDiv.append('<span class="keywords" idValue="' + attachmentid + '"> <a type="del" onclick="delFile(this,\'' + attachmentid + '\')"></a><a target="_blank" href="' + filePath + '" >' + fileTitle + '</a></span>');
                                        }
                                    }
                                    imgages.showextalink();
                                }
                            }
                        } 
                    } else {
                        var data = {}
                        $('#form').setWebControls(data);
                        loadSchoolStage();
                      
                    }
                }
            }
        });
    }

    //上课时间
    function loadUseDate() {
        ys.ajax({
            url: '@Url.Content("~/OrganizationManage/SchoolTerm/GetCurrentFormJson")',
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1) {
                    laydate.render({
                        elem: '#UseDate',
                        format: 'yyyy-MM-dd',
                        min: obj.Data.TermStart,
                        max: obj.Data.TermEnd,
                        trigger: 'click',
                        position: 'fixed'

                    });
                } else {
                    ys.alertWarning("当前日期不在平台配置的学期内，禁止登记。");
                    return;
                }
            }
        });
    }


    //#region 运行状态
    function loadStatuz() {
        var html = '';
        html += '<label class="iradio-box"><div class="iradio-blue single" style="vertical-align:bottom;"><input name="inputType_checkbox" type="radio" value="1" style="position: absolute; opacity: 0;"></div> 正常</label>';
        html += '<label class="iradio-box"><div class="iradio-blue batch" style="vertical-align:bottom;"><input name="inputType_checkbox" type="radio" value="2" style="position: absolute; opacity: 0;"></div> 不正常</label>';
        $("#Statuz").html(html);
        $(".single").addClass("checked");
        $('#divRemark').hide();
        $('input[name="inputType_checkbox"]').change(function () {
            if ($(this).prop("checked")) {
                var val1 = $(this).val();
                statuzChange(val1);
            }
        });
    }
    function statuzChange(selectid) {
        if (selectid == 1) {
            $(".single").addClass("checked");
            $(".batch").removeClass("checked");
            $('#divRemark').hide();
        } else {
            $(".batch").addClass("checked");
            $(".single").removeClass("checked");
            $('#divRemark').show();
        }
    }
    //#endregion

    //加载绑定学段
    function loadSchoolStage() {
        ys.ajax({
            url: '@Url.Content("~/BusinessManage/FunRoomUse/GetSchoolStageJson")',
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1) {
                    $('#SchoolStage').ysComboBox({
                        data: obj.Data,
                        class: 'form-control',
                        key: 'DictionaryId',
                        value: 'DicName'
                    });
                    if (EditFormObjData && EditFormObjData.SchoolStage > 0) {
                        $("#SchoolStage").ysComboBox('setValue', EditFormObjData.SchoolStage);
                    } else if (obj.Data.length == 1) {
                        $("#SchoolStage").ysComboBox('setValue', obj.Data[0].DictionaryId);
                    }
                } else {
                    ys.msgError(obj.Message);
                }
            }
        });
    }

    //学科
    function loadSubject() {
        var schoolStage = $('#SchoolStage').ysComboBox('getValue');
        if (schoolStage > 0) {
            ys.ajax({
                url: '@Url.Content("~/BusinessManage/FunRoomUse/GetSubjectJson")' + '?schoolstage=' + schoolStage,
                type: 'get',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        defailBindSubject(obj.Data);
                    } else {
                        defailBindSubject([]);
                        ys.msgError(obj.Message);
                    }
                }
            });
        } else {
            defailBindSubject([], 0, 0);
        }
    }
    function defailBindSubject(data) {
        $('#DictionaryId1005').ysComboBox({
            data: data,
            class: 'form-control',
            key: 'DictionaryId',
            value: 'DicName'
        });
        if (data != undefined && data.length > 0) {
            if (EditFormObjData != undefined && parseInt(EditFormObjData.DictionaryId1005) > 0) {
                $("#DictionaryId1005").ysComboBox('setValue', EditFormObjData.DictionaryId1005);
            } else if (data.length == 1) {
                $("#DictionaryId1005").ysComboBox('setValue', data[0].DictionaryId);
            }
        }
    }
    //根据学科加载年级班级
    function loadGradeClassByCourse(couseid, schoolstage) {
        var schoolstage = $('#SchoolStage').ysComboBox('getValue');
        var couseid = $('#DictionaryId1005').ysComboBox('getValue');
        if (couseid > 0) {
            ys.ajax({
                url: '@Url.Content("~/ExperimentTeachManage/ExperimentBooking/GetGradeClassByCourseJson")' + '?courseid=' + couseid + '&schoolstage=' + schoolstage,
                type: 'get',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        loadClassData(obj.Data);
                    } else {
                        loadClassData([]);
                    }
                }
            });
        } else {
            loadClassData([]);
        }
    }

    //上课年级班级
    function loadClassData(classdata) {
        ComClassData = classdata;
        $('#UseClass').ysComboBox({
            data: classdata,
            class: 'form-control',
            key: 'Id',
            value: 'ClassName',
            defaultName: '请选择'
        });
        if (classdata != undefined && classdata.length > 0 && EditFormObjData && parseInt(EditFormObjData.UseClass) > 0) {
            $('#UseClass').ysComboBox('setValue', EditFormObjData.UseClass);
        } else if (classdata != undefined && classdata.length == 1) {
            $('#UseClass').ysComboBox('setValue', classdata[0].Id);
        } else if (classdata == undefined || classdata.length == 0) {
            // ComBox.LoadPageMessage('任课班级', '/PersonManage/UserClassInfo/UserClassInfoIndex', '任课信息', @RoleEnum.SchoolManager.ParseToInt());
        }
    }
    function loadFunRoom() {
        ys.ajax({
            url: '@Url.Content("~/BusinessManage/FunRoom/GetListJson")' + '?SchoolStage=' + CurrentSelectIds.SchoolStage + '&DictionaryId1005=' + CurrentSelectIds.DictionaryId1005,
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1) {
                    $('#FunRoomId').ysComboBox({
                        data: obj.Data,
                        class: 'form-control',
                        key: 'Id',
                        value: 'Name'
                    });
                    if (EditFormObjData && parseInt(EditFormObjData.FunRoomId) > 0) {
                        $("#FunRoomId").ysComboBox('setValue', EditFormObjData.FunRoomId);
                    } else if (obj.Data.length == 1) {
                        $("#FunRoomId").ysComboBox('setValue', obj.Data[0].Id);
                    }
                } else {
                    ys.msgError(obj.Message);
                }
            }
        });
    }

    //#region 课程节次
    function loadCourseSection() {
        ys.ajax({
            url: '@Url.Content("~/OrganizationManage/CourseSection/GetCourseSelectList")' + '?gradeId=' + CurrentSelectIds.GradeId + '&dtDate=' + $("#UseDate").val(),
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1) {
                    bindCourseSectionData(obj.Data);
                } else {
                    bindCourseSectionData([]);
                    ys.msgError(obj.Message);
                }
            }
        });
    }

    var CourseSectionData;
    //学科节次数据
    function bindCourseSectionData(data) {
        CourseSectionData = data;
        //console.log('课程节次Id-2', selectid);
        $('#CourseSectionId').ysComboBox({
            data: data,
            class: 'form-control',
            key: 'Id',
            value: 'SectionName',
            defaultName: '请选择'
        });
        //根据上课的班级年级加载课程节次

        if (data != null && data.length > 0) {
            if (EditFormObjData && Number(EditFormObjData.CourseSectionId) > 0) {
                $("#CourseSectionId").ysComboBox('setValue', EditFormObjData.CourseSectionId);
            } else if (data.length == 1) {
                $("#CourseSectionId").ysComboBox('setValue', data[0].SectionValue);

            }
        } else {
            ComBox.LoadPageMessage('课程节次', '/OrganizationManage/CourseSection/CourseSectionIndex', '课程节次表', @RoleEnum.SchoolManager.ParseToInt());
        }
    }
    //#endregion

    function loadUploadify() {
        $("#uploadify").uploadifive({
            'uploadScript': '/File/UploadFile?fc=1023',
            'cancelImg': '~/lib/uploadifive/uploadifive-cancel.png',
            'buttonText': '上 传',
            'queueID': 'fileQueue',
            'auto': true,
            'multi': false,
            'fileDesc': '支持格式：*.jpg;*.gif;*.png;*.pdf;',
            'fileExt': '*.jpg;*.gif;*.png;*.pdf;',
            'onAddQueueItem': function (file) {

            },
            'onUploadComplete': function (fileObj, response) {
                if (typeof (response) == "string" && response != "") {
                    response = JSON.parse(response);
                    if (response.Tag == 1) {
                        ys.msgSuccess("上传成功！");
                        var id = response.Description;
                        var filePath = response.Data;
                        var fileTitle = response.Message;
                        var fileExt = filePath.substring(filePath.lastIndexOf('.')).toUpperCase();
                        if (fileExt == ".JPG" || fileExt == ".BMP" || fileExt == ".JPEG" || fileExt == ".PNG" || fileExt == ".GIF") {
                            $("#spanFile").append('<span class="keywords" idValue="' + id + '"> <a type="del" onclick="delFile(this,\'' + id + '\')"></a><a  modal="zoomImg"  href="javascript:void(0);" src="' + filePath + '" >' + fileTitle + '</a></span>');
                        } else {
                            $("#spanFile").append('<span class="keywords" idValue="' + id + '"> <a type="del" onclick="delFile(this,\'' + id + '\')"></a><a target="_blank" href="' + filePath + '" >' + fileTitle + '</a></span>');
                        }
                        imgages.showextalink();
                    } else {
                        ys.msgError(response.Message);
                    }
                }
            },
            'onFallback': function (event) {
                ys.msgError(event);
            }
        });
    }

    function intCombox() { //是否关联摄像头
        $('#FunRoomId').ysComboBox({
            data: [],
            class: 'form-control',
            key: 'Id',
            value: 'Name',
            onChange: function (even, node, e) {
                //调用是否关联摄像头方法。
                var selectid = $('#FunRoomId').ysComboBox('getValue');
                ys.ajax({
                    url: '@Url.Content("~/CameraManage/SchoolCamera/GetVerifyJson")' + '?funroomid=' + selectid,
                    type: 'get',
                    success: function (obj) {
                        if (obj.Tag == 1 && obj.Data != undefined && parseInt(obj.Data) == 1) {
                            IsRelation = 1;
                            $("#divFunRoom").hide();
                        } else {
                            $("#divFunRoom").show();
                        }
                    }
                });
            }
        });
    }

    function saveForm() {
        var postData = $('#form').getWebControls({ Id: id });
        var recordType = $('input[name="inputTypeRecord_checkbox"]:checked').val();
        postData.RecordType = recordType;
        //验证
        var errorMsg = '';
        if (!(postData.SchoolStage > 0)) {
            errorMsg += '请选择任课学段。<br/>';
        }
        if (recordType == 1) {
            if (!(postData.DictionaryId1005 > 0)) {
                errorMsg += '请选择任课学科。<br/>';
            }
            if (!(postData.UseClass > 0)) {
                errorMsg += '请选择任课班级。<br/>';
            }
            if (!(postData.FunRoomId > 0)) {
                errorMsg += '请选择使用地点 。<br/>';
            }
            if (!(postData.UseDate != undefined && postData.UseDate.length > 0)) {
                errorMsg += '请选择使用日期 。<br/>';
            }
            if (!(postData.CourseSectionId > 0)) {
                errorMsg += '请选择课程节次 。<br/>';
            }
        } else {
            if (postData.OtherDictionaryId1005 && postData.OtherDictionaryId1005 > 0) {
                postData.DictionaryId1005 = postData.OtherDictionaryId1005;
            } else {
                postData.DictionaryId1005 = 0;
            }
            if (postData.OtherUseClass && postData.OtherUseClass > 0) {
                postData.UseClass = postData.OtherUseClass;
            } else {
                postData.UseClass = 0;
            }
            if (!(postData.UseDate != undefined && postData.UseDate.length > 0)) {
                errorMsg += '请选择使用日期 。<br/>';
            }
            if (postData.OtherCourseSectionId && postData.OtherCourseSectionId > 0) {
                postData.CourseSectionId = 0;
                postData.UseCourseSectionIndex = postData.OtherCourseSectionId;
            } else {
                postData.CourseSectionId = 0;
            }
            if (postData.OtherFunRoomId && postData.OtherFunRoomId > 0) {
                postData.FunRoomId = postData.OtherFunRoomId;
            } else {
                errorMsg += '请选择使用地点 。<br/>';
            }
        } 
        
        if (postData.Statuz != 1 && postData.Statuz != 2) {
            errorMsg += '请选择设备运行状态 。<br/>';
        } else if (postData.Statuz == 2) {
            if (!(postData.RepairContent != undefined && postData.RepairContent.length > 0)) {
                errorMsg += '请填写问题描述，设备运行不正常必须填写 。<br/>';
            }
        }
        if (!(postData.ClassContent != undefined && postData.ClassContent.length > 0)) {
            /* errorMsg += '请填写上课内容 。<br/>';*/
        } else if (postData.ClassContent.length > 500) {
            errorMsg += '填写的上课内容字符请控制在500以内 。<br/>';
        }
        if (IsRelation == 0) {
            var imageArr = [];
            $(".keywords").each(function () {
                var idValue = $(this).attr("idValue");
                imageArr.push(idValue);
            });
            if (imageArr.length > 0) {
                postData.Imagez = imageArr.join(',');
            }
        }

        if (SaveValiDate != undefined && SaveValiDate.length > 0) {
            SaveValiDate.map(function (item, i) {
                if (item.typecode == "Image") {
                    if (!(imageArr != undefined && imageArr.length > 0) && IsRelation == 0) {
                        errorMsg += (item.verifyhint + '<br/>');
                    }
                } else if (item.typecode == "ClassContent") {
                    if (!(postData.ClassContent != undefined && postData.ClassContent.length > 0)) {
                        errorMsg += (item.verifyhint + '<br/>');
                    }
                }
            });
        }

        if (errorMsg != '') {
            ys.msgError(errorMsg);
            return;
        }
        ys.ajax({
            url: '@Url.Content("~/BusinessManage/FunRoomUse/SaveFormJson")',
            type: 'post',
            data: postData,
            success: function (obj) {
                if (obj.Tag == 1) {
                    ys.msgSuccess(obj.Message);
                    var url = '@Url.Content("~/BusinessManage/FunRoomUse/Index")';
                    createMenuAndCloseCurrent(url, "已登记列表");
                }
                else {
                    ys.msgError(obj.Message);
                }
            }
        });
    }
    //删除附件
    function delFile(obj, value) {
        $(obj).parent().remove();
        imgages.showextalink();
    }
    function updateClassInfo() {
        var url = '@Url.Content("~/PersonManage/UserClassInfo/UserClassInfoIndex")';
        createMenuItem(url, "个人任课信息");
    }

    //#region
    //加载配置验证信息
    function loadFormVali() {
        ys.ajax({
            url: '@Url.Content("~/SystemManage/ConfigSet/GetListByCode")',
            type: 'Post',
            data: { typecode: '2SYSG-3SYGL-1SYDJ' },
            success: function (obj) {
                if (obj.Tag == 1) {
                    if (obj.Data != undefined && obj.Data.length > 0) {
                        for (var i = 0; i < obj.Data.length; i++) {
                            if (obj.Data[i].ConfigValue == "1") {
                                var typecode = obj.Data[i].TypeCode.replaceAll("2SYSG-3SYGL-1SYDJ-", "");
                                var verifyhint = "";
                                if (obj.Data[i].VerifyHint != undefined && obj.Data[i].VerifyHint.length > 0) {
                                    verifyhint = obj.Data[i].VerifyHint;
                                }
                                $("#lab" + typecode).append('<font class="red"> *</font>');
                                SaveValiDate.push({ typecode: typecode, verifyhint: verifyhint });
                            }
                        }
                    }
                }
            }
        });
    }

    //#endregion

    //#region 登记类型，其他登记加载、
    function loadOtherCourse() {
        var schoolStage = 0; 
        ys.ajax({
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetSchoolCourseListJson")' + '?Pid=' + schoolStage,
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1) {
                    bindOtherCourseData(obj.Data);
                } else {
                    bindOtherCourseData([]);
                    ys.msgError(obj.Message);
                }
            }
        });
    }
    function bindOtherCourseData(data) {
        $('#OtherDictionaryId1005').ysComboBox({
            data: data,
            class: 'form-control',
            key: 'DictionaryId',
            value: 'DicName'
        });
        if (data != undefined && data.length > 0) {
            if (EditFormObjData != undefined && parseInt(EditFormObjData.DictionaryId1005) > 0) {
                $("#OtherDictionaryId1005").ysComboBox('setValue', EditFormObjData.DictionaryId1005);
            } else if (data.length == 1) {
                $("#OtherDictionaryId1005").ysComboBox('setValue', data[0].DictionaryId);
            }
        }
    }

    function loadOtherSection() {
        var data = [];
        data.push({ Id: 1, SectionName: "第一节课" });
        data.push({ Id: 2, SectionName: "第二节课" });
        data.push({ Id: 3, SectionName: "第三节课" });
        data.push({ Id: 4, SectionName: "第四节课" });
        data.push({ Id: 5, SectionName: "第五节课" });
        data.push({ Id: 6, SectionName: "第六节课" });
        data.push({ Id: 7, SectionName: "第七节课" });

        $('#OtherCourseSectionId').ysComboBox({
            data: data,
            class: 'form-control',
            key: 'Id',
            value: 'SectionName',
            defaultName: '请选择'
        });
        if (EditFormObjData && Number(EditFormObjData.UseCourseSectionIndex) > 0) {
            $("#OtherCourseSectionId").ysComboBox('setValue', EditFormObjData.UseCourseSectionIndex);
        } else if (data.length == 1) {
            $("#OtherCourseSectionId").ysComboBox('setValue', data[0].SectionValue);

        }
    }

    function loadOtherFunroom(){
        ys.ajax({
            url: '@Url.Content("~/BusinessManage/FunRoom/GetListJson")',
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1) {
                    $('#OtherFunRoomId').ysComboBox({
                        data: obj.Data,
                        class: 'form-control',
                        key: 'Id',
                        value: 'Name'
                    });
                    if (EditFormObjData && parseInt(EditFormObjData.FunRoomId) > 0) {
                        $("#OtherFunRoomId").ysComboBox('setValue', EditFormObjData.FunRoomId);
                    } else if (obj.Data.length == 1) {
                        $("#OtherFunRoomId").ysComboBox('setValue', obj.Data[0].Id);
                    }
                } else {
                    ys.msgError(obj.Message);
                }
            }
        });
    }

    //根据学科加载年级班级
    function loadOtherGradeClass() {
        var schoolstage = $('#SchoolStage').ysComboBox('getValue');
        if (!(schoolstage && parseInt(schoolstage) > 0)) {
            schoolstage = '';
        } 
        ys.ajax({
            url: '@Url.Content("~/OrganizationManage/SchoolGradeClass/GetListJson")' + '?IsGraduate=0' + '&SchoolStage=' + schoolstage,
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1) {
                    loadOtherClassData(obj.Data);
                } else {
                    loadOtherClassData([]);
                }
            }
        });
    }

    //上课年级班级
    function loadOtherClassData(classdata) { 
        $('#OtherUseClass').ysComboBox({
            data: classdata,
            class: 'form-control',
            key: 'Id',
            value: 'ClassName',
            defaultName: '请选择'
        });
        if (classdata != undefined && classdata.length > 0 && EditFormObjData && parseInt(EditFormObjData.UseClass) > 0) {
            $('#OtherUseClass').ysComboBox('setValue', EditFormObjData.UseClass);
        } else if (classdata != undefined && classdata.length == 1) {
            $('#OtherUseClass').ysComboBox('setValue', classdata[0].Id);
        } else if (classdata == undefined || classdata.length == 0) {
            // ComBox.LoadPageMessage('任课班级', '/PersonManage/UserClassInfo/UserClassInfoIndex', '任课信息', @RoleEnum.SchoolManager.ParseToInt());
        }
    }
    //#endregion
</script>

