﻿
@{
    Layout = "~/Views/Shared/_FormWhite.cshtml";
}


@inject Microsoft.AspNetCore.Hosting.IWebHostEnvironment HostingEnvironment
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/laydate/5.1/laydate.js"),true)

<div class="wrapper animated fadeInRight">
    <form id="form" class="form-horizontal m">
        <div class="form-group row">
            <label class="col-sm-2 control-label">
                夏季时段：
            </label>
            <div class="col-sm-4 inline">
                <input id="x_beginDate" col="XBeginDate" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-2 control-label">
                冬季时段：
            </label>
            <div class="col-sm-4 inline">
                <input id="d_beginDate" col="DBeginDate" type="text" class="form-control" />
            </div>
        </div>
    </form>
</div>
<script type="text/javascript">
    var id = ys.request("id");
    $(function () {

        laydate.render({
            elem: '#x_beginDate',
            format: 'MM-dd',
            range: '~',
        });

        laydate.render({
            elem: '#d_beginDate',
            format: 'MM-dd',
            range: '~',
        });

        $("#form").validate({
            rules: {
                x_beginDate: { required: true },
                d_beginDate: { required: true },
            }
        });

        getForm();
    });



    function getForm() {
        var url = '@Url.Content("~/OrganizationManage/UnitSchedule/GetSuperListJson")';
        ys.ajax({
            url: url,
            type: "get",
            success: function (obj) {
                if (obj.Tag == 1 && obj.Total > 0) {
                    var result = obj.Data;
                    $("#x_beginDate").val(result[0].BeginDate + " ~ " + result[0].EndDate);
                    $("#d_beginDate").val(result[0].DjBeginTime + " ~ " + result[0].DjEndTime);
                }
            }
        });
    }


    function saveForm(index) {
        if ($("#form").validate().form()) {
            var formObj = $("#form").getWebControls();
            var beginDate = formObj.XBeginDate;
            var djBeginDate = formObj.DBeginDate;
            var arrXjBegin = beginDate.split('~');
            var arrDjBegin = djBeginDate.split('~');
            var obj = {IsSet: 1, BeginDate: $.trim(arrXjBegin[0]), EndDate: $.trim(arrXjBegin[1]), DjBeginTime: $.trim(arrDjBegin[0]), DjEndTime: $.trim(arrDjBegin[1]) };
            var postData = { schedule: obj, gradeId: id };
            ys.ajax({
                url: '@Url.Content("~/OrganizationManage/UnitSchedule/SaveSuperBatchSuperFormJson")',
                type: "post",
                data: postData,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        parent.$('#gridTable').ysTable('refresh'); parent.resetToolbarStatus();
                        parent.layer.close(index);
                    }
                    else {
                        ys.alertError(obj.Message);
                    }
                }
            });
        }
    }

 </script>