﻿@{
    Layout = "~/Views/Shared/_FormWhite.cshtml";
}

<div class="wrapper animated fadeInRight">
    <form id="form" class="form-horizontal m">
        <div class="form-group row">
            <label class="col-sm-3 control-label ">版本名称<font class="red"> *</font></label>
            <div class="col-sm-8">
                <input id="VersionName" col="VersionName" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">学段<font class="red"> *</font></label>
            <div class="col-sm-8">
                <div id="SchoolStage" col="SchoolStage"></div>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">适用学科<font class="red"> *</font></label>
            <div class="col-sm-8">
                <div id="DictionaryId1005" col="DictionaryId1005"></div>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">一级分类<font class="red"> *</font></label>
            <div class="col-sm-8">
                <div id="DictionaryId1006A" col="DictionaryId1006A"></div>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">二级分类<font class="red"> *</font></label>
            <div class="col-sm-8">
                <div id="DictionaryId1006B" col="DictionaryId1006B"></div>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">指标名称<font class="red"> *</font></label>
            <div class="col-sm-8">
                <input id="TargetName" col="TargetName" type="text" class="form-control" />
            </div>
        </div>
    </form>
    <input type="hidden" id="hidSubjectId" value="" />
    <input type="hidden" id="hidClassOneId" value="" />
    <input type="hidden" id="hidClassTwoId" value="" />
</div>

<script type="text/javascript">
    var id = ys.request("id");
    $(function () {
        $('#SchoolStage').ysComboBox({
            url:  '@Url.Content("~/SystemManage/StaticDictionary/GetListJson")' + '?TypeCode=1002',
            class: 'form-control',
            key: 'DictionaryId',
            value: 'DicName',
            onChange: function () {
                var selectid = $('#SchoolStage').ysComboBox('getValue');
                loadSubject(selectid);
            }
        });
        loadSubject(0);
        loadDictionaryId1006A(0);
        loadDictionaryId1006B(0);
        getForm();

        $('#form').validate({
            rules: {
                VersionName: { required: true },
                TargetName: { required: true }
            }
        });
    });
    function loadSubject(selectid) {
        if (selectid > 0) {
            ys.ajax({
                url: '@Url.Content("~/SystemManage/StaticDictionary/GetChildToListJson")' + '?TypeCode=1005&Pid=' + selectid,
                type: "get",
                success: function (obj) {
                    if (obj.Tag == 1) {
                        loadDictionary1005Data(obj.Data);
                    }
                }
            });
        } else {
            loadDictionary1005Data([]);
        }
    }

    function loadDictionary1005Data(data) {
        $("#DictionaryId1005").ysComboBox({
            data: data,
            class: 'form-control',
            key: 'DictionaryId',
            value: 'DicName',
            onChange: function () {
                var selectid = $('#DictionaryId1005').ysComboBox('getValue');
                loadDictionaryId1006A(selectid);
            }

        });
        if (id > 0 && data.length > 0) {
            var subjectid = $("#hidSubjectId").val();
            if (subjectid != undefined && parseFloat(subjectid)>0) {
                $("#DictionaryId1005").ysComboBox('setValue', subjectid);
            }
        }
    }
    /**功能室分类。 */
    function loadDictionaryId1006A(subjectid) {
        if (subjectid > 0) {
            ys.ajax({
                url: '@Url.Content("~/SystemManage/StaticDictionary/GetChildToListJson")' + '?TypeCode=1006&OptType=7&Pid=' + subjectid,
                type: "get",
                success: function (obj) {
                    if (obj.Tag == 1) {
                        loadDictionaryId1006AData(obj.Data);
                    }
                }
            });
        } else {
               loadDictionaryId1006AData([]);
        }
    }
    function loadDictionaryId1006AData(data) {
        $("#DictionaryId1006A").ysComboBox({
            data: data,
            class: 'form-control',
            key: 'DictionaryId',
            value: 'DicName',
            onChange: function () {
                var selectid = $('#DictionaryId1006A').ysComboBox('getValue');
                loadDictionaryId1006B(selectid);
            }

        });
        if (id > 0 && data.length > 0) {
            var classoneid = $("#hidClassOneId").val();
            if (classoneid != undefined && parseFloat(classoneid) > 0) {
                $("#DictionaryId1006A").ysComboBox('setValue', classoneid);
            }
        }
    }
    function loadDictionaryId1006B(classid) {
        if (classid > 0) {
            var subjectid = $('#DictionaryId1005').ysComboBox('getValue');
            ys.ajax({
                url: '@Url.Content("~/SystemManage/StaticDictionary/GetChildToListJson")' + '?TypeCode=1006&Pid=' + subjectid + '&DictionaryPid=' + classid,
                type: "get",
                success: function (obj) {
                    if (obj.Tag == 1) {
                        loadDictionaryId1006BData(obj.Data);
                    }
                }
            });
        } else {
            loadDictionaryId1006BData({});
        }
    }
    function loadDictionaryId1006BData(data) {
        $("#DictionaryId1006B").ysComboBox({
            data: data,
            class: 'form-control',
            key: 'DictionaryId',
            value: 'DicName'
        });
        if (id > 0 && data.length > 0) {
            var classtwoid = $("#hidClassTwoId").val();
            if (classtwoid != undefined && parseFloat(classtwoid) > 0) {
                $("#DictionaryId1006B").ysComboBox('setValue', classtwoid);
            }
        }
    }
    function getForm() {
        if (id > 0) {
            ys.ajax({
                url: '@Url.Content("~/EvaluateManage/FunRoomEvaluateStandard/GetFormJson")' + '?id=' + id,
                type: 'get',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        $('#form').setWebControls(obj.Data);
                        $("#hidSubjectId").val(obj.Data.DictionaryId1005);
                        $("#hidClassOneId").val(obj.Data.DictionaryId1006A);
                        $("#hidClassTwoId").val(obj.Data.DictionaryId1006B);
                    }
                }
            });
        }
        else {
            var defaultData = {};
            $('#form').setWebControls(defaultData);
        }
    }

    function saveForm(index) {
        if ($('#form').validate().form()) {
            var postData = $('#form').getWebControls({ Id: id });
            var errorMsg = '';
            if (!(parseInt(postData.SchoolStage) > 0)) {
                errorMsg = '请选择学段。<br/>';
            }
            if (!(parseInt(postData.DictionaryId1005) > 0)) {
                errorMsg = '请选择适用学科。<br/>';
            }
            if (!(parseInt(postData.DictionaryId1006A) > 0)) {
                errorMsg = '请选择一级分类。<br/>';
            }
            if (!(parseInt(postData.DictionaryId1006B) > 0)) {
                errorMsg = '请选择二级分类。<br/>';
            }
            if (errorMsg != '') {
                ys.msgError('验证失败，请填写完成再提交！<br/>' + errorMsg);
                return;
            }
            ys.ajax({
                url: '@Url.Content("~/EvaluateManage/FunRoomEvaluateStandard/SaveFormJson")',
                type: 'post',
                data: postData,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        parent.$('#gridTable').ysTable('refresh');parent.resetToolbarStatus();
                        parent.layer.close(index);
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        }
    }
</script>

