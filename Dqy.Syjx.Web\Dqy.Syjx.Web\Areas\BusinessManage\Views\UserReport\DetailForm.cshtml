﻿@{
    Layout = "~/Views/Shared/_FormWhite.cshtml";
}

<div class="container-div">
    <div class="row">
        <div class="btn-group-sm hidden-xs" id="toolbar">
        </div>
        <div class="col-sm-12 select-table table-striped">
            <div class="ibox-title">
                <h5>管理员授权配置</h5>
            </div>
            <div class="card-body">
                <form id="form" class="form-horizontal m">
                    <div class="form-group row">
                        <label class="col-sm-3 control-label ">姓名<font class="red"> </font></label>
                        <div class="col-sm-8">
                            <input id="RealName" col="RealName" type="text" class="form-control" readonly />
                        </div>
                    </div>
                    <div class="form-group row">
                        <label class="col-sm-3 control-label ">是否实验员<font class="red"> </font></label>
                        <div class="col-sm-8">
                            <div id="IsExperimenter" col="IsExperimenter" readonly></div>
                        </div>
                    </div>
                    <div class="form-group row">
                        <label class="col-sm-3 control-label ">实验员性质<font class="red"> </font></label>
                        <div class="col-sm-8">
                            <div id="ExperimenterNature" col="ExperimenterNature" readonly></div>
                        </div>
                    </div>
                    <div class="form-group row">
                        <label class="col-sm-3 control-label ">类别<font class="red"> </font></label>
                        <div class="col-sm-8">
                            <div id="ExperimenterType" col="ExperimenterType" readonly></div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
<script type="text/javascript">
    var id = ys.request("id");
    $(function () {
        $("#IsExperimenter").ysRadioBox({ data: ys.getJson(@Html.Raw(typeof(IsEnum).EnumToDictionaryString())), class:"form-control"});
        $("#ExperimenterNature").ysComboBox({ data: ys.getJson(@Html.Raw(typeof(ExperimenterNatureEnum).EnumToDictionaryString())), class: "form-control" });
        $("#ExperimenterType").ysComboBox({ data: ys.getJson(@Html.Raw(typeof(ExperimenterTypeEnum).EnumToDictionaryString())), class: "form-control"});
        getForm();
        parent.removeConfirm();
    });
    function getForm() {
        if (id > 0) {
            ys.ajax({
                url: '@Url.Content("~/BusinessManage/UserReport/GetFormJson")' + '?id=' + id,
                type: 'get',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        $('#form').setWebControls(obj.Data);
                    }
                }
            });
        }
    }
</script>

