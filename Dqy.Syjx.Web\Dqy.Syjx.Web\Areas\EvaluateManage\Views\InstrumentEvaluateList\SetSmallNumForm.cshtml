﻿@{
    Layout = "~/Views/Shared/_FormWhite.cshtml";
}

<div class="wrapper animated fadeInRight">
    <form id="form" class="form-horizontal m">
        <div class="form-group row">
            <label class="col-sm-3 control-label ">去小数的值（D）</label>
            <div class="col-sm-6">
                <input id="NeglectSmallNum" col="NeglectSmallNum" type="text" class="form-control" />
            </div>
            <div class="col-sm-3">
            </div>
        </div>
    </form>
</div>

<script type="text/javascript">
    $(function () {
        $("#IsEvaluate").ysRadioBox({ data: ys.getJson(@Html.Raw(typeof(IsEnum).EnumToDictionaryString())) });
        $('#form').validate({
            rules: {
                NeglectSmallNum: { required: true, number: true }
            }
        });
    });
    function saveForm(index) {
        if ($('#form').validate().form()) {
            var postData = $('#form').getWebControls();
            if (!(postData.NeglectSmallNum != undefined && postData.NeglectSmallNum > -1)) {
                ys.msgError("请填写调控系数，再提交。");
                return;
            }
            postData = { ids: parent.getSelectIds(), standardid: parent.id, smallnum: postData.NeglectSmallNum };
            ys.ajax({
                url: '@Url.Content("~/EvaluateManage/InstrumentEvaluateList/SetSmallNumFormJson")',
                type: 'post',
                data: postData,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        parent.$('#gridTable').ysTable('refresh');parent.resetToolbarStatus();
                        parent.layer.close(index);
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        }
    }
</script>

