﻿@{
    Layout = "~/Views/Shared/_FormWhite.cshtml";
}

<div class="wrapper animated fadeInRight">
    <form id="form" class="form-horizontal m">
        <div class="form-group row">
            <label class="col-sm-3 control-label ">评估项目名称<font class="red"> *</font></label>
            <div class="col-sm-6">
                <div id="EvaluateProjectId" col="EvaluateProjectId"></div>
            </div>
            <div class="col-sm-2">
                <a class="btn btn-primary btn-sm" onclick="showEditProjectForm()"><i class="fa fa-edit"></i> 修改名称</a>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">学段</label>
            <div class="col-sm-6">
                <div id="SchoolStage" col="SchoolStage"></div>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">适用学科</label>
            <div class="col-sm-6">
                <div id="DictionaryId1005" col="DictionaryId1005"></div>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">版本名称</label>
            <div class="col-sm-6">
                <div id="EvaluateStandardId" col="EvaluateStandardId"></div>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">备注</label>
            <div class="col-sm-6">
                <textarea id="Remark" col="Remark" style="max-width:100%" class="form-control"></textarea>
            </div>
        </div>
    </form>
    <input type="hidden" id="hidSubjectId" value="" />
    <input type="hidden" id="hidEvaluateStandardId" value="" />
    <input type="hidden" id="hidEvaluateProjectId" value="" />
</div>

<script type="text/javascript">
    var id = ys.request("id");
    $(function () {
        loadEvaluateProject();
        $('#SchoolStage').ysComboBox({
            url:  '@Url.Content("~/SystemManage/StaticDictionary/GetListJson")' + '?TypeCode=1002',
            class: 'form-control',
            key: 'DictionaryId',
            value: 'DicName',
            onChange: function () {
                var selectid = $('#SchoolStage').ysComboBox('getValue');
                loadSubject(selectid);
            }
        });
        loadSubject(0);
        loadEvaluateStandard(0);
        getForm();
    });
    function loadEvaluateProject() {
        $("#EvaluateProjectId").ysComboBox({
            url: '@Url.Content("~/EvaluateManage/InstrumentEvaluateProject/GetProjectListJson")',
            class: 'form-control',
            key: 'Id',
            value: 'EvaluateName',
        });
        var hidProjectid = $("#hidEvaluateProjectId").val();
        if (hidProjectid != undefined && parseInt(hidProjectid)>0) {
            $("#EvaluateProjectId").ysComboBox("setValue", hidProjectid);
        }
    }
    function loadSubject(selectid) {
        if (selectid > 0) {
            ys.ajax({
                url: '@Url.Content("~/SystemManage/StaticDictionary/GetChildToListJson")' + '?TypeCode=1005&Pid=' + selectid,
                type: "get",
                success: function (obj) {
                    if (obj.Tag == 1) {
                        loadDictionary1005Data(obj.Data);
                    }
                }
            });
        } else {
            loadDictionary1005Data([]);
        }
    }
    function loadDictionary1005Data(data) {
        $("#DictionaryId1005").ysComboBox({
            data: data,
            class: 'form-control',
            key: 'DictionaryId',
            value: 'DicName',
            onChange: function () {
                var selectid = $('#DictionaryId1005').ysComboBox('getValue');
                loadEvaluateStandard(selectid);
            }

        });
        if (id > 0 && data.length > 0) {
            var subjectid = $("#hidSubjectId").val();
            if (subjectid != undefined && parseFloat(subjectid)>0) {
                $("#DictionaryId1005").ysComboBox('setValue', subjectid);
            }
        }
    }
    function loadEvaluateStandard(subjectid) {
        if (subjectid > 0) {
            var schoolstage = $('#SchoolStage').ysComboBox('getValue');
            ys.ajax({
                url: '@Url.Content("~/EvaluateManage/InstrumentEvaluateStandard/GetListJson")' + '?SchoolStage=' + schoolstage + '&DictionaryId1005=' + subjectid,
                type: "get",
                success: function (obj) {
                    if (obj.Tag == 1) {
                        loadEvaluateStandardData(obj.Data);
                    }
                }
            });
        } else {
            loadEvaluateStandardData([]);
        }
    }
    function loadEvaluateStandardData(data) {
        $("#EvaluateStandardId").ysComboBox({
            data: data,
            class: 'form-control',
            key: 'Id',
            value: 'VersionName'
        });
        if (id > 0 && data.length > 0) {
            var standardid = $("#hidEvaluateStandardId").val();
            if (standardid != undefined && parseFloat(standardid) > 0) {
                $("#EvaluateStandardId").ysComboBox('setValue', standardid);
            }
        }
    }
    function getForm() {
        if (id > 0) {
            ys.ajax({
                url: '@Url.Content("~/EvaluateManage/InstrumentEvaluateProject/GetFormJson")' + '?id=' + id,
                type: 'get',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        $('#form').setWebControls(obj.Data);
                        $("#hidSubjectId").val(obj.Data.DictionaryId1005);
                        $("#hidEvaluateStandardId").val(obj.Data.EvaluateStandardId);
                        $("#hidEvaluateProjectId").val(obj.Data.EvaluateProjectId);
                    }
                }
            });
        }
        else {
            var defaultData = {};
            $('#form').setWebControls(defaultData);
        }
    }
    function saveForm(index) {
        if ($('#form').validate().form()) {
            var postData = $('#form').getWebControls({ Id: id });
            var errorMsg = '';
            if (!(parseInt(postData.EvaluateProjectId) > 0)) {
                errorMsg = '请选择评估项目。<br/>';
            }
            if (!(parseInt(postData.SchoolStage) > 0)) {
                errorMsg = '请选择学段。<br/>';
            }
            if (!(parseInt(postData.DictionaryId1005) > 0)) {
                errorMsg = '请选择适用学科。<br/>';
            }
            if (!(parseInt(postData.EvaluateStandardId) > 0)) {
                errorMsg = '请选择版本名称。<br/>';
            }
            if (errorMsg != '') {
                ys.msgError('验证失败，请填写完成再提交！<br/>' + errorMsg);
                return;
            }
            ys.ajax({
                url: '@Url.Content("~/EvaluateManage/InstrumentEvaluateProject/SaveFormJson")',
                type: 'post',
                data: postData,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        parent.$('#gridTable').ysTable('refresh');parent.resetToolbarStatus();
                        parent.layer.close(index);
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        }
    }

    function showEditProjectForm() {
        var projectid = $("#EvaluateProjectId").ysComboBox('getValue');
        if (!(parseInt(projectid) > 0)) {
            ys.msgError('请选择评估项目,才可修改项目名称！<br/>');
            return;
        }
        $("#hidEvaluateProjectId").val(projectid);
        parent.showAddProjectForm(projectid, loadEvaluateProject);
    }
</script>

