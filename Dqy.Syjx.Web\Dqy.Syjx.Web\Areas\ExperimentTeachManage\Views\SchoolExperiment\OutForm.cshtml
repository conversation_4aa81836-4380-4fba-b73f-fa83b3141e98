﻿@{
    Layout = "~/Views/Shared/_FormWhite.cshtml";
}

<div class="wrapper animated fadeInRight">
    <form id="form" class="form-horizontal m">
        <div class="form-group row">
            <label class="col-sm-2 control-label ">状态<font class="red"> *</font></label>
            <div class="col-sm-8">
                <div id="Statuz" col="Statuz"></div>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-2 control-label ">适用学科<font class="red"> *</font></label>
            <div class="col-sm-8">
                <div id="CourseId" col="CourseId"></div>
            </div>
            <div class="col-sm-2 tag_msg_color">
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-2 control-label ">适用年级<font class="red"> *</font></label>
            <div class="col-sm-8">
                <div id="GradeId"></div>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-2 control-label ">实验类型<font class="red"> *</font></label>
            <div class="col-sm-8">
                <div id="ExperimentType" col="ExperimentType"></div>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-2 control-label ">实验名称<font class="red"> *</font></label>
            <div class="col-sm-8">
                <input id="ExperimentName" col="ExperimentName" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-2 control-label ">主要仪器和耗材<font class="red"> *</font></label>
            <div class="col-sm-8">
                <textarea id="EquipmentNeed" style="max-width:100%" col="EquipmentNeed" type="text" class="form-control"></textarea>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-2 control-label ">学生课前准备<font class="red"> *</font></label>
            <div class="col-sm-8">
                <textarea id="Preparation" style="max-width:100%" col="Preparation" type="text" class="form-control"></textarea>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-2 control-label ">特别事项说明<font class="red"> *</font></label>
            <div class="col-sm-8">
                <textarea id="ItemDescription" style="max-width:100%" col="ItemDescription" type="text" class="form-control"></textarea>
            </div>
        </div>
        <input type="hidden" id="ApplicableGrade" col="ApplicableGrade" value="" />
    </form>
</div>
<input type="hidden" id="hidCourseId" value="0" />
<input type="hidden" id="hidGradeId" value="0" />
<script type="text/javascript">
    var id = ys.request("id");
    var isview = ys.request("v");
    var paramSchoolStage = 0;
    $(function () {
        ysComboboxInit();
         
        getForm();
        $('#form').validate({
            rules: {
                schoolId: { required: true }
            }
        });
    });
    //#region 初始化下拉加载

    function ysComboboxInit() {
        $("#Statuz").ysComboBox({ data: ys.getJson(@Html.Raw(typeof(StatusEnum).EnumToDictionaryString())), class: 'form-control' });

        $('#CourseId').ysComboBox({
            key: 'DictionaryId',
            value: 'DicName',
            class: 'form-control',
            onChange: function () { 
                var courseid = $('#CourseId').ysComboBox("getValue");
                loadGrade(courseid);
            }
        });

        $('#GradeId').ysComboBox({
            key: 'DictionaryId',
            value: 'DicName',
            class: 'form-control'
        });
        $('#ExperimentType').ysComboBox({
            key: 'DictKey',
            value: 'DictValue',
            class: 'form-control'
        }); 
    }

    //#endregion

    function getForm() {
        if (id > 0) {
            ys.ajax({
                url: '@Url.Content("~/ExperimentTeachManage/SchoolExperiment/GetFormJson")' + '?id=' + id,
                type: 'get',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        var courseid = obj.Data.CourseId;
                        delete obj.Data.CourseId;//防止form加载触发改变事件
                        $('#form').setWebControls(obj.Data);
                        loadExperimentType(obj.Data.ExperimentType);
                        loadCourse(courseid);//这个的改变事件会触发年级更新
                    }
                }
            });
        } else {
            loadCourse(); 
            loadGrade();
            loadExperimentType();
        }
    }

    //#region 加载下拉基础数据
    var LastCourseId = -1;//上一次加载年级使用的可成Id,Id不变不重新加载
    function loadGrade(courseid) {
        if (!(courseid != undefined && parseInt(courseid) > 0)) {
            courseid = 0;
        }
        if (LastCourseId != courseid) {
            LastCourseId = courseid;
            ys.ajax({
                url: '@Url.Content("~/SystemManage/StaticDictionary/GetSchoolGradeListByPidJson")' + '?opttype=201&pid=' + courseid,
                type: 'get',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        var html = '';
                        var gradeids = $("#ApplicableGrade").val();
                        console.log(gradeids);
                        if (obj.Data.length == 1) {
                            $("#ApplicableGrade").val(obj.Data[0].DictionaryId);
                            schoolstagez = obj.Data[0].DictionaryId + '';
                        }
                        for (var i = 0; i < obj.Data.length; i++) {
                            html += '<label class="check-box">';
                            if (gradeids.indexOf(obj.Data[i].DictionaryId) > -1) {
                                html += $.Format('<div class="icheckbox-blue checked"><input name="grade_checkbox" type="checkbox" value="{0}" style="position: absolute; opacity: 0;" checked="checked"></div>', obj.Data[i].DictionaryId);
                            } else {
                                html += $.Format('<div class="icheckbox-blue"><input name="grade_checkbox" type="checkbox" value="{0}" style="position: absolute; opacity: 0;"></div>', obj.Data[i].DictionaryId);
                            }
                            html += $.Format('{0}</label>', obj.Data[i].DicName);
                        }
                        $("#GradeId").html(html);
                        setApplicableGrade();
                        $('input[name="grade_checkbox"]').change(function () {
                            if ($(this).prop("checked")) {
                                $(this).parent(".icheckbox-blue").addClass("checked");
                            } else {
                                $(this).parent(".icheckbox-blue").removeClass("checked");
                            }
                            setApplicableGrade();
                        });
                    }
                    else {
                        $("#GradeId").html("");
                        ys.msgError(obj.Message);
                    }
                }
            });
        }
    }
    //记录选中的年级数据。
    function setApplicableGrade() {
        var schoolstages = $('input[name="grade_checkbox"]:checked').map(function () { return this.value }).get().join(',');
        $("#ApplicableGrade").val(schoolstages);
    }
    function loadCourse(defaultValue) {
        ys.ajax({
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetSchoolUserCourseListJson")',
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1) {
                    bindCourseData(obj.Data, defaultValue);
                } else {
                    bindCourseData([]);
                }
            }
        });
    } 
    function bindCourseData(data, defaultValue) {
        $('#CourseId').ysComboBox({
            data: data,
            key: 'DictionaryId',
            value: 'DicName',
            class: 'form-control',
        });
        if (defaultValue != undefined && parseInt(defaultValue) > 0) $('#CourseId').ysComboBox('setValue', defaultValue);
    }


    function loadExperimentType(defaultValue) {
        ys.ajax({
            url: '@Url.Content("~/SystemManage/DataDictDetail/GetSortListJson")' + '?TypeCode=OET101001',
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1) {
                    bindExperimentTypeData(obj.Data, defaultValue);
                } else {
                    bindExperimentTypeData([]);
                }
            }
        });
    }

    function bindExperimentTypeData(data, defaultValue) {
        $('#ExperimentType').ysComboBox({
            class: 'form-control',
            data: data,
            key: 'DictKey',
            value: 'DictValue'
        });
        if (defaultValue > 0) {
            $('#ExperimentType').ysComboBox("setValue", defaultValue); 
        }
    }
     
    //#endregion
     
    function saveForm(index) {
        if ($('#form').validate().form()) {
            var postData = $('#form').getWebControls({ Id: id });
            //验证必填项。
            var errMsg = '';
            if (!(postData.Statuz != undefined && postData.Statuz > 0)) {
                errMsg += '请选择实验状态.</br>';
            }
            if (!(postData.CourseId != undefined && postData.CourseId > 0)) {
                errMsg += '请选择适用学科.</br>';
            }
            if (!(postData.ApplicableGrade != undefined && postData.ApplicableGrade.length > 1)) {
                errMsg += '请选择适用年级.</br>';
            }
            if (!(postData.ExperimentName != undefined && postData.ExperimentName.length > 1)) {
                errMsg += '请填写实验名称，并且实验名称字符请控制在2至50.</br>';
            }
            if (!(postData.ExperimentType != undefined && postData.ExperimentType > 0)) {
                errMsg += '请选择实验类型。</br>';
            }
            if (!(postData.EquipmentNeed != undefined && postData.EquipmentNeed.length >= 1)) {
                errMsg += '请填写主要仪器和耗材，并且主要仪器和耗材字符请控制在500以内.</br>';
            }
            if (!(postData.Preparation != undefined && postData.Preparation.length >= 1)) {
                errMsg += '请填写学生课前准备，并且学生课前准备字符请控制在500以内.</br>';
            }
            if (!(postData.ItemDescription != undefined && postData.ItemDescription.length >= 1)) {
                errMsg += '请填写特别事项说明，并且特别事项说明字符请控制在500以内.</br>';
            }
            if (errMsg != '') {
                layer.msg(errMsg, { icon: 2, time: 3000, btn: ['关闭'], yes: function () { layer.closeAll(); }, area: ['400px'] });
                return false;
            }

            ys.ajax({
                url: '@Url.Content("~/ExperimentTeachManage/SchoolExperiment/SaveOutFormJson")',
                type: 'post',
                data: postData,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        parent.searchGrid();
                        parent.layer.close(index);
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        }
    }
</script>

