﻿@{
    Layout = "~/Views/Shared/_FormWhite.cshtml";
}
@inject Microsoft.AspNetCore.Hosting.IWebHostEnvironment HostingEnvironment
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/uploadifive/File.min.css"))
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/uploadifive/File.min.js"))

@section header{
    <style type="text/css">
        .card {
            width: 260px;
            height: 260px;
            font-family: '宋体';
            border: 1px solid #ceccca;
            font-size: 16px;
            text-align:center;
            margin-left:80px;
        }

        .card ul{
            margin-top: 20px;
        }

        .headQrcode {
            width: 100%;
            height: 80px;
            line-height: 80px;
            display: flex;
            margin: 50px 0 20px 0;
            justify-content: center;
        }

        .head-left {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            overflow: hidden;
        }

            .head-left img {
                width: 100%;
                height: 100%;
            }

        .head-right {
            font-size: 36px;
            font-weight: bold;
            padding: 0 20px;
        }

        .section {
            width: 100%;
            display: flex;
            text-align:center;
        }

        /*.spanNameText{
           margin-top:5px;
        }*/

        .section-left {
            width: 180px;
            height: 180px;
        }

            .section-left img {
                width: 100%;
                height: 100%;
                margin-left:40px;
            }

        /*.spanQrcodeText {
            display: block;
            width: 65px;
            text-align: justify;
        }

            .spanQrcodeText::after {
                display: inline-block;
                width: 100%;
                content: "";
            }*/
    </style>
    @* 使用文档参考：      http://html2canvas.hertzen.com*@
    @BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/html2canvas/html2canvas.min.js"), true)

}
<div class="wrapper animated fadeInRight">
    <form id="form" class="form-horizontal m">
        <input type="hidden" id="Id" col="Id" value="0" />
       <div class="form-group row">
            <div class="btn-group-sm" id="ptoolbar">
                <a class="btn btn-success downqrcode" onclick="downHtmlToImage()"><i class="fa fa-download"></i> 下载</a>
                <a class="btn btn-primary" onclick="printPage()"><i class="fa fa-print"></i> 打印</a>
            </div>
        </div>

         <div class="card" id="divQrCodeInfo">
            <div>
                <ul>
                    <li><span id="spanName" class="spanNameText"></span></li>
                    <li>
                        <div class="section-left" id="divQrCode">
                        </div>
                    </li>
                    <li><span id="spanQRCode"></span></li>
                </ul>
            </div>
        </div>
    </form>
</div>
<script type="text/javascript">
    var id = ys.request("id");
    $(function () {
       getForm();
    });
    function getForm() {
        if (id > 0) {
            ys.ajax({
                url: '@Url.Content("~/InstrumentManage/SchoolInstrument/GetQRCodeJson")' + '?id=' + id,
                type: 'get',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        var d = obj.Data;
                     
                        $('#spanName').html(d.Name);
                        $('#divQrCode').append('<img  modal="zoomImg"  src="' + d.QrCodeData + '" />');
                       
                        imgages.showextalink();
                        $("#spanQRCode").html(d.QRCode);
                        //$('#spanCourseName').html(d.Course);
                        //$('#spanCode').html(d.Code);
                        //$('#spanStoragePlace').html(d.StoragePlace);
                        //$("#unitLogo").attr("src", d.Logo);
                    }
                }
            });
        }
    }

    //打印Div中的内容
    function printPage() {
        $("#ptoolbar").hide();
        window.print();
        $("#ptoolbar").show();
    }

    function downHtmlToImage() {

        html2canvas(document.querySelector("#divQrCodeInfo")).then(canvas => {
            //document.body.appendChild(canvas)
            const link = document.createElement('a'); // 创建一个超链接对象实例
            const event = new MouseEvent('click'); // 创建一个鼠标事件的实例
            link.download = $('#spanName').text() + '.png'; // 设置要下载的图片的名称
            link.href = canvas.toDataURL(); // 将图片的URL设置到超链接的href中
            link.dispatchEvent(event); // 触发超链接的点击事件

        });
    }

</script>
