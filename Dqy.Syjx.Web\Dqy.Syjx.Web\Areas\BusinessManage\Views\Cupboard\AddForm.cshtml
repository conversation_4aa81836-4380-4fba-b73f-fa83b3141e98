﻿@{
    Layout = "~/Views/Shared/_FormWhite.cshtml";
}

<div class="wrapper animated fadeInRight">
    <form id="form" class="form-horizontal m">
        <div class="form-group row">
            <label class="col-sm-3 control-label ">实验（专用）室名称<font class="red"> *</font></label>
            <div class="col-sm-7">
                <div id="Id" col="Id"></div>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">橱柜数量</label>
            <div class="col-sm-7">
                <input id="Num" col="Num" type="text" class="form-control" />
            </div>
            <div class="col-sm-2 tag_msg_color">
                <span class="glyphicon glyphicon-question-sign titleprompt inputprompt" aria-hidden="true" data-container="body" data-toggle="tooltip" data-placement="right" data-content="一次最多创建10个橱柜，如需创建更多，请多次创建。"></span>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label " id="labModel">规格参数</label>
            <div class="col-sm-7">
                <input id="Model" col="Model" type="text" class="form-control" />
            </div>
            <div class="col-sm-2 tag_msg_color">
                <span class="glyphicon glyphicon-question-sign titleprompt inputprompt" aria-hidden="true" data-container="body" data-toggle="tooltip" data-placement="right" data-content="如果每个橱柜的规格参数不一致，请在创建后修改"></span>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label " id="labModel"></label>
            <div class="col-sm-7">
                <span style="color:red;">备注：创建成功后，请按“实验室名称”搜索查询。</span>
            </div> 
        </div>
    </form>
</div>

<script type="text/javascript">
    $(function () {
        loadFunRoom();
        $('#form').validate({
            rules: {
                Num: { required: true, number: true}
            }
        });
        $(".inputprompt").popover({
            trigger: 'hover',
            placement: 'left',
            html: true
        });
    });

    function loadFunRoom() {
        ys.ajax({
            url: '@Url.Content("~/BusinessManage/FunRoom/GetListByUserId")',
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1) {
                    $('#Id').ysComboBox({ data: obj.Data, class: 'form-control', key: 'Id', value: 'RoomNatureName' });
                    if (obj.Data.length == 1) {
                        $("#Id").ysComboBox('setValue', obj.Data[0].Id);
                    }
                } else {
                    $('#Id').ysComboBox({ data: [], class: 'form-control', key: 'Id', value: 'RoomNatureName' });
                    ys.msgError(obj.Message);
                }
            }
        });
    }

    function saveForm(index) {
        if ($('#form').validate().form()) {
            var postData = $('#form').getWebControls();
            if (!(parseInt(postData.Id) > 0)) {
                ys.msgError("请选择实验（专用）室名称。");
                return;
            }
            if (!(parseInt(postData.Num)>0)) {
                ys.msgError("橱柜数量必须大于0。");
                return;
            }
            if (parseInt(postData.Num) > 10) {
                ys.msgError("橱柜数量一次最多创建10个。");
                return;
            }
            ys.ajax({
                url: '@Url.Content("~/BusinessManage/Cupboard/SaveAddFormJson")',
                type: 'post',
                data: postData,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        parent.$('#gridTable').ysTable('refresh');parent.resetToolbarStatus();
                        parent.layer.close(index);
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        }
    }


    //#region
    //加载配置验证信息
    function loadFormVali() {
        ys.ajax({
            url: '@Url.Content("~/SystemManage/ConfigSet/GetListByCode")',
            type: 'Post',
            data: { typecode: '2SYSG-1CJWH-2CGGL' },
            success: function (obj) {
                if (obj.Tag == 1) {
                    var validateRule = {};
                    var validateMessage = {}; 
                    if (obj.Data != undefined && obj.Data.length > 0) {
                        for (var i = 0; i < obj.Data.length; i++) {
                            if (obj.Data[i].ConfigValue == "1") {
                                var typecode = obj.Data[i].TypeCode.replaceAll("2SYSG-1CJWH-2CGGL-", "");
                                var verifyhint = "";
                                if (obj.Data[i].VerifyHint != undefined && obj.Data[i].VerifyHint.length > 0) {
                                    verifyhint = obj.Data[i].VerifyHint;
                                }
                              
                                if (typecode == "Model") {
                                    $("#lab" + typecode).append('<font class="red"> *</font>');
                                    validateRule.Model = { required: true };
                                    validateMessage.Model = { required: verifyhint };
                                }
                            }
                        }
                    }
                    $('#form').validate({
                        rules: validateRule,
                        messages: validateMessage
                    });
                }
            }
        });
    }

        //#endregion
</script>