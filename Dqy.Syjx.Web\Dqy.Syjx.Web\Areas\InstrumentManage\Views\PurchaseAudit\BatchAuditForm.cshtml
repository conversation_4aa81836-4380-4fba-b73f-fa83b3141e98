﻿@{
    Layout = "~/Views/Shared/_FormWhite.cshtml";
}
<style type="text/css">
    .check-box {
        width: 100px;
    }

    .iradio-box {
        width: 100px;
    }

    .iradio-box {
        position: initial;
        display: inline-block;
    }

    .iradio-blue {
        position: inherit;
        display: inline-block;
        /*  top: 6px;*/
    }
    .col-sm-8{padding-top:7px;}
</style>
<div class="wrapper animated fadeInRight">
    <div class="row">
        <form id="form" class="form-horizontal m">
            <div class="form-group row">
                <label class="col-sm-2 control-label ">审批结果<font class="red"> *</font></label>
                <div class="col-sm-8">
                    <div id="approvalStatuz" col="ApprovalStatuz"></div>
                </div>
                <div class="col-sm-2"> </div>
            </div>
            <div class="form-group row">
                <label class="col-sm-2 control-label ">审批意见</label>
                <div class="col-sm-8">
                    <textarea id="approvalRemark" style="max-width:100%" col="ApprovalRemark" class="form-control"></textarea>
                </div>
                <div class="col-sm-2"> </div>
            </div>
        </form>
    </div>
</div>
<script type="text/javascript">
    var ids = ys.request("ids");
    $(function () {
        loadApprovalStatuz();
    });
    function loadApprovalStatuz(statuz) {
        var html = '';
        html += '<label class="iradio-box"><div class="iradio-blue pass" style="vertical-align:bottom;"><input name="inputType_checkbox" type="radio" value="@InstrumentAuditStatuzEnum.End.ParseToInt()" style="position: absolute; opacity: 0;"></div> 通过</label>';
        html += '<label class="iradio-box"><div class="iradio-blue nopass" style="vertical-align:bottom;"><input name="inputType_checkbox" type="radio" value="@InstrumentAuditStatuzEnum.SchoolAuditPass.ParseToInt()" style="position: absolute; opacity: 0;"></div> 不通过</label>';
        $("#approvalStatuz").html(html);

        $('input[name="inputType_checkbox"]').change(function () {
            if ($(this).prop("checked")) {
                var val = $(this).val();
                if (val % 2 == 0) {
                    $(".pass").addClass("checked");
                    $(".nopass").removeClass("checked");
                    if ($('#approvalRemark').val() == '')
                        $('#approvalRemark').val('同意');

                } else {
                    $(".nopass").addClass("checked");
                    $(".pass").removeClass("checked");
                    if ($('#approvalRemark').val() == '同意')
                        $('#approvalRemark').val('');
                }
            }
        });
    }
    function saveForm(index) {
        var statuz = $('input[name="inputType_checkbox"]:checked').val();
        if (statuz > 0) {
            var remark = $('#approvalRemark').val();
            if (statuz % 2 == 1 && remark == '') {
                ys.msgError('不通过必须填写审批意见！');
                return false;
            }
            var postData = {
                Ids: ids,
                ApprovalStatuz: statuz,
                ApprovalRemark: $('#approvalRemark').val()
            };
            ys.ajax({
                url: '@Url.Content("~/InstrumentManage/PurchaseAudit/SaveFormJson")',
                type: 'post',
                data: postData,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        parent.$('#gridTable').ysTable('refresh');parent.resetToolbarStatus();
                        parent.layer.close(index);
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        }
        else {
            ys.msgError('请选择审批结果！');
        }
    }
</script>

