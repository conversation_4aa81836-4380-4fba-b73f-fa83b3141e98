﻿@{
    Layout = "~/Views/Shared/_FormWhite.cshtml";
}
<div class="wrapper animated fadeInRight">
    <form id="form" class="form-horizontal m"> 
        <div class="form-group row">
            <label class="col-sm-3 control-label ">功能室<font class="red"> *</font></label>
            <div class="col-sm-7">
                <div id="FunRoomId" col="FunRoomId"></div>
            </div>
            <div class="col-sm-2">

            </div>
        </div>  
    </form>
</div>
<script type="text/javascript">
    var id = ys.request("id");
    $(function () {
        loadFunRoom();
    });
    function loadFunRoom() {
        ys.ajax({
            url: '@Url.Content("~/CameraManage/SchoolCamera/GetFunRoomListJson")' + '?Id=' + id,
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1) {
                    loadFunRoomData(obj.Data);
                } else {
                    loadFunRoomData([]);
                    ys.msgError(obj.Message);
                }
            }
        });
    }
    function loadFunRoomData(data) {
        $('#FunRoomId').ysComboBox({
            data: data,
            class: 'form-control',
            key: 'Id',
            value: 'AddressName',
            onChange: function () {
            }
        });
        if (data != undefined && data.length == 1) {
            $("#FunRoomId").ysComboBox('setValue', data.Id);
        }
    }
    function saveForm(index) {
        if ($('#form').validate().form()) {
            var postData = $('#form').getWebControls({ Id: id }); 
            var errorMsg = '';
            if (!(parseInt(postData.FunRoomId) > 0)) {
                errorMsg = '请选择功能室。<br/>';
            }  
            if (errorMsg != '') {
                ys.msgError('验证失败，请填写完成再提交！<br/>' + errorMsg);
                return;
            }
            ys.ajax({
                url: '@Url.Content("~/CameraManage/SchoolCamera/SetRelationForm")',
                type: 'post',
                data: postData,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        parent.$('#gridTable').ysTable('refresh');parent.resetToolbarStatus();
                        parent.layer.close(index);
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        } else {
            ys.msgError('验证失败，请填写完成再提交！');
        }
    } 
</script>

