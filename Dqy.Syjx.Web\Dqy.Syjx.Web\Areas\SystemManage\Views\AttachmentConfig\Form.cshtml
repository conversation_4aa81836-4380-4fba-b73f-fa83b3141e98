﻿@{
    Layout = "~/Views/Shared/_FormWhite.cshtml";
}

<div class="wrapper animated fadeInRight">
    <form id="form" class="form-horizontal m">
        <input type="hidden" id="Id" col="Id" value="0" />
        <div class="form-group row">
            <label class="col-sm-3 control-label ">表名<font class="red"> *</font></label>
            <div class="col-sm-8">
                <input id="OwnTableName" col="OwnTableName" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">分组编码</label>
            <div class="col-sm-8">
                <input id="GroupCode" col="GroupCode" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">名称<font class="red"> *</font></label>
            <div class="col-sm-8">
                <input id="Name" col="Name" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">附件分类<font class="red"> *</font></label>
            <div class="col-sm-8">
                <input id="FileCategory" col="FileCategory" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">说明</label>
            <div class="col-sm-8">
                <input id="remark" col="Remark" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">最大文件个数<font class="red"> *</font></label>
            <div class="col-sm-8">
                <input id="MaxFileNumber" col="MaxFileNumber" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">至少上传文件个数<font class="red"> *</font></label>
            <div class="col-sm-8">
                <input id="LeastFileNumber" col="LeastFileNumber" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">上传对象<font class="red"> *</font></label>
            <div class="col-sm-8">
                <div id="UploadUserType" col="UploadUserType" ></div>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">上传文件大小<font class="red"> *</font></label>
            <div class="col-sm-8">
                <input id="UploadFileSize" col="UploadFileSize" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">上传文件类型扩展名<font class="red"> *</font></label>
            <div class="col-sm-8">
                <input id="UploadFileExt" col="UploadFileExt" type="text" class="form-control" placeholder="扩展名格式必须为.jpg|.jpeg|.gif|.png" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">上传文件类型扩展名编码<font class="red"> *</font></label>
            <div class="col-sm-8">
                <input id="UploadFileExtCode" col="UploadFileExtCode" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">存放位置<font class="red"> *</font></label>
            <div class="col-sm-8">
                <input id="SaveDir" col="SaveDir" type="text" class="form-control" />
            </div>
        </div>
    </form>
</div>

<script type="text/javascript">
    var id = ys.request("id");
    $(function () {
        $("#UploadUserType").ysComboBox({ data: ys.getJson(@Html.Raw(typeof(UnitTypeEnum).EnumToDictionaryString())), class: "form-control"});
        getForm();

        $('#form').validate({
            rules: {
                OwnTableName: { required: true },
                GroupCode: { required: true },
                Name: { required: true },
                FileCategory: { required: true, number: true,min:1000,max:9999 },
                MaxFileNumber: { required: true ,number: true },
                LeastFileNumber: { number: true },
                UploadFileSize: { required: true ,number: true },
                UploadFileExt: { required: true },
                SaveDir: { required: true },
            },
            messages: {
                OwnTableName: { required:'请填写表名' },
                GroupCode: { required: '请填写分组编码' },
                Name: { required: '请填写名称' },
                FileCategory: { required: '请填写附件分类', number: '附件分类必须为1000~~9999的整数', min: '附件分类必须为1000~~9999的整数', max: '附件分类必须为1000~~9999的整数' },
                MaxFileNumber: { required: '请填写最大文件个数', number: '请填写最大文件个数' },
                LeastFileNumber: { required: '请填写至少上传文件个数', number: '请填写至少上传文件个数' },
                UploadFileSize: { required: '请填写上传文件大小', number: '请填写上传文件大小' },
                UploadFileExt: { required: '请填写上传文件类型扩展名' },
                SaveDir: { required: '请填写上传文件类型扩展名' },
            }
        });
    });

    function getForm() {
        if (id > 0) {
            ys.ajax({
                url: '@Url.Content("~/SystemManage/AttachmentConfig/GetFormJson")' + '?id=' + id,
                type: 'get',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        $('#form').setWebControls(obj.Data);
                    }
                }
            });
        }
        else {
            var defaultData = { Id: 0, LeastFileNumber: 0, UploadFileSize: 10, UploadFileExt: ".JPG|.JPEG|.PNG|.GIF|.BMP|.PDF|.DOC|.DOCX|.XLS|.XLSX|.DWG" };
            $('#form').setWebControls(defaultData);
        }
    }

    function saveForm(index) {
        if ($('#form').validate().form()) {
            var postData = $('#form').getWebControls({ Id: id });
            //单独验证下拉框。
            var msg = ''; 
            if (isNaN(postData.UploadUserType) || parseInt(postData.UploadUserType) < 0) {
                msg += '请选择上传对象！';
            }
            if (msg != '') {
                ys.msgError(msg);
                return;
            }
            ys.ajax({
                url: '@Url.Content("~/SystemManage/AttachmentConfig/SaveFormJson")',
                type: 'post',
                data: postData,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        parent.$('#gridTable').ysTable('refresh');parent.resetToolbarStatus();
                        parent.layer.close(index);
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        }
    }
</script>

