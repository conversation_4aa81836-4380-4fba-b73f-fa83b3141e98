﻿@{
    Layout = "~/Views/Shared/_FormWhite.cshtml";
}

<div class="wrapper animated fadeInRight">
    <form id="form" class="form-horizontal m">
        <div class="form-group row">
            <label class="col-sm-3 control-label">是否需要 新增</label>
            <div class="col-sm-8" id="addStatus" ref="btnAdd"></div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label">是否需要 修改</label>
            <div class="col-sm-8" id="editStatus" ref="btnEdit"></div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label">是否需要 删除</label>
            <div class="col-sm-8" id="deleteStatus" ref="btnDelete"></div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label">是否需要 导入</label>
            <div class="col-sm-8" id="importStatus" ref="btnImport"></div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label">是否需要 导出</label>
            <div class="col-sm-8" id="exportStatus" ref="btnExport"></div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label">是否需要 设置1</label>
            <div class="col-sm-8" id="set1Status" ref="btnSet1"></div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label">是否需要 设置2</label>
            <div class="col-sm-8" id="set2Status" ref="btnSet2"></div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label">是否需要 设置3</label>
            <div class="col-sm-8" id="set3Status" ref="btnSet3"></div>
        </div>

    </form>
</div>

<script type="text/javascript">
    $(function () {

        var btnAdd = $("#btnAdd", parent.document);
        var btnEdit = $("#btnEdit", parent.document);
        var btnDelete = $("#btnDelete", parent.document);
        var btnImport = $("#btnImport", parent.document);
        var btnExport = $("#btnExport", parent.document);
        var btnSet1 = $("#btnSet1", parent.document);
        var btnSet2 = $("#btnSet2", parent.document);
        var btnSet3 = $("#btnSet3", parent.document);

        $("#addStatus").ysRadioBox({ data: ys.getJson(@Html.Raw(typeof(NeedEnum).EnumToDictionaryString())) });
        $("#editStatus").ysRadioBox({ data: ys.getJson(@Html.Raw(typeof(NeedEnum).EnumToDictionaryString())) });
        $("#deleteStatus").ysRadioBox({ data: ys.getJson(@Html.Raw(typeof(NeedEnum).EnumToDictionaryString())) });
        $("#importStatus").ysRadioBox({ data: ys.getJson(@Html.Raw(typeof(NeedEnum).EnumToDictionaryString())) });
        $("#exportStatus").ysRadioBox({ data: ys.getJson(@Html.Raw(typeof(NeedEnum).EnumToDictionaryString())) });
        $("#set1Status").ysRadioBox({ data: ys.getJson(@Html.Raw(typeof(NeedEnum).EnumToDictionaryString())) });
        $("#set2Status").ysRadioBox({ data: ys.getJson(@Html.Raw(typeof(NeedEnum).EnumToDictionaryString())) });
        $("#set3Status").ysRadioBox({ data: ys.getJson(@Html.Raw(typeof(NeedEnum).EnumToDictionaryString())) });

        $('input').on('ifChecked', function (event) {
            var ref = $(this).attr("ref");
            var val = $(this).val();
            if (val == 1) {
                $("#" + ref, parent.document).show();
            }
            else {
                $("#" + ref, parent.document).hide();
            }
        });

        // 设置默认值
        if (btnAdd.is(':hidden')) {
            $("#addStatus").ysRadioBox('setValue',0);
        }
        else {
            $("#addStatus").ysRadioBox('setValue',1);
        }

        if (btnEdit.is(':hidden')) {
            $("#editStatus").ysRadioBox('setValue',0);
        }
        else {
            $("#editStatus").ysRadioBox('setValue',1);
        }

        if (btnDelete.is(':hidden')) {
            $("#deleteStatus").ysRadioBox('setValue',0);
        }
        else {
            $("#deleteStatus").ysRadioBox('setValue',1);
        }

        if (btnImport.is(':hidden')) {
            $("#importStatus").ysRadioBox('setValue', 0);
        }
        else {
            $("#importStatus").ysRadioBox('setValue', 1);
        }

        if (btnExport.is(':hidden')) {
            $("#exportStatus").ysRadioBox('setValue', 0);
        }
        else {
            $("#exportStatus").ysRadioBox('setValue', 1);
        }

        if (btnSet1.is(':hidden')) {
            $("#set1Status").ysRadioBox('setValue', 0);
        }
        else {
            $("#set1Status").ysRadioBox('setValue', 1);
        }

        if (btnSet2.is(':hidden')) {
            $("#set2Status").ysRadioBox('setValue', 0);
        }
        else {
            $("#set2Status").ysRadioBox('setValue', 1);
        }

        if (btnSet3.is(':hidden')) {
            $("#set3Status").ysRadioBox('setValue', 0);
        }
        else {
            $("#set3Status").ysRadioBox('setValue', 1);
        }
    });


</script>
