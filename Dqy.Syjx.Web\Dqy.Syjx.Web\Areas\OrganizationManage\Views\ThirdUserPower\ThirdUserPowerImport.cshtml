﻿
@{
    Layout = "~/Views/Shared/_FormWhite.cshtml";
}

@inject Microsoft.AspNetCore.Hosting.IWebHostEnvironment HostingEnvironment
@section header{
    @BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/fileinput/5.0.3/css/fileinput.min.css"))
    @BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/fileinput/5.0.3/js/fileinput.min.js"))
}


<div class="wrapper animated fadeInRight">
    <form id="form" class="form-horizontal m">
        <div class="form-group row">
            <label class="col-sm-2 control-label ">选择文件</label>
            <div class="col-sm-10">
                <input id="importFile" type="file">
            </div>
        </div>

        <div class="form-group row">
            <label class="col-sm-2 control-label "></label>
            <div class="col-sm-4">
                <div class="control-label" style="text-align:left">
                    <a href='@Url.Content("~/template/第三方登录授权信息.xlsx")' class="btn btn-secondary btn-xs"><i class="fa fa-file-excel-o"></i> 下载模板</a>
                </div>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-2 control-label "></label>
            <div class="col-sm-10 text-danger">
                提示：仅允许导入“xls”或“xlsx”格式文件！
            </div>
        </div>
    </form>
</div>

<script type="text/javascript">
    var filePath = undefined;
    $(document).ready(function () {
        $("#isOverride").ysCheckBox({
            data: [{ Key: '1', Value: '是否更新已经存在的用户数据' }]
        });

        $("#importFile").fileinput({
            language: 'zh',
            'uploadUrl': '@Url.Content("~/File/UploadExcel")' + '?fileModule=@UploadFileType.Import.ParseToInt()',
            showPreview: false,
            allowedFileExtensions: ['xls', 'xlsx']
        }).on("fileuploaded", function (event, data) {
            var obj = data.response;
            if (obj.Tag == 1) {
                filePath = obj.Data;
            }
            else {
                filePath = '';
            }
        });
    });

    function saveForm(index) {
        if (!filePath) {
            ys.alertError('文件未上传或者上传失败');
            return;
        }

        var postData =$("#form").getWebControls();
        postData.FilePath = filePath;
        ys.ajax({
            url: '@Url.Content("~/OrganizationManage/ThirdUserPower/ImportUnitUserJson")',
            type: "post",
            data: postData,
            success: function (obj) {
                if (obj.Tag == 1) {
                    ys.msgSuccess(obj.Description);
                    setTimeout(function () {
                        parent.$('#gridTable').ysTable('refresh');parent.resetToolbarStatus();
                        parent.layer.close(index);
                    }, 1000);
                   
                }
                else {
                    
                    var objData = obj.Data;
                    if (objData != null && objData.length > 0) {
                        parent.layer.close(index);

                        var divHtml = "";
                        var layerTitle = "";
                        if (obj.Tag == 2) {
                            divHtml = "<div id='tipMsg'><table class='table'><thead><tr><th style='width:100px;'>单位名称</th><th style='width:120px;'>人员姓名</th>";
                            divHtml += "<th style='width:60px;'>手机号码</th><th style='width:100px;'>单位类型</th><th style='width:100px;'>角色</th></tr></thead>";
                            divHtml += "<tbody>";

                            objData.forEach(function (value, index, array) {
                                divHtml += "<tr>";
                                divHtml += $.Format("<td>{0}</td><td>{1}</td><td>{2}</td><td>{3}</td><td>{4}</td>", value.UnitName == null ? "" : value.UnitName, value.UserName == null ? "" : value.UserName, value.Mobile == null ? "" : value.Mobile, value.UnitTypeName == null ? "" : value.UnitTypeName, value.RoleNames == null ? "" : value.RoleNames);
                                divHtml += "</tr>";
                            });
                            divHtml += "</tbody></table></div>";
                            var layerTitle = $.Format("导入失败用户信息<span style='color:red;'>【<span style='color:red;margin-top:20px;text-align:center;'>{0}</span>】</span>", obj.Description);

                        } else {
                            divHtml = "<div id='tipMsg'><table class='table'><thead><tr><th style='width:60px;'>行号</th><th>错误信息</th><th style='width:100px;'>单位名称</th><th style='width:120px;'>人员姓名</th>";
                            divHtml += "<th style='width:60px;'>手机号码</th><th style='width:100px;'>单位类型</th><th style='width:100px;'>角色</th></tr></thead>";
                            divHtml += "<tbody>";

                            objData.forEach(function (value, index, array) {
                                divHtml += "<tr>";
                                divHtml += $.Format("<td style='text-align:center;'>{0}</td><td  style='color:red;'>{1}</td><td>{2}</td><td>{3}</td><td>{4}</td><td>{5}</td><td>{6}</td>", value.Number, value.ErrorMsg == null ? "" : value.ErrorMsg, value.UnitName == null ? "" : value.UnitName, value.UserName == null ? "" : value.UserName, value.Mobile == null ? "" : value.Mobile, value.UnitTypeName == null ? "" : value.UnitTypeName, value.RoleNames == null ? "" : value.RoleNames);
                                divHtml += "</tr>";
                            });
                            divHtml += "</tbody></table></div>";
                            var layerTitle = $.Format("导入失败用户信息<span style='color:red;'>（备注：行号对应Execl表行）【<span style='color:red;margin-top:20px;text-align:center;'>{0}</span>】</span>", obj.Description);

                        }
                        parent.layer.open({
                            title: layerTitle,
                            type: 1,
                            skin: 'layui-layer-demo',
                            closeBtn: false,
                            shift: 2,
                            area: ["900px", "500px"],
                            btn: ['关闭'],
                            shadeClose: false,
                            content: divHtml,
                            closeBtn: 1,
                        });


                    } else {
                        ys.alertError(obj.Message);
                    }

                }
            }
        });
    }

</script>