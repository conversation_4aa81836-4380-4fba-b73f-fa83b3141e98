﻿@{
    Layout = "~/Views/Shared/_FormWhite.cshtml";
}

<div class="wrapper animated fadeInRight">
    <form id="form" class="form-horizontal m">
        <div class="form-group row">
            <div class="col-sm-4">
                <div id="tableFieldTree" class="ztree"></div>
            </div>
            <div class="col-sm-8">
                <div class="row">
                    <label class="col-sm-3 control-label">是否需要分页</label>
                    <div class="col-sm-8" id="paginationStatus"></div>
                </div>
                <div style="border-bottom:1px solid rgb(221,221,221)"></div>
            </div>
        </div>
        <div class="form-group row">
        </div>
    </form>
</div>

<script type="text/javascript">
    var tableName = ys.request("tableName");
    var tableNameUpper = ys.request("tableNameUpper");

    $(function () {

        loadTableFieldTree();
        $("#paginationStatus").ysRadioBox({ data: ys.getJson(@Html.Raw(typeof(NeedEnum).EnumToDictionaryString())) });

        var divPagination = $("#divPagination", parent.document);
        $('input').on('ifChecked', function (event) {
            var val = $(this).val();
            if (val == @NeedEnum.Need.ParseToInt()) {
                divPagination.show();
            }
            else {
                divPagination.hide();
            }
        });

        //默认设置
        if (divPagination.is(':hidden')) {
            $("#paginationStatus").ysRadioBox('setValue',@NeedEnum.NotNeed.ParseToInt());
        }
        else {
            $("#paginationStatus").ysRadioBox('setValue',@NeedEnum.Need.ParseToInt());
        }
        var selectedColumn = '';
        $("#gridTable th", parent.document).each(function (i, ele) {
            if (i == 0) {
                selectedColumn = $(ele).html();
            }
            else {
                selectedColumn += ",";
                selectedColumn += $(ele).html();
            }
        });
        $('#tableFieldTree').ysTree("setCheckedNodesByName", selectedColumn);
    });

    function loadTableFieldTree() {
        $('#tableFieldTree').ysTree({
            url: '@Url.Content("~/ToolManage/CodeGenerator/GetTableFieldTreePartListJson")' + '?tableName=' + tableName + '&upper=1',
            maxHeight: "400px",
            async: false,
            check: { enable: true },
            expandLevel: 0,
            callback: {
                onCheck: function (event, treeId, treeNode) {
                    $("#gridTable th", parent.document).each(function (i, ele) {
                        $(ele).remove();
                    });
                    var fields = $('#tableFieldTree').ysTree("getCheckedNodes", 'name');
                    $.each(fields.split(','), function (i, val) {
                        if (val != tableNameUpper) {
                            $("#gridTable tr", parent.document).append("<th>" + val + "</th>");
                        }
                    });
                }
            }
        });
    }
</script>
