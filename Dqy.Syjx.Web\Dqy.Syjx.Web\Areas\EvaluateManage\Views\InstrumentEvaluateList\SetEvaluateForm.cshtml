﻿@{
    Layout = "~/Views/Shared/_FormWhite.cshtml";
}

<div class="wrapper animated fadeInRight">
    <form id="form" class="form-horizontal m">
        <div class="form-group row">
            <label class="col-sm-3 control-label ">是否评估</label>
            <div class="col-sm-6">
                <div id="IsEvaluate" col="IsEvaluate"></div>
            </div>
            <div class="col-sm-3">
            </div>
        </div>
    </form>
</div>

<script type="text/javascript">
    $(function () {
        $("#IsEvaluate").ysRadioBox({ data: ys.getJson(@Html.Raw(typeof(IsEnum).EnumToDictionaryString()))});
    });
    function saveForm(index) {
        if ($('#form').validate().form()) {
            var postData = $('#form').getWebControls();
            if (postData.IsEvaluate != 0 && postData.IsEvaluate != 1) {
                ys.msgError("请选择是否评估，再提交。");
                return;
            }
            postData = { ids: parent.getSelectIds(), standardid: parent.id, isevaluate: postData.IsEvaluate };
            ys.ajax({
                url: '@Url.Content("~/EvaluateManage/InstrumentEvaluateList/SetEvaluateFormJson")',
                type: 'post',
                data: postData,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        parent.$('#gridTable').ysTable('refresh');parent.resetToolbarStatus();
                        parent.layer.close(index);
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        }
    }
</script>

