﻿@{ Layout = "~/Views/Shared/_FormWhite.cshtml"; }
@inject Microsoft.AspNetCore.Hosting.IWebHostEnvironment HostingEnvironment
<div class="wrapper animated fadeInRight">
    <form id="form" class="form-horizontal m">
        <div class="form-group row">
            <label class="col-sm-3 control-label ">登录名称</label>
            <div class="col-sm-8">
                <input id="userName" col="UserName" type="text" class="form-control" readonly="readonly" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">密码</label>
            <div class="col-sm-8">
                <input id="password" col="Password" type="password" autocomplete="new-password"  class="form-control" />
            </div>
        </div>
    </form>
</div>
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/md5/js/md5.min.js"))
<script type="text/javascript">
    var id = ys.request("id");
    $(function () {
        getForm(id);

        $("#form").validate({
            rules: {
                password: {
                    required: true,
                    minlength: 8,
                    maxlength: 20,
                    isLoginpass: true
                }
            }
        });
    });

    function getForm() {
        if (id > 0) {
            ys.ajax({
                url: '@Url.Content("~/OrganizationManage/User/GetFormJson")' + '?id=' + id,
                type: "get",
                success: function (obj) {
                    if (obj.Tag == 1) {
                        var result = obj.Data;
                        result.Password = "";
                        $("#form").setWebControls(result);
                    }
                }
            });
        }
    }

    function saveForm(index) {
        if ($("#form").validate().form()) {
            var postData = $("#form").getWebControls({ Id: id });
            postData.Password = $.md5(postData.Password);
            ys.ajax({
                url: '@Url.Content("~/OrganizationManage/User/ResetPasswordJson")',
                type: "post",
                data: postData,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        parent.layer.close(index);
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        }
    }
</script>

