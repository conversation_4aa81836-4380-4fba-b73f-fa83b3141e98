﻿@{
    Layout = "~/Views/Shared/_FormWhite.cshtml";
}

<div class="wrapper animated fadeInRight">
    <form id="form" class="form-horizontal m">
        <div class="form-group row">
            <label class="col-sm-3 control-label "></label>
            <div class="col-sm-5">

            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">测评时长（分钟）：<font class="red"> *</font></label>
            <div class="col-sm-4">
                <input id="Duration" col="Duration" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">备注：</label>
            <div class="col-sm-8">
                <input id="Remark" col="Remark" type="text" class="form-control" />
            </div>
        </div>
    </form>
</div>

<script type="text/javascript">
    var id = ys.request("id");
    var paperids = ys.request("paperIds");
    $(function () {
        getForm();
    });

    function getForm() {
        if (id > 0) {
            ys.ajax({
                url: '@Url.Content("~/PaperExamineManage/Examine/GetFormJson")' + '?id=' + id,
                type: 'get',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        $('#form').setWebControls(obj.Data);
                    }
                }
            });
        }
        else {
            var defaultData = {};
            $('#form').setWebControls(defaultData);
        }
    }

    function saveForm(index) {
        var msg = '';
        if ($('#form').validate().form()) {
            var postData = $('#form').getWebControls({ Id: id });
            postData.PaperIds = paperids;
            if (!(postData.Duration != undefined && postData.Duration.length > 0)) {
                msg += '请填写测评时长！<br/>';
            } else if (isNaN(postData.Duration)) {
                msg += '测评时长请填写正确的数字！<br/>';
            }

            if (msg != '') {
                ys.msgError("验证失败<br/>" + msg);
                return;
            }
            ys.ajax({
                url: '@Url.Content("~/PaperExamineManage/Examine/SaveExamineJson")',
                type: 'post',
                data: postData,
                success: function (obj) {
                    if (obj.Tag == 1) {
                       // ys.msgSuccess(obj.Message);
                    }
                    else {
                       // ys.msgError(obj.Message);
                    }
                    parent.$('#gridTable').ysTable('refresh');parent.resetToolbarStatus();
                    parent.layer.close(index);
                }
            });
        }
    }
</script>

