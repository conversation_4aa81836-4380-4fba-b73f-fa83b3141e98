﻿@{
    Layout = "~/Views/Shared/_FormWhite.cshtml";
}
@inject Microsoft.AspNetCore.Hosting.IWebHostEnvironment HostingEnvironment
@section header{
    <link href="@Url.Content("~/lib/summernote/0.8.15/summernote.min.css")" rel="stylesheet" type="text/css">
    <script src='@Url.Content("~/lib/summernote/0.8.15/summernote.js")' type="text/javascript"></script>
    <script src='@Url.Content("~/lib/summernote/0.8.15/lang/summernote-zh-CN.min.js")' type="text/javascript"></script>

    @BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/imageupload/1.0/css/imgup.min.css"))
    @BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/imageupload/1.0/js/imgup.min.js"))
    @BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/bootstrap.tagsinput/0.8.0/bootstrap-tagsinput.min.css"))
    @BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/bootstrap.tagsinput/0.8.0/bootstrap-tagsinput.min.js"))
    @BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/uploadifive/File.min.css"))
    @BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/uploadifive/File.min.js"))
}
<div class="wrapper animated fadeInRight">
    <form id="form" class="form-horizontal m">
        <div class="form-group row" id="divSchoolName" style="display:none;">
            <label class="col-sm-3 control-label ">单位名称</label>
            <div class="col-sm-8">
                <input id="SchoolName" col="SchoolName" type="text" class="form-control" readonly />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">培训名称</label>
            <div class="col-sm-8">
                <input id="Name" col="Name" type="text" class="form-control" readonly />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">培训课时（小时）</label>
            <div class="col-sm-8">
                <input id="SchoolHour" col="SchoolHour" type="text" class="form-control" readonly />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">培训日期</label>
            <div class="col-sm-8">
                <input id="TrainDate" col="TrainDate" type="text" class="form-control" readonly />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">培训地点</label>
            <div class="col-sm-8">
                <input id="TrainAddresz" col="TrainAddresz" type="text" class="form-control" readonly />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">组织单位</label>
            <div class="col-sm-8">
                <input id="TrainInstitution" col="TrainInstitution" type="text" class="form-control" readonly />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">主要培训内容</label>
            <div class="col-sm-8">
                <textarea id="TrainContent" col="TrainContent" class="form-control" style="height:60px" readonly></textarea>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">培训证书</label>
            <div class="col-sm-8">
                <div>
                    <div style="height: 55px;display:none;" id="fileQueue"></div>
                </div>
                &nbsp;&nbsp;
                <div id="spanFile_1100">


                </div>
            </div>
        </div>

        <div class="form-group row">
            <label class="col-sm-3 control-label ">附件</label>
            <div class="col-sm-8">
                <div>
                    <div style="height: 55px;display:none;" id="fileQueue"></div>
                </div>
                &nbsp;&nbsp;
                <div id="spanFile_1110">


                </div>
            </div>
        </div>

        <div class="form-group row">
            <label class="col-sm-3 control-label ">认证结果</label>
            <div class="col-sm-8" id="Statuz" col="Statuz"></div>
        </div>

        <div class="form-group row">
            <label class="col-sm-3 control-label ">认证意见</label>
            <div class="col-sm-8">
                <textarea id="AuthSuggestion" col="AuthSuggestion" class="form-control" style="height:60px"></textarea>
            </div>
        </div>
    </form>
</div>

<script type="text/javascript">
    var id = ys.request("id");
    var schoolName = decodeURI(ys.request("schoolName"));
    $(function () {

        getForm();

        $("#Statuz").ysRadioBox({
            data: ys.getJson(@Html.Raw(typeof(AuthPassEnum).EnumToDictionaryString())),
            class: 'form-control'
        });

    });


    function getForm() {
        
        if (id > 0) {
            ys.ajax({
                url: '@Url.Content("~/PersonManage/UserTrainInfo/GetFormJson")' + '?id=' + id,
                type: 'get',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        obj.Data.TrainDate = obj.Data.TrainDate.substring(0, 10);
                        $('#form').setWebControls(obj.Data);

                        //调用显示附件信息
                        var fileList = obj.Data.Atts;

                        if (fileList.length > 0) {

                            for (var i = 0; i < fileList.length; i++) {
                                var id = fileList[i].Id;
                                var filePath = fileList[i].Path;
                                var fileExt = fileList[i].Ext.toUpperCase();
                                var fileTitle = fileList[i].Title;
                                var fileFileCategory = fileList[i].FileCategory;
                                var objDiv = $("#spanFile_" + fileFileCategory);

                                if (fileExt == ".JPG" || fileExt == ".BMP" || fileExt == ".JPEG" || fileExt == ".PNG" || fileExt == ".GIF") {
                                    objDiv.append('<span class="keywords" idValue="' + id +'"><a  modal="zoomImg"  href="javascript:void(0);" src="' + filePath + '" >' + fileTitle + '</a></span>');
                                } else {
                                    objDiv.append('<span class="keywords" idValue="' + id +'"><a target="_blank" href="' + filePath + '" >' + fileTitle + '</a></span>');
                                }
                            }

                            imgages.showextalink();
                        }

                        if (schoolName != null && schoolName != "") {
                            $("#divSchoolName").show();
                            $("#SchoolName").val(schoolName);
                        } else {
                            $("#divSchoolName").hide();
                        }

                        $("#AuthSuggestion").val("");
                    }
                }
            });
        }
        else {
            var defaultData = {};
            $('#form').setWebControls(defaultData);
        }
        
    }

    function saveForm(index) {
        var postData = $('#form').getWebControls({ Id: id });
        if (postData.Statuz == "") {
            ys.msgError("请选择认证结果");
            return;
        }
        if (postData.Statuz == 3 && postData.AuthSuggestion == "") {
            ys.msgError("认证不通过必须填写不通过原因");
            return;
        }
        ys.ajax({
            url: '@Url.Content("~/PersonManage/UserTrainInfo/SaveAuthFormJson")',
            type: 'post',
            data: postData,
            success: function (obj) {
                if (obj.Tag == 1) {
                    ys.msgSuccess(obj.Message);
                    parent.$('#gridTable').ysTable('refresh');parent.resetToolbarStatus();
                    parent.layer.close(index);
                }
                else {
                    ys.msgError(obj.Message);
                }
            }
        });
    }
</script>

