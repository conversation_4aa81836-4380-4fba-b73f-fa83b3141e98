﻿@{
    Layout = "~/Views/Shared/_FormWhite.cshtml";
}

<div class="wrapper animated fadeInRight">
    <form id="form" class="form-horizontal m">
        <div class="form-group row">
            <label class="col-sm-3 control-label ">CompulsoryType<font class="red"> *</font></label>
            <div class="col-sm-8">
                <input id="compulsoryType" col="CompulsoryType" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">OptionalGroupNum</label>
            <div class="col-sm-8">
                <input id="optionalGroupNum" col="OptionalGroupNum" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">OptionalShowNum</label>
            <div class="col-sm-8">
                <input id="optionalShowNum" col="OptionalShowNum" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">NeedGroupNum</label>
            <div class="col-sm-8">
                <input id="needGroupNum" col="NeedGroupNum" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">NeedShowNum</label>
            <div class="col-sm-8">
                <input id="needShowNum" col="NeedShowNum" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">Num</label>
            <div class="col-sm-8">
                <input id="num" col="Num" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">SchoolTerm</label>
            <div class="col-sm-8">
                <input id="schoolTerm" col="SchoolTerm" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">SchoolYearEnd</label>
            <div class="col-sm-8">
                <input id="schoolYearEnd" col="SchoolYearEnd" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">SchoolYearStart</label>
            <div class="col-sm-8">
                <input id="schoolYearStart" col="SchoolYearStart" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">CourseId</label>
            <div class="col-sm-8">
                <input id="courseId" col="CourseId" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">GradeId</label>
            <div class="col-sm-8">
                <input id="gradeId" col="GradeId" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">SchoolStage</label>
            <div class="col-sm-8">
                <input id="schoolStage" col="SchoolStage" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">UnitId</label>
            <div class="col-sm-8">
                <input id="unitId" col="UnitId" type="text" class="form-control" />
            </div>
        </div>
    </form>
</div>

<script type="text/javascript">
    var id = ys.request("id");
    $(function () {
        getForm();

        $('#form').validate({
            rules: {
                compulsoryType: { required: true }
            }
        });
    });

    function getForm() {
        if (id > 0) {
            ys.ajax({
                url: '@Url.Content("~/ExperimentTeachManage/PlanExamParameter/GetFormJson")' + '?id=' + id,
                type: 'get',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        $('#form').setWebControls(obj.Data);
                    }
                }
            });
        }
        else {
            var defaultData = {};
            $('#form').setWebControls(defaultData);
        }
    }

    function saveForm(index) {
        if ($('#form').validate().form()) {
            var postData = $('#form').getWebControls({ Id: id });
            ys.ajax({
                url: '@Url.Content("~/ExperimentTeachManage/PlanExamParameter/SaveFormJson")',
                type: 'post',
                data: postData,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        parent.searchGrid();
                        parent.layer.close(index);
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        }
    }
</script>

