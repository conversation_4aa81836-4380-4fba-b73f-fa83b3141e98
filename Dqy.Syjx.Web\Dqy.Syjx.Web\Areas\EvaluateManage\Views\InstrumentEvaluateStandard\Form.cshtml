﻿@{
    Layout = "~/Views/Shared/_FormWhite.cshtml";
}

<div class="wrapper animated fadeInRight">
    <form id="form" class="form-horizontal m">
        <div class="form-group row">
            <label class="col-sm-3 control-label ">版本名称<font class="red"> *</font></label>
            <div class="col-sm-8">
                <input id="VersionName" col="VersionName" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">学段<font class="red"> *</font></label>
            <div class="col-sm-8">
                <div id="SchoolStage" col="SchoolStage"></div>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">适用学科<font class="red"> *</font></label>
            <div class="col-sm-8">
                <div id="DictionaryId1005" col="DictionaryId1005"></div>
            </div>
        </div>
    </form>
    <input type="hidden" id="hidSubjectId" value="" />
</div>

<script type="text/javascript">
    var id = ys.request("id");
    $(function () {
        $('#SchoolStage').ysComboBox({
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetListJson")' + '?TypeCode=1002',
            class: 'form-control',
            key: 'DictionaryId',
            value: 'DicName',
            onChange: function () {
                var selectid = $('#SchoolStage').ysComboBox('getValue');
                loadSubject(selectid);
            }
        });
        loadSubject(0);
        getForm();

        $('#form').validate({
            rules: {
                VersionName: { required: true }
            }
        });
    });
    function loadSubject(selectid) {
        if (selectid > 0) {
            ys.ajax({
                url: '@Url.Content("~/SystemManage/StaticDictionary/GetChildToListJson")' + '?TypeCode=1005&Pid=' + selectid,
                type: "get",
                success: function (obj) {
                    if (obj.Tag == 1) {
                        loadDictionary1005Data(obj.Data);
                    } else {
                        loadDictionary1005Data([]);
                    }
                }
            });
        } else {
            loadDictionary1005Data([]);
        }
    }
    function loadDictionary1005Data(data) {
        $("#DictionaryId1005").ysComboBox({
            data: data,
            class: 'form-control',
            key: 'DictionaryId',
            value: 'DicName'
        });
        if (id > 0 && data.length > 0) {
            var subjectid = $("#hidSubjectId").val();
            if (subjectid != undefined && parseFloat(subjectid)>0) {
                $("#DictionaryId1005").ysComboBox('setValue', subjectid);
            }
        }
    }
    function getForm() {
        if (id > 0) {
            ys.ajax({
                url: '@Url.Content("~/EvaluateManage/InstrumentEvaluateStandard/GetFormJson")' + '?id=' + id,
                type: 'get',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        $('#form').setWebControls(obj.Data);
                        $("#hidSubjectId").val(obj.Data.DictionaryId1005);
                    }
                }
            });
        }
        else {
            var defaultData = {};
            $('#form').setWebControls(defaultData);
        }
    }

    function saveForm(index) {
        if ($('#form').validate().form()) {
            var postData = $('#form').getWebControls({ Id: id });
            ys.ajax({
                url: '@Url.Content("~/EvaluateManage/InstrumentEvaluateStandard/SaveFormJson")',
                type: 'post',
                data: postData,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        parent.$('#gridTable').ysTable('refresh');parent.resetToolbarStatus();
                        parent.layer.close(index);
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        }
    }
</script>

