﻿@{
    Layout = "~/Views/Shared/_FormWhite.cshtml";
}
@inject Microsoft.AspNetCore.Hosting.IWebHostEnvironment HostingEnvironment
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/uploadifive/File.min.css"))
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/uploadifive/File.min.js"))
<style type="text/css">
    .layui-laydate {
        top: 10px !important;
    }

    .tag_msg_color {
        color: #999;
    }
</style>
<div class="wrapper animated fadeInRight">
    <form id="form" class="form-horizontal m">
        <div class="form-group row">
            <label class="col-sm-2 control-label ">登记人</label>
            <div class="col-sm-6">
                <span id="RealName" class="form-control"></span>
            </div>
            <div class="col-sm-4">

            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-2 control-label ">任课学段</label>
            <div class="col-sm-6">
                <span id="SchoolStage" class="form-control"></span>
            </div>
            <div class="col-sm-4">

            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-2 control-label ">任课学科</label>
            <div class="col-sm-6">
                <span id="SubjectName" class="form-control"></span>
            </div>
            <div class="col-sm-4">

            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-2 control-label ">任课班级</label>
            <div class="col-sm-6">
                <span id="UseClassName" class="form-control"></span>
            </div>
            <div class="col-sm-4 tag_msg_color">
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-2 control-label ">使用地点</label>
            <div class="col-sm-6">
                <span id="Address" class="form-control"></span>
            </div>
            <div class="col-sm-4 tag_msg_color">
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-2 control-label ">使用日期</label>
            <div class="col-sm-6">
                <span id="UseDate" class="form-control"></span>
            </div>
            <div class="col-sm-4">

            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-2 control-label ">课程节次</label>
            <div class="col-sm-6">
                <span id="SectionName" class="form-control"></span>
            </div>
            <div class="col-sm-4 tag_msg_color">

            </div>
        </div>
        <div class="form-group row naturetype">
            <label class="col-sm-2 control-label ">上课内容</label>
            <div class="col-sm-6">
                <span id="ClassContent" class="form-control"></span>
            </div>
            <div class="col-sm-4 tag_msg_color">
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-2 control-label ">现场照片</label>
            <div class="col-sm-6">
                <div id="spanFile"></div>
            </div>
            <div class="col-sm-4 tag_msg_color">
            </div>
        </div>
        <div class="form-group row naturetype">
            <label class="col-sm-2 control-label ">设备运行状态</label>
            <div class="col-sm-6" style="padding-top:6px;">
                <span id="Statuz"></span>
            </div>
            <div class="col-sm-4 tag_msg_color">
            </div>
        </div>
        <div class="form-group row" id="divRepair">
            <label class="col-sm-2 control-label ">问题描述</label>
            <div class="col-sm-6">
                <span id="RepairContent" class="form-control"></span>
            </div>
            <div class="col-sm-4 tag_msg_color">
            </div>
        </div> 
    </form>
</div>
<script type="text/javascript">
    var id = ys.request("id");
    $(function () {
        parent.removeConfirm();
        getForm();
    });
    function getForm() {
        if (id > 0) {
            ys.ajax({
                url: '@Url.Content("~/QueryStatisticsManage/FunRoomUse/GetDetailJson")' + '?id=' + id,
                type: 'get',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        $("#RealName").text(obj.Data.RealName);
                        $("#SchoolStage").text(obj.Data.SchoolStage);
                        $("#SubjectName").text(obj.Data.SubjectName);
                        $("#UseClassName").text(obj.Data.UseClassName);
                        $("#Address").text(obj.Data.Name);
                        $("#UseArea").text(obj.Data.UseArea);
                        $("#ClassContent").text(obj.Data.ClassContent);
                        $("#SectionName").text(obj.Data.SectionName);
                        $("#RepairContent").text(obj.Data.RepairContent);
                        if (obj.Data.Statuz==1) {
                            $("#Statuz").text("正常");
                            $("#divRepair").hide();
                        } else {
                            $("#Statuz").text("不正常");
                        }
                        if (isNaN(obj.Data.UseDate) && obj.Data.UseDate.length > 10) {
                            $("#UseDate").text(obj.Data.UseDate.substring(0,10));
                        }
                        var objDiv = $("#spanFile");
                        objDiv.html("--");
                        //调用显示附件信息
                        if (isNaN(obj.Data.AttachmentList) && obj.Data.AttachmentList != undefined) {
                            var fileList = obj.Data.AttachmentList;
                            if (fileList.length > 0) {
                                 //先清空
                                objDiv.html("");
                                for (var i = 0; i < fileList.length; i++) {
                                    var id = fileList[i].Id;
                                    var filePath = fileList[i].Path;
                                    var fileExt = fileList[i].Ext.toUpperCase();
                                    var fileTitle = fileList[i].Title;
                                    if (fileExt == ".JPG" || fileExt == ".BMP" || fileExt == ".JPEG" || fileExt == ".PNG" || fileExt == ".GIF") {
                                        objDiv.append('<span class="keywords" idValue="' + id + '"><a  modal="zoomImg"  href="javascript:void(0);" src="' + filePath + '" >' + fileTitle + '</a></span>');
                                    } else {
                                        objDiv.append('<span class="keywords" idValue="' + id + '"><a target="_blank" href="' + filePath + '" >' + fileTitle + '</a></span>');
                                    }
                                }
                                imgages.showextalink();
                            }
                        }
                    }
                }
            });
        }
        else {
            var defaultData = {Id:0};
            $('#form').setWebControls(defaultData);
        }
    }
</script>

