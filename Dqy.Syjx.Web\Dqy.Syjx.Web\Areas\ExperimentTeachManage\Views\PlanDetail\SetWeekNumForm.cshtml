﻿@{
 Layout = "~/Views/Shared/_FormWhite.cshtml";
}
<div class="wrapper animated fadeInRight">
    <form id="form" class="form-horizontal m">
        <div class="form-group row">
            <label class="col-sm-3 control-label ">周次<font class="red"> *</font></label>
            <div class="col-sm-7">
                <div id="WeekNum" col="WeekNum"></div>
            </div>
            <div class="col-sm-2"> </div>
        </div>
    </form>
</div>

<script type="text/javascript">
    var id = ys.request("id");
    var weekNum = ys.request("num");
    $(function () {
        loadWeekNum();
    });
    function loadWeekNum() {
        var arr = [];
        for (var i = 1; i <= 30; i++) {
            arr.push({ id: i, name: i + ''  })
        }
        $('#WeekNum').ysComboBox({
            data: arr,
            class: 'form-control',
            key: 'id',
            value: 'name'
        });
        if (weekNum != undefined && parseInt(weekNum) > 0) {
            $('#WeekNum').ysComboBox("setValue", weekNum);
        }
    }
    function saveForm(index) {
        if ($('#form').validate().form()) {
            var postData = $('#form').getWebControls({ Id: id });
            //验证
            var errorMsg = '';
            if (!(postData.WeekNum > 0)) {
                errorMsg += '请选择周次。<br/>';
            }
            if (errorMsg != '') {
                ys.msgError(errorMsg);
                return;
            }
            postData = { id: id, num: postData.WeekNum };
            ys.ajax({
                url: '@Url.Content("~/ExperimentTeachManage/PlanDetail/SaveWeekNumFormJson")',
                type: 'post',
                data: postData,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        parent.$('#gridTable').ysTable('refresh');parent.resetToolbarStatus();
                        parent.layer.close(index);
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        }
    }
</script>