﻿

@{
    Layout = "~/Views/Shared/_FormWhite.cshtml";
}
@inject Microsoft.AspNetCore.Hosting.IWebHostEnvironment HostingEnvironment
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/uploadifive/File.min.css"))
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/uploadifive/File.min.js"))

<div class="wrapper animated fadeInRight">
    <form id="form" class="form-horizontal m">
        <div class="form-group row" id="divSchoolName" style="display:none;">
            <label class="col-sm-3 control-label ">单位名称</label>
            <div class="col-sm-8">
                <input id="SchoolName" col="SchoolName" type="text" class="form-control" readonly />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">类别</label>
            <div class="col-sm-8">
                <input id="CategoryName" col="CategoryName" type="text" class="form-control" readonly />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">等级</label>
            <div class="col-sm-8">
                <input id="LevelName" col="LevelName" type="text" class="form-control" readonly />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">获奖日期</label>
            <div class="col-sm-8">
                <input id="AwardDate" col="AwardDate" type="text" class="form-control" readonly />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">备注</label>
            <div class="col-sm-8">
                <textarea id="Remark" style="max-width:100%" col="Remark" class="form-control" style="height:60px" readonly></textarea>
            </div>
        </div>

        <div class="form-group row">
            <label class="col-sm-3 control-label ">获奖证书</label>
            <div class="col-sm-8">
                <div>
                    <div style="height: 55px;display:none;" id="fileQueue"></div>
                </div>
                &nbsp;&nbsp;
                <div>
                    <div id="spanFile" style="padding: 10px 0px;"></div>
                    <a modal="zoomImg" href="javascript:void(0);" id="awardSrc">
                        <img modal="zoomImg" href="javascript:void(0);" src="" id="imgAward" class="col-sm-8" style="padding: 10px 0px;" />
                    </a>
                </div>
            </div>
        </div>
    </form>
</div>

<script type="text/javascript">
    var id = ys.request("id");
    var schoolName = decodeURI(ys.request("schoolName"));
    $(function () {
        getForm();

    });


    function getForm() {

        if (id > 0) {
            ys.ajax({
                url: '@Url.Content("~/QueryStatisticsManage/UserInfo/GetFormJson")' + '?id=' + id,
                type: 'get',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        $('#form').setWebControls(obj.Data);
                        var AwardDate = ys.formatDate(obj.Data.AwardDate, "yyyy-MM-dd");
                        $("#AwardDate").val(AwardDate);
                        var attachmentid = obj.Data.AttachmentId;
                        var filePath = obj.Data.Path;
                        var fileExt = obj.Data.Ext.toUpperCase();
                        var fileTitle = obj.Data.Title;
                        if (fileExt == ".JPG" || fileExt == ".BMP" || fileExt == ".JPEG" || fileExt == ".PNG" || fileExt == ".GIF") {
                            $("#imgAward").attr("src", obj.Data.Path);
                            $("#awardSrc").attr("src", obj.Data.Path);
                            imgages.showextalink();
                            $("#spanFile").remove();
                        } else {
                            $("#spanFile").append('<span class="keywords" idValue="' + attachmentid + '"><a target="_blank" href="' + filePath + '" >' + fileTitle + '</a></span>');
                            $("#awardSrc").remove();
                        }
                        if (schoolName != null && schoolName != "") {
                            $("#divSchoolName").show();
                            $("#SchoolName").val(schoolName);
                        } else {
                            $("#divSchoolName").hide();
                        }
                    }
                }
            });
        }
        else {
            var defaultData = {};
            $('#form').setWebControls(defaultData);
        }
    }
</script>

