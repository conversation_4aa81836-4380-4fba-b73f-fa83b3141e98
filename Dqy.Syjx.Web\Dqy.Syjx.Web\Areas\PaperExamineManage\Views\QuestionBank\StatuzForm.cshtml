﻿@{
    Layout = "~/Views/Shared/_FormWhite.cshtml";
}

<div class="wrapper animated fadeInRight">
    <form id="form" class="form-horizontal m">
        <div class="form-group row">
            <div class="col-sm-10">
                <label class="col-sm-2 control-label " style="color:red;">注：只对本单位添加的题目设置才有效。</label>
            </div>
        </div>
        <div class="form-group row">
            <div class="col-sm-10">
                <label class="col-sm-2 control-label ">题目状态：</label>
                <div id="Statuz" col="Statuz" style="display: inline-block;"></div>
            </div>
        </div>
    </form>
</div>
@section footer{
    <script type="text/javascript">
        var ids = ys.request("ids");
        $(function () {
            $("#Statuz").ysRadioBox({ data: ys.getJson(@Html.Raw(typeof(StatusEnum).EnumToDictionaryString())), class: "form-control" });
        });

        function saveForm(index) {
            if ($('#form').validate().form()) {
                var statuz = $("#Statuz input:checked").val();
                if (!(statuz!=undefined && statuz > 0)) {
                    ys.msgError("请选择需要设置的状态！");
                    return false;
                }
                var postData = { ids: ids, statuz: statuz };
                ys.ajax({
                    url: '@Url.Content("~/PaperExamineManage/QuestionBank/SaveStatuFormJson")',
                    type: 'post', 
                    data: postData,
                    success: function (obj) {
                        if (obj.Tag == 1) {
                            ys.msgSuccess(obj.Message);
                            parent.$('#gridTable').ysTable('refresh');parent.resetToolbarStatus();
                            parent.layer.close(index);
                        }
                        else {
                            ys.msgError(obj.Message);
                        }
                    }
                });
            }
        }
    </script>
}

