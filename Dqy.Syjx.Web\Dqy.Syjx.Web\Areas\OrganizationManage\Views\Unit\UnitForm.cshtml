﻿@{
    Layout = "~/Views/Shared/_FormWhite.cshtml";
}
<div class="wrapper animated fadeInRight">
    <form id="form" class="form-horizontal m">
        <div class="form-group row">
            <label class="col-sm-3 control-label ">单位代码<font class="red"> *</font></label>
            <div class="col-sm-9">
                <input id="Code" col="Code" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">单位名称<font class="red"> *</font></label>
            <div class="col-sm-9">
                <input id="Name" col="Name" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">社会统一信用代码</label>
            <div class="col-sm-9">
                <input id="OrganizationCode" col="OrganizationCode" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">所属地区</label>
            <div class="col-sm-9">
                <div id="AreaId" col="AreaId"></div>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">详细地址</label>
            <div class="col-sm-9">
                <input id="Address" col="Address" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">单位性质：<font class="red"> *</font></label>
            <div class="col-sm-9">
                <div id="SchoolNature" col="SchoolNature" style="display:inline-block;"></div>
            </div>
        </div>
        <div class="form-group row">
            @await Html.PartialAsync("/Areas/OrganizationManage/Shared/SchoolPropFormpartial.cshtml", new ViewDataDictionary(this.ViewData) { { "Div", "6" }, { "Label", "3" }, { "Content", "3" } })
            <label class="col-sm-3 control-label ">教职工数（人）<font class="red"></font></label>
            <div class="col-sm-3">
                <input id="TeacherNum" col="TeacherNum" type="text" data-suffix="BLL" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">占地面积（㎡）<font class="red"></font></label>
            <div class="col-sm-3">
                <input id="FloorArea" col="FloorArea" type="text" data-suffix="BLL" class="form-control" />
            </div>
            <label class="col-sm-3 control-label ">建筑面积（㎡）<font class="red"></font></label>
            <div class="col-sm-3">
                <input id="BuildArea" col="BuildArea" type="text" data-suffix="Service" class="form-control" />
            </div>
        </div>
    </form>
</div>

<script type="text/javascript">
    var id = ys.request("id");
    $(function () {
        $("#SchoolNature").ysRadioBox({ data: ys.getJson(@Html.Raw(typeof(SchoolNatureEnum).EnumToDictionaryString())) });
        $('#AreaId').ysComboBoxTree({ url: '@Url.Content("~/SystemManage/Area/GetZtreeAreaListJson")' });
        $("#form").validate({
            rules: {
                Code: { required: true },
                Name: { required: true },
                SchoolNature: { required: true },
                SchoolProp: { required: true }
            },
            messages: {
                Code: "请填写单位代码。",
                Name: "请填写单位名称。",
                SchoolNature: "请选择单位性质。",
                SchoolProp: "请选择单位属性。"
            }
        });
        getForm();
    });

    function getForm() {
        if (id > 0) {
            ys.ajax({
                url: '@Url.Content("~/OrganizationManage/Unit/GetFormJson")' + '?id=' + id,
                type: 'get',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        $('#form').setWebControls(obj.Data);
                    }
                }
            });
        }
        else {
            var defaultData = {};
            $('#form').setWebControls(defaultData);
        }
    }

    function saveForm(index) {
        if ($('#form').validate().form()) {
            var postData = $('#form').getWebControls({ Id: id });
            postData.AreaId = ys.getLastValue(postData.AreaId);
             var errorMsg = '';
            if (!(postData.Code != undefined && postData.Code.length >= 4)) {
                errorMsg += '请填写单位代码，至少4位字符。<br/>';
            }
            if (!(postData.Name != undefined && postData.Name.length >= 2)) {
                errorMsg += '请填写单位名称，名称至少2个字符。<br/>';
            }
            if (!(parseInt(postData.SchoolNature) > 0)) {
                errorMsg += '请选择单位性质。<br/>';
            }
            if (!(parseInt(postData.SchoolProp) > 0)) {
                errorMsg += '请选择单位属性。<br/>';
            }
            if (errorMsg != '') {
                ys.msgError('验证失败，请填写完成再提交！<br/>' + errorMsg);
                return;
            }
            ys.ajax({
                url: '@Url.Content("~/OrganizationManage/Unit/SaveFormJson")',
                type: 'post',
                data: postData,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        parent.$('#gridTable').ysTable('refresh');parent.resetToolbarStatus();
                        parent.layer.close(index);
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        }
    }
</script>

