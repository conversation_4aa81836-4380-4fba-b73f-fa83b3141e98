﻿@{
    Layout = "~/Views/Shared/_Index.cshtml";
}
@inject Microsoft.AspNetCore.Hosting.IWebHostEnvironment HostingEnvironment
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/fileinput/5.0.3/css/fileinput.min.css"))
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/fileinput/5.0.3/js/fileinput.min.js"))
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/uploadifive/File.min.css"))
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/uploadifive/File.min.js"))

<div class="wrapper animated fadeInRight">
    <div class="col-sm-12 select-table table-striped">
        <form id="form" class="form-horizontal m">
            <div class="form-group row">
                <label class="col-sm-2 control-label ">适用学段</label>
                <div class="col-sm-8">
                    <input id="stage" col="Stage" type="text" class="form-control" readonly />
                </div>
            </div>
            <div class="form-group row">
                <label class="col-sm-2 control-label ">适用学科</label>
                <div class="col-sm-8">
                    <input id="course" col="Course" type="text" class="form-control" readonly />
                </div>
            </div>
            <div class="form-group row">
                <label class="col-sm-2 control-label ">分类代码</label>
                <div class="col-sm-8">
                    <input type="text" class="form-control" id="code" col="Code" readonly />
                </div>
            </div>
            <div class="form-group row">
                <label class="col-sm-2 control-label ">仪器名称</label>
                <div class="col-sm-8">
                    <input id="name" col="Name" type="text" class="form-control" readonly />
                </div>
            </div>
            <div class="form-group row">
                <label class="col-sm-2 control-label ">规格属性</label>
                <div class="col-sm-8">
                    @*<input id="model" col="Model" type="text" class="form-control" readonly />*@
                    <textarea id="Model" col="Model" type="text" class="form-control" readonly></textarea>
                </div>
            </div>
            <div class="form-group row">
                <label class="col-sm-2 control-label ">数量</label>
                <div class="col-sm-8">
                    <input id="num" col="Num" type="text" class="form-control" readonly />
                </div>
            </div>
            <div class="form-group row">
                <label class="col-sm-2 control-label ">单位</label>
                <div class="col-sm-8">
                    <input id="unitName" col="UnitName" type="text" class="form-control" readonly />
                </div>
            </div>
            
            <div class="form-group row">
                <label class="col-sm-2 control-label ">单价(元)</label>
                <div class="col-sm-8">
                    <input id="price" col="Price" type="text" class="form-control" readonly />
                </div>
            </div>
            <div class="form-group row">
                <label class="col-sm-2 control-label ">品牌</label>
                <div class="col-sm-8">
                    <input id="brand" col="Brand" type="text" class="form-control" readonly />
                </div>
            </div>
           
            <div class="form-group row">
                <label class="col-sm-2 control-label ">保修期(月)</label>
                <div class="col-sm-8">
                    <input id="warrantyMonth" col="WarrantyMonth" type="text" class="form-control" readonly />
                </div>
            </div>
            <div class="form-group row">
                <label class="col-sm-2 control-label ">供应商</label>
                <div class="col-sm-8">
                    <input id="supplierName" col="SupplierName" type="text" class="form-control" readonly />
                </div>
            </div>
          
            <div class="form-group row">
                <label class="col-sm-2 control-label ">存放地</label>
                <div class="col-sm-8">
                    <input id="storagePlace" col="StoragePlace" class="form-control" readonly />
                </div>
            </div>

            <div class="form-group row">
                <label class="col-sm-2 control-label ">采购日期</label>
                <div class="col-sm-8">
                    <input id="purchaseDate" col="PurchaseDate" type="text" class="form-control" readonly />
                </div>
            </div>
            <div class="form-group row">
                <label class="col-sm-2 control-label ">仪器图片</label>
                <div class="col-sm-8">
                    <div style="height: 55px;display:none;" id="fileQueue"></div>
                    <input id="AttachmentId" col="AttachmentId" type="hidden" value="0" />
                    <input id="Path" col="Path" type="hidden" />
                    <a modal="zoomImg" href="javascript:void(0);" id="awardSrc">
                        <img modal="zoomImg" href="javascript:void(0);" src="" id="imgAward" class="col-sm-8" style="max-width:200px;padding: 10px 0px;" />
                    </a>
                </div>
            </div>

           @* <div class="form-group row">
                <label class="col-sm-2 control-label ">状态</label>
                <div class="col-sm-8">
                    <input id="StrStatuz" col="StrStatuz" type="text" class="form-control" readonly />
                </div>
            </div>*@
        </form>
    </div>
</div>
<script type="text/javascript">
    var id = ys.request("id");
    $(function () {
        getForm();
    });

    function getForm() {
        if (id > 0) {
            ys.ajax({
                url: '@Url.Content("~/InstrumentManage/SchoolInstrument/GetFormJson")' + '?id=' + id,
                type: 'get',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        //console.log("obj.Data:" + JSON.stringify(obj.Data));
                        obj.Data.PurchaseDate = ys.formatDate(obj.Data.PurchaseDate, "yyyy-MM-dd");
                        obj.Data.Price = ComBox.ToLocaleString(obj.Data.Price);
                        $('#form').setWebControls(obj.Data);
                        if (obj.Data.AttachmentList.length > 0) {
                            $('#AttachmentId').val(obj.Data.AttachmentList[0].Id);
                            $('#Path').val(obj.Data.AttachmentList[0].Path);
                            $('#imgAward').attr("src", obj.Data.AttachmentList[0].Path);
                            $("#awardSrc").attr("src", obj.Data.AttachmentList[0].Path);
                            imgages.showextalink();
                        }
                    }
                }
            });
        }
        else {
            getInstrumentModel(0);
        }
    }
</script>
