﻿@using Newtonsoft.Json
@{
    Layout = "~/Views/Shared/_FormWhite.cshtml";
}
<div class="wrapper animated fadeInRight">
    <form id="form" class="form-horizontal m">
        <div class="form-group row">
            <label class="col-sm-3 control-label ">单位代码<font class="red"> *</font></label>
            <div class="col-sm-9">
                <input id="Code" col="Code" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">单位名称<font class="red"> *</font></label>
            <div class="col-sm-9">
                <input id="Name" col="Name" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">社会统一信用代码</label>
            <div class="col-sm-9">
                <input id="OrganizationCode" col="OrganizationCode" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">所属地区</label>
            <div class="col-sm-9">
                <div id="AreaId" col="AreaId"></div>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">详细地址</label>
            <div class="col-sm-9">
                <input id="Address" col="Address" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">排序<font class="red">*</font></label>
            <div class="col-sm-9">
                <input id="Sort" col="Sort" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">单位类型：<font class="red"> *</font></label>
            <div class="col-sm-9">
                <div id="UnitType" col="UnitType"></div>
                <span id="spanUnitTypeName" class="form-control" style="border: 0px solid #e5e6e7;">--</span>
            </div>
        </div>
        <div class="form-group row" id="divPidUnit">
            <label class="col-sm-3 control-label ">父级单位：<font class="red"> *</font></label>
            <div class="col-sm-9">
                <div id="PId" col="PId"></div>
            </div>
        </div>
        <div class="form-group row" id="divSchoolExtension">
            <label class="col-sm-3 control-label ">单位性质：<font class="red"> *</font></label>
            <div class="col-sm-3">
                <div id="SchoolNature" col="SchoolNature" style="display:inline-block;"></div>
            </div>
            @await Html.PartialAsync("/Areas/OrganizationManage/Shared/SchoolPropFormpartial.cshtml", new ViewDataDictionary(this.ViewData) { { "Div", "6" }, { "Label", "3" }, { "Content", "3" } })
        </div>
    </form>
</div>

<script type="text/javascript">
    var id = ys.request("id");
    $(function () {
        $("#UnitType").ysComboBox({
            data: ys.getJson(@Html.Raw(JsonConvert.SerializeObject(typeof(UnitTypeEnum).EnumToDictionary().Where(m=>m.Key!=0).ToList()))),
            class: "form-control",
            onChange: function () {
                LoadShowPidUnit();
                LoadIsShowSchoolExtension();
            }
        });
        $("#SchoolNature").ysRadioBox({ data: ys.getJson(@Html.Raw(typeof(SchoolNatureEnum).EnumToDictionaryString())) });
        $('#AreaId').ysComboBoxTree({ url: '@Url.Content("~/SystemManage/Area/GetZtreeAreaListJson")', expandLevel: -1 });
        $("#form").validate({
            rules: {
                Code: { required: true },
                Name: { required: true }
            },
            messages: {
                Code: "请填写单位代码。",
                Name: "请填写单位名称。"
            }
        });
        getForm();
    });
    function LoadDataPidUnit(selectid) {
        if (selectid > 0) {
            $('#PId').ysComboBox({
                url: '@Url.Content("~/OrganizationManage/Unit/GetListJson")' + "?UnitType=" + selectid,
                class: "form-control",
                key: 'Id',
                value: 'Name'
            });
        } else {
            $('#PId').ysComboBox({ data: [], key: 'Id', value: 'Name' });
        }
    }
    function LoadShowPidUnit() {
         var selectid = parseInt($('#UnitType').ysComboBox('getValue'));
        if (selectid == @UnitTypeEnum.County.ParseToInt() ) {
            LoadDataPidUnit(@UnitTypeEnum.City.ParseToInt());
            $("#divPidUnit").show();
        }
        else if (selectid == @UnitTypeEnum.School.ParseToInt()) {
            LoadDataPidUnit(@UnitTypeEnum.County.ParseToInt());
            $("#divPidUnit").show();
        }
        else {
            $("#divPidUnit").hide();

        }
    }
    function LoadIsShowSchoolExtension() {
        var selectid = parseInt($('#UnitType').ysComboBox('getValue'));
        if (selectid ==@UnitTypeEnum.School.ParseToInt()) {
            $("#divSchoolExtension").show();
        } else {
            $("#divSchoolExtension").hide();
        }
    }
    function getForm() {
        if (id > 0) {
            $("#spanUnitTypeName").show();
            $("#UnitType").hide();
            ys.ajax({
                url: '@Url.Content("~/OrganizationManage/Unit/GetFormJson")' + '?id=' + id,
                type: 'get',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        $('#form').setWebControls(obj.Data);
                        $("#spanUnitTypeName").text(obj.Data.UnitTypeName);
                    }
                }
            });
        }
        else {
            $("#spanUnitTypeName").hide();
            $("#UnitType").show();
            LoadShowPidUnit();
            LoadIsShowSchoolExtension();
            var defaultData = {};
            $('#form').setWebControls(defaultData);
        }
    }

    function saveForm(index) {
        if ($('#form').validate().form()) {
            var postData = $('#form').getWebControls({ Id: id });
            postData.AreaId = ys.getLastValue(postData.AreaId);
            //if (postData.AreaId != undefined && postData.AreaId.length > 0) {
            //    postData.AreaId = ys.getLastValue(postData.AreaId);
            //} else {
            //    postData.AreaId = 0;
            //}
            var errorMsg = '';
            if (!(postData.Code != undefined && postData.Code.length >= 4)) {
                errorMsg += '请填写单位代码，至少4位字符。<br/>';
            }
            if (!(postData.Name != undefined && postData.Name.length >= 2)) {
                errorMsg += '请填写单位名称，名称至少2个字符。<br/>';
            }
            if (id == 0 && !(parseInt(postData.UnitType) > 0)) {
                errorMsg += '请选择单位类型。<br/>';
            }
            if (!(parseInt(postData.PId) > 0) && (postData.UnitType == @UnitTypeEnum.County.ParseToInt() || postData.UnitType == @UnitTypeEnum.School.ParseToInt())) {
                errorMsg += '请选择父级单位。<br/>';
            }
            if ( postData.UnitType == @UnitTypeEnum.School.ParseToInt()) {
                if (!(parseInt(postData.SchoolNature) > 0)) {
                    errorMsg += '请选择单位性质。<br/>';
                }
                if (!(parseInt(postData.SchoolProp) > 0)) {
                    errorMsg += '请选择单位属性。<br/>';
                }
            }
            if (errorMsg != '') {
                ys.msgError('验证失败，请填写完成再提交！<br/>' + errorMsg);
                return;
            }
            ys.ajax({
                url: '@Url.Content("~/OrganizationManage/Unit/SaveFormJson")',
                type: 'post',
                data: postData,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        parent.$('#gridTable').ysTable('refresh'); parent.resetToolbarStatus();
                        parent.layer.close(index);
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        }
    }
</script>

