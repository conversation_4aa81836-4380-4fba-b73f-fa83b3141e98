﻿@{
    Layout = "~/Views/Shared/_FormWhite.cshtml";
}

<div class="wrapper animated fadeInRight">
    <form id="form" class="form-horizontal m">
        <div class="form-group row">
            <label class="col-sm-3 control-label ">状态：</label>
            <div class="col-sm-8">
                <div id="Statuz" col="Statuz"></div>
            </div>
        </div>
    </form>
</div>

<script type="text/javascript">
    var ids = ys.request("ids");
    var textbookVersionBaseId = ys.request("textbookVersionBaseId");
    $(function () {
        $("#Statuz").ysRadioBox({ data: ys.getJson(@Html.Raw(typeof(StatusEnum).EnumToDictionaryString())), class:"form-control"});
    });
    function saveForm(index) {
        if (ids == '') {
            ys.msgError('请选择需要设置的数据！');
            return false;
        }

        var postData = $('#form').getWebControls({ Ids: ids, TextbookVersionBaseId: textbookVersionBaseId });

        if (postData.Statuz != @StatusEnum.Yes.ParseToInt() && postData.Statuz != @StatusEnum.No.ParseToInt()) {
            ys.msgError('请选择状态！');
            return false;
        }

        ys.ajax({
            url: '@Url.Content("~/ExperimentTeachManage/TextbookVersionDetail/SetStatuzFormJson")',
            type: 'post',
            data: postData,
            success: function (obj) {
                if (obj.Tag == 1) {
                    ys.msgSuccess(obj.Message);
                    parent.$('#gridTable').ysTable('refresh');parent.resetToolbarStatus();
                    parent.layer.close(index);
                }
                else {
                    ys.msgError(obj.Message);
                }
            }
        });
    }
    
</script>

