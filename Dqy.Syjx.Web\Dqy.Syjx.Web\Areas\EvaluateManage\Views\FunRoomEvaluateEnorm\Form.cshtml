﻿@{
    Layout = "~/Views/Shared/_FormWhite.cshtml";
}

<div class="wrapper animated fadeInRight">
    <form id="form" class="form-horizontal m">
        <div class="form-group row">
            <label class="col-sm-3 control-label ">轨数起</label>
            <div class="col-sm-8">
                <input id="RailStart" col="RailStart" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">轨数止</label>
            <div class="col-sm-8">
                <input id="RailEnd" col="RailEnd" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">达标级别</label>
            <div class="col-sm-8">
                <div id="StandardLevel" col="StandardLevel" ></div>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">数量（间）</label>
            <div class="col-sm-8">
                <input id="RoomNum" col="RoomNum" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">单室面积（㎡）</label>
            <div class="col-sm-8">
                <input id="RoomArea" col="RoomArea" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">是否评估</label>
            <div class="col-sm-8">
                <div id="IsEvaluate" col="IsEvaluate"></div>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">备注</label>
            <div class="col-sm-8">
                <textarea  id="remark" col="Remark" class="form-control"></textarea>
            </div>
        </div>
    </form>
</div>

<script type="text/javascript">
    var id = ys.request("id");
    $(function () {
        $('#StandardLevel').ysComboBox({ url: '@Url.Content("~/SystemManage/StaticDictionary/GetListJson")' + "?TypeCode=1011", class: 'form-control', key: 'DictionaryId', value: 'DicName' });
        $("#IsEvaluate").ysComboBox({ data: ys.getJson(@Html.Raw(typeof(IsEnum).EnumToDictionaryString())), class:"form-control"});
        getForm();

        $('#form').validate({
            rules: {
                RailStart: { required: true, number: true },
                RailEnd: { required: true, number: true },
                RoomNum: { required: true, number: true  },
                RoomArea: { required: true, number: true  }
            }
        });
    });

    function getForm() {
        if (id > 0) {
            ys.ajax({
                url: '@Url.Content("~/EvaluateManage/FunRoomEvaluateEnorm/GetFormJson")' + '?id=' + id,
                type: 'get',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        $('#form').setWebControls(obj.Data);
                    }
                }
            });
        }
        else {
            var defaultData = {};
            $('#form').setWebControls(defaultData);
        }
    }

    function saveForm(index) {
        if ($('#form').validate().form()) {
            var postData = $('#form').getWebControls({ Id: id });
            var errorMsg = '';
            if (postData.RailStart <= 0) {
                errorMsg += "轨数起的值必须在0至100之间。<br/>";
            }
            if (postData.RailEnd <= 0) {
                errorMsg += "轨数止的值必须在0至100之间。<br/>";
            }
            if (parseInt(postData.RailStart) > parseInt(postData.RailEnd)) {
                errorMsg += "轨数起必须小于轨数止的值。<br/>";
            }
            if (!(postData.StandardLevel >0)) {
                errorMsg += "请选择达标级别。<br/>";
            }
            if (!(postData.RoomNum >= 0)) {
                errorMsg += "填写的数量（间）必须大于0。<br/>";
            }
            if (!(postData.RoomArea >= 0)) {
                errorMsg += "填写的单室面积（㎡）必须大于0。<br/>";
            }
            if (!(postData.IsEvaluate == 0 || postData.IsEvaluate == 1)) {
                errorMsg += "请选择是否评估。<br/>";
            }
            if (errorMsg != '') {
                ys.msgError('验证失败，请填写完成再提交！<br/>' + errorMsg);
                return;
            }
            postData.EvaluateStandardId = parent.standardid;
            ys.ajax({
                url: '@Url.Content("~/EvaluateManage/FunRoomEvaluateEnorm/SaveFormJson")',
                type: 'post',
                data: postData,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        parent.$('#gridTable').ysTable('refresh');parent.resetToolbarStatus();
                        parent.layer.close(index);
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        }
    }
</script>

