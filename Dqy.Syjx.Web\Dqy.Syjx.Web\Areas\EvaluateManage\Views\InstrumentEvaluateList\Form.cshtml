﻿@{
    Layout = "~/Views/Shared/_FormWhite.cshtml";
}

<div class="wrapper animated fadeInRight">
    <form id="form" class="form-horizontal m">
        <div class="form-group row">
            <label class="col-sm-3 control-label ">仪器名称<font class="red"> *</font></label>
            <div class="col-sm-8">
                <input id="instrumentName" col="InstrumentName" type="text" class="form-control" readonly />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">比对代码</label>
            <div class="col-sm-8">
                <input id="ContrastCode" col="ContrastCode" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">是否评估</label>
            <div class="col-sm-8">
                <div id="IsEvaluate" col="IsEvaluate"></div>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">调控系数</label>
            <div class="col-sm-8">
                <input id="RegulationRatio" col="RegulationRatio" type="text" class="form-control" />
            </div>
        </div>

        <div class="form-group row">
            <label class="col-sm-3 control-label ">备注</label>
            <div class="col-sm-8">
               <textarea id="Remark" col="Remark"  class="form-control" ></textarea>
            </div>
        </div>
    </form>
</div>

<script type="text/javascript">
    var id = ys.request("id");
    $(function () {
        $("#IsEvaluate").ysComboBox({ data: ys.getJson(@Html.Raw(typeof(IsEnum).EnumToDictionaryString())), class:'form-control' });
        getForm();

        $('#form').validate({
            rules: {
                ContrastCode: { required: true },
                RegulationRatio: { required: true, number: true  }
            }
        });
    });

    function getForm() {
        if (id > 0) {
            ys.ajax({
                url: '@Url.Content("~/EvaluateManage/InstrumentEvaluateList/GetFormJson")' + '?id=' + id,
                type: 'get',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        $('#form').setWebControls(obj.Data);
                    }
                }
            });
        }
        else {
            var defaultData = {};
            $('#form').setWebControls(defaultData);
        }
    }

    function saveForm(index) {
        if ($('#form').validate().form()) {
            var postData = $('#form').getWebControls({ Id: id });
            postData.EvaluateStandardId = parent.id;
            ys.ajax({
                url: '@Url.Content("~/EvaluateManage/InstrumentEvaluateList/SaveFormJson")',
                type: 'post',
                data: postData,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        parent.$('#gridTable').ysTable('refresh');parent.resetToolbarStatus();
                        parent.layer.close(index);
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        }
    }
</script>

