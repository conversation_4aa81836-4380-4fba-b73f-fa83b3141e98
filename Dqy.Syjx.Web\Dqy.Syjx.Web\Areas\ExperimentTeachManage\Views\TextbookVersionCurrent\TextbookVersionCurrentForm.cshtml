﻿@{
    Layout = "~/Views/Shared/_FormWhite.cshtml";
}
<style type="text/css">
    .check-box{
        width:200px;
    }
</style>
<div class="wrapper animated fadeInRight">
    <form id="form" class="form-horizontal m">
        <div class="form-group row">
            <label class="col-sm-3 control-label ">学段<font class="red"> *</font></label>
            <div class="col-sm-8">
                <div id="schoolStage" col="SchoolStage"></div>
            </div>
        </div>
        <div class="form-group nongaozhong">
            <label class="col-sm-3 control-label ">年级<font class="red"> *</font></label>
            <div class="col-sm-8">
                <div id="gradeId" col="GradeId"></div>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">学科<font class="red"> *</font></label>
            <div class="col-sm-8">
                <div id="courseId" col="CourseId"></div>
            </div>
        </div>
        <div class="form-group nongaozhong">
            <label class="col-sm-3 control-label ">学期<font class="red"> *</font></label>
            <div class="col-sm-8">
                <div id="schoolTerm" col="SchoolTerm"></div>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">实验教材版本<font class="red"> *</font></label>
            <div class="col-sm-8">
                <div id="textbookVersionBaseId" col="TextbookVersionBaseId"></div>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">区县<font class="red"> *</font></label>
            <div class="col-sm-8" id="countyId">
                
            </div>
        </div>
    </form>
</div>

<script type="text/javascript">
    var id = ys.request("id");
    var CurrentSelectSchoolStageId = 0;
    var CurrentSelectGradeId = 0;
    var CurrentSelectCourseId = 0;
    $(function () {
        initBindComboBox();
        getForm();

        $('#form').validate({
            rules: {

            }
        });
    });

    function initBindComboBox() {
        $("#schoolTerm").ysComboBox({ data: ys.getJson(@Html.Raw(typeof(SchoolTermEnum).EnumToDictionaryString())), class: 'form-control' });
        $('#schoolStage').ysComboBox({
            //url: '@Url.Content("~/SystemManage/StaticDictionary/GetListJson")' + '?TypeCode=@DicTypeCodeEnum.SchoolStage.ParseToInt()',
            key: 'DictionaryId',
            value: 'DicName',
            class: 'form-control',
            onChange: function () {
                var selectSchoolStage = $('#schoolStage').ysComboBox('getValue');
                if (CurrentSelectSchoolStageId != selectSchoolStage) {
                    CurrentSelectSchoolStageId = selectSchoolStage;
                    loadGaoZhongShow();
                    CurrentSelectCourseId = 0;
                    if (selectSchoolStage == @SchoolStageEnum.GaoZhong.ParseToInt()) {
                        //加载学科
                        loadCourse(selectSchoolStage);
                    } else {
                        CurrentSelectGradeId = 0; 
                        bindCourseData({});
                        loadGrade(selectSchoolStage);
                    }
                    bindTextbookVersionBase({});
                }
            }
        });

        $('#gradeId').ysComboBox({
            //url: '@Url.Content("~/SystemManage/StaticDictionary/GetChildListJson")' + '?TypeCode=@DicTypeCodeEnum.Grade.ParseToInt()&Pid=' + schoolStage,
            key: 'DictionaryId',
            value: 'DicName',
            class: 'form-control',
            onChange: function () {
                var selectGradeId = $('#gradeId').ysComboBox('getValue');
                if (CurrentSelectGradeId != selectGradeId) {
                    CurrentSelectGradeId = selectGradeId;
                    bindTextbookVersionBase({});
                    CurrentSelectCourseId = 0;
                    loadCourse(selectGradeId);
                 
                }
            }
        });

        $('#courseId').ysComboBox({
            //url: '@Url.Content("~/SystemManage/StaticDictionary/GetChildToListJson")' + '?TypeCode=@DicTypeCodeEnum.Course.ParseToInt()&Pid=' + gradeId + '&Ids=1005002,1005003,1005004,1005005',
            key: 'DictionaryId',
            value: 'DicName',
            class: 'form-control', 
            onChange: function () {
                var selectid = $('#courseId').ysComboBox('getValue');
                if (CurrentSelectCourseId != selectid) {
                    CurrentSelectCourseId = selectid;
                    loadTextbookVersionBase(selectid);
                }
            }
        });
        $('#textbookVersionBaseId').ysComboBox({
            //url: '@Url.Content("~/ExperimentTeachManage/TextbookVersionBase/GetListJson")' + '?Statuz=@StatusEnum.Yes.ParseToInt()&SchoolStage=' + $('#schoolStage').ysComboBox('getValue')+ '&CourseId=' + $('#courseId').ysComboBox('getValue'),
            key: 'Id',
            value: 'VersionBrandName',
            class: 'form-control',
        });
    }
    function getForm() {
        if (id > 0) {
            ys.ajax({
                url: '@Url.Content("~/ExperimentTeachManage/TextbookVersionCurrent/GetFormJson")' + '?id=' + id,
                type: 'get',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        /*$('#form').setWebControls(obj.Data);*/
                        CurrentSelectSchoolStageId = obj.Data.SchoolStage;
                        loadSchoolStage(obj.Data.SchoolStage);
                        if (obj.Data.SchoolStage == @SchoolStageEnum.GaoZhong.ParseToInt()) {
                            CurrentSelectCourseId = obj.Data.CourseId;
                            loadCourse(obj.Data.SchoolStage, obj.Data.CourseId);
                        }else{
                            CurrentSelectGradeId = obj.Data.GradeId;
                            loadGrade(obj.Data.SchoolStage, obj.Data.GradeId);
                            CurrentSelectCourseId = obj.Data.CourseId;
                            loadCourse(obj.Data.GradeId, obj.Data.CourseId);
                        }
                        loadGaoZhongShow() 
                        if (obj.Data.SchoolTerm > 0) $('#schoolTerm').ysComboBox('setValue', obj.Data.SchoolTerm);
                        loadTextbookVersionBase(obj.Data.CourseId, obj.Data.TextbookVersionBaseId);
                        loadCounty(obj.Data.CountyIdz); 
                    }
                }
            });
        }
        else {
            loadSchoolStage();
            loadGrade();
            loadCourse();
            loadGaoZhongShow();
            loadCounty();
            $('#textbookVersionBaseId').ysComboBox({ class: 'form-control'});

        }
    }

    function loadGaoZhongShow() {
        var selectSchoolStage = $('#schoolStage').ysComboBox('getValue');
        if (selectSchoolStage != undefined && selectSchoolStage > 0) {
            if (selectSchoolStage == @SchoolStageEnum.GaoZhong.ParseToInt()) {
                $(".nongaozhong").hide();
            } else {
                $(".nongaozhong").show();
            }
        } else {
            $(".nongaozhong").hide();
        }
    }

    function loadSchoolStage(defaultValue) {
        $('#schoolStage').ysComboBox({
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetListJson")' + '?TypeCode=@DicTypeCodeEnum.SchoolStage.ParseToInt()',
            key: 'DictionaryId',
            value: 'DicName',
            class: 'form-control'
        });
        if (defaultValue > 0) $('#schoolStage').ysComboBox('setValue', defaultValue);
    }

    
    function loadGrade(schoolStage, defaultValue) {
        if (schoolStage > 0) {
            ys.ajax({
                url: '@Url.Content("~/SystemManage/StaticDictionary/GetChildListJson")' + '?TypeCode=@DicTypeCodeEnum.Grade.ParseToInt()&Pid=' + schoolStage,
                type: 'get',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        bindGradeData(obj.Data, defaultValue);
                    } else {
                        bindGradeData({});
                    }
                }
            });
        }else{
            bindGradeData({});
        }
    }

    function bindGradeData(data, defaultValue) {
        $('#gradeId').ysComboBox({
            class: 'form-control',
            data: data,
            key: 'DictionaryId',
            value: 'DicName'
        });
        if (defaultValue > 0) $('#gradeId').ysComboBox('setValue', defaultValue);
    }

    function loadCourse(pid, defaultValue) {
        if (pid > 0) {
            ys.ajax({
                url: '@Url.Content("~/SystemManage/StaticDictionary/GetChildToListJson")' + '?TypeCode=@DicTypeCodeEnum.Course.ParseToInt()&Pid=' + pid + '&Ids=1005002,1005003,1005004,1005005',
                type: 'get',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        bindCourseData(obj.Data, defaultValue);
                    } else {
                        bindCourseData({});
                    }
                }
            });
        } else {
            bindCourseData({});
        }
    }
    
    function bindCourseData(data, defaultValue) {
        $('#courseId').ysComboBox({
            class: 'form-control',
            data: data,
            key: 'DictionaryId',
            value: 'DicName'
        });
        if (defaultValue > 0) $('#courseId').ysComboBox('setValue', defaultValue);
    }

    function loadTextbookVersionBase(pid, defaultValue) {
        if (pid > 0) {
            var selectSchoolStage = $('#schoolStage').ysComboBox('getValue');
            ys.ajax({
                url: '@Url.Content("~/ExperimentTeachManage/TextbookVersionBase/GetListJson")' + '?Statuz=@StatusEnum.Yes.ParseToInt()&SchoolStage=' + selectSchoolStage + '&CourseId=' + pid,
                type: 'get',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        bindTextbookVersionBase(obj.Data, defaultValue);
                    } else {
                        bindTextbookVersionBase({});
                    }
                }
            });
        } else {
            bindTextbookVersionBase({});
        }
    }
    function bindTextbookVersionBase(data, defaultValue) {
        $('#textbookVersionBaseId').ysComboBox({
            data: data,
            key: 'Id',
            value: 'VersionBrandName',
            class: 'form-control',
        });
        if (defaultValue > 0) $('#textbookVersionBaseId').ysComboBox('setValue', defaultValue);
    }

    function loadCounty(defaultValue) {
        $('#countyId').html('');
        ys.ajax({
            url: '@Url.Content("~/OrganizationManage/Unit/GetAllCountyListJson")',
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1) {
                    var html = '<label class="check-box"><div class="icheckbox-blue"><input name="countyall_checkbox" type="checkbox" value="0" style="position: absolute; opacity: 0;"></div>全部</label>';

                    for (var i = 0; i < obj.Data.length; i++) {
                        html += '<label class="check-box">';
                        if (defaultValue && defaultValue.indexOf(obj.Data[i].Id) > -1) {
                            html += $.Format('<div class="icheckbox-blue checked"><input name="countyId_checkbox" type="checkbox" value="{0}" style="position: absolute; opacity: 0;" checked="checked"></div>', obj.Data[i].Id);
                        } else {
                            html += $.Format('<div class="icheckbox-blue"><input name="countyId_checkbox" type="checkbox" value="{0}" style="position: absolute; opacity: 0;"></div>', obj.Data[i].Id);
                        }
                        html += $.Format('{0}</label>', obj.Data[i].Name);
                    }
                    $('#countyId').html(html);

                    $('input[name="countyId_checkbox"]').change(function () {
                        if ($(this).prop("checked")) {
                            $(this).parent(".icheckbox-blue").addClass("checked");
                        } else {
                            $(this).parent(".icheckbox-blue").removeClass("checked");
                        }
                        let ckLength = $('input[name="countyId_checkbox"]').length;
                        let ckedLength = $('input[name="countyId_checkbox"]:checked').length;
                        if (ckLength > ckedLength) {
                            $('input[name="countyall_checkbox"]').prop('checked', false);
                            $('input[name="countyall_checkbox"]').parent(".icheckbox-blue").removeClass("checked");
                        }
                        else {
                            $('input[name="countyall_checkbox"]').prop('checked', true);
                            $('input[name="countyall_checkbox"]').parent(".icheckbox-blue").addClass("checked");
                        }
                    });

                    $('input[name="countyall_checkbox"]').change(function () {
                        //实现全选效果
                        if ($(this).prop("checked")) {
                            $('input[name="countyId_checkbox"]').prop('checked', true);
                            $(this).parent(".icheckbox-blue").addClass("checked");
                            $('input[name="countyId_checkbox"]').parent(".icheckbox-blue").addClass("checked");
                        } else {
                            $('input[name="countyId_checkbox"]').prop('checked', false);
                            $(this).parent(".icheckbox-blue").removeClass("checked");
                            $('input[name="countyId_checkbox"]').parent(".icheckbox-blue").removeClass("checked");
                        }
                    });

                    if (defaultValue) {
                        if (defaultValue.split(',').length == $('input[name="countyId_checkbox"]').length) {
                            $('input[name="countyall_checkbox"]').prop('checked', true);
                            $('input[name="countyall_checkbox"]').parent(".icheckbox-blue").addClass("checked");
                        }
                    }
                }
            }
        });
    }

    function saveForm(index) {
        if ($('#form').validate().form()) {
            var postData = $('#form').getWebControls({ Id: id });
            if (!postData.SchoolStage) {
                ys.msgError('请选择学段！');
                return false;
            }
            if (postData.SchoolStage != @SchoolStageEnum.GaoZhong.ParseToInt()) {
                if (!postData.GradeId) {
                    ys.msgError('请选择年级！');
                    return false;
                }
                if (!postData.SchoolTerm) {
                    ys.msgError('请选择学期！');
                    return false;
                }
            }
           
            if (!postData.CourseId) {
                ys.msgError('请选择学科！');
                return false;
            } 
            if (!postData.TextbookVersionBaseId) {
                ys.msgError('请选择实验教材版本！');
                return false;
            }
            var countyIdz = $('input[name="countyId_checkbox"]:checked').map(function () { return this.value }).get().join(',');
            if (countyIdz == '') {
                ys.msgError('请选择区县！');
                return false;
            }
            postData.CountyIdz = countyIdz;
            ys.ajax({
                url: '@Url.Content("~/ExperimentTeachManage/TextbookVersionCurrent/SaveFormJson")',
                type: 'post',
                data: postData,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        parent.$('#gridTable').ysTable('refresh');parent.resetToolbarStatus();
                        parent.layer.close(index);
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        }
    }
</script>

