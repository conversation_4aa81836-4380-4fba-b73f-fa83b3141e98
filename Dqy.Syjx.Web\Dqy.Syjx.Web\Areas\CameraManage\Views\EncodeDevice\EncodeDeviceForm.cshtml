﻿@{
    Layout = "~/Views/Shared/_FormWhite.cshtml";
}

<div class="wrapper animated fadeInRight">
    <form id="form" class="form-horizontal m">
        @*<div class="form-group row">
            <label class="col-sm-3 control-label ">资源唯一编码</label>
            <div class="col-sm-8">
                <input id="indexCode" col="IndexCode" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">资源名称</label>
            <div class="col-sm-8">
                <input id="encodeName" col="EncodeName" type="text" class="form-control" />
            </div>
        </div>*@
        <div class="form-group row">
            <label class="col-sm-3 control-label ">设备编号<font class="red"> *</font></label>
            <div class="col-sm-8">
                <input id="deviceCode" col="DeviceCode" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">设备品牌<font class="red"> *</font></label>
            <div class="col-sm-8" id="devicebrand" col="DeviceBrand" style="bottom:6px;">
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">摄像头名称<font class="red"> *</font></label>
            <div class="col-sm-8">
                <input id="cameraName" col="CameraName" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">通道号<font class="red"> *</font></label>
            <div class="col-sm-8">
                <input id="channelSeq" col="ChannelSeq" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">码流类型<font class="red"> *</font></label>
            <div class="col-sm-8" id="streamType" col="StreamType" style="bottom:6px;">
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">IP地址</label>
            <div class="col-sm-8">
                <input id="iP" col="IP" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">端口号</label>
            <div class="col-sm-8">
                <input id="port" col="Port" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">所属单位<font class="red"> *</font></label>
            <div class="col-sm-8" id="unitName" col="UnitId"></div>
        </div>
    </form>
</div>

<script type="text/javascript">
    var id = ys.request("id");
    $(function () {
        $("#streamType").ysRadioBox({ data: ys.getJson([{ "Key": 1, "Value": "主码流" }, { "Key": 2, "Value": "辅码流" }]), class: "form-control" });
        $("#streamType").ysRadioBox('setValue', 1);
        $("#devicebrand").ysRadioBox({ data: ys.getJson([{ "Key": 1, "Value": "海康" }, { "Key": 2, "Value": "大华" }]), class: "form-control" });
        $("#devicebrand").ysRadioBox('setValue', 1);
        $('#unitName').ysComboBox(
             {
                url: '@Url.Content("~/OrganizationManage/Unit/GetListJson")' + "?PageSize=10000",
                class: 'form-control col-sm-8',
                key: 'Id',
                value: 'Name',
                onChange: function () {
                    var unitId = $('#unitName').ysComboBox('getValue');
                    if (unitId == "") {
                        unitId = 0;
                    }
                }
            });
       
        getForm();

        $('#form').validate({
            rules: {
                deviceCode: { required: true },
                cameraName: { required: true },
                channelSeq: { required: true },
                streamType: { required: true },
                devicebrand: { required: true }
            }
        });
    });

    function getForm() {
        if (id > 0) {
            ys.ajax({
                url: '@Url.Content("~/CameraManage/EncodeDevice/GetFormJson")' + '?id=' + id,
                type: 'get',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        $('#form').setWebControls(obj.Data);
                    }
                }
            });
        }
        else {
            var defaultData = {};
            $('#form').setWebControls(defaultData);
        }
    }

    function saveForm(index) {
        if ($('#form').validate().form()) {
            var postData = $('#form').getWebControls({ Id: id });
            var unitId = postData.UnitId;
            if (unitId == "") {
                ys.msgError("请选择所属单位");
                return;
            }
            ys.ajax({
                url: '@Url.Content("~/CameraManage/EncodeDevice/SaveFormJson")',
                type: 'post',
                data: postData,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        parent.$('#gridTable').ysTable('refresh');
                        parent.resetToolbarStatus();
                        parent.layer.close(index);
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        }
    }
</script>

