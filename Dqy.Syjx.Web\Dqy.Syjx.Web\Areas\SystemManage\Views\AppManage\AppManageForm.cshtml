﻿@{
    Layout = "~/Views/Shared/_FormWhite.cshtml";
}

<div class="wrapper animated fadeInRight">
    <form id="form" class="form-horizontal m">
        <div class="form-group row">
            <label class="col-sm-3 control-label ">密钥类型 <font class="red"> *</font></label>
            <div class="col-sm-6">
                <div id="isMain" col="IsMain" style="display: inline-block;"></div>
            </div>
            <div class="col-sm-3">
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">应用类型 <font class="red"> *</font></label>
            <div class="col-sm-6">
                <div id="appType" col="AppType" style="display: inline-block;"></div>
            </div>
            <div class="col-sm-3">
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">第三方应用/平台名称 <font class="red"> *</font></label>
            <div class="col-sm-6">
                <input id="appName" col="AppName" type="text" class="form-control" />
            </div>
            <div class="col-sm-3">
            </div>
        </div>
        <div class="form-group row" id="divAppCode">
            <label class="col-sm-3 control-label ">应用编码</label>
            <div class="col-sm-6">
                <input id="appCode" col="AppCode" type="text" class="form-control" disabled="disabled"/>
            </div>
            <div class="col-sm-3">
                系统自动生成的编码
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">AppKey</label>
            <div class="col-sm-6">
                <input id="appKey" col="AppKey" type="text" class="form-control" />
            </div>
            <div class="col-sm-3">
                应用的AppKey
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">AppSecret</label>
            <div class="col-sm-6">
                <input id="appSecret" col="AppSecret" type="text" class="form-control" />
            </div>
            <div class="col-sm-3">
                应用的AppSecret
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">AgentId</label>
            <div class="col-sm-6">
                <input id="angentId" col="AngentId" type="text" class="form-control" />
            </div>
            <div class="col-sm-3">
                企业微信自建应用的AgentId
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">Secret</label>
            <div class="col-sm-6">
                <input id="secret" col="Secret" type="text" class="form-control" />
            </div>
            <div class="col-sm-3">
                企业微信自建应用的Secret
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">CorpId</label>
            <div class="col-sm-6">
                <input id="corpId" col="CorpId" type="text" class="form-control" />
            </div>
            <div class="col-sm-3">
                企业微信的企业ID
            </div>
        </div>
        @*<div class="form-group row">
            <label class="col-sm-3 control-label ">CorpSecret</label>
            <div class="col-sm-8">
                <input id="corpSecret" col="CorpSecret" type="text" class="form-control" />
            </div>
            <div class="col-sm-3">
                企业微信
            </div>
        </div>*@
        <div class="form-group row">
            <label class="col-sm-3 control-label ">单位类型</label>
            <div class="col-sm-6">
                <div id="unitType" col="UnitType"></div>
            </div>
            <div class="col-sm-3">
                创建企业微信应用必须选择单位类型、名称！
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">单位名称</label>
            <div class="col-sm-6">
                <div id="unitId" col="UnitId"></div>
            </div>
            <div class="col-sm-3">
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">应用描述</label>
            <div class="col-sm-6">
                <textarea id="memo" col="Memo" class="form-control" style="height:80px;"></textarea>
            </div>
            <div class="col-sm-3">
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">接口名称</label>
            <div class="col-sm-6">
                <input id="apiName" col="ApiName" type="text" class="form-control" />
            </div>
            <div class="col-sm-3">
                企业微信应用使用的接口名称
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">接口密钥</label>
            <div class="col-sm-6">
                <input id="apiSecret" col="ApiSecret" type="text" class="form-control" />
            </div>
            <div class="col-sm-3">
                企业微信应用使用的接口密钥
            </div>
        </div>

        <div class="form-group row">
            <label class="col-sm-3 control-label ">统一身份认证接口地址</label>
            <div class="col-sm-6">
                <input id="authHost" col="AuthHost" type="text" class="form-control" />
            </div>
            <div class="col-sm-3">
                最基本的接口地址，必填；如果多个地址，这个是存换取token的接口地址
            </div>
        </div>

                <div class="form-group row">
            <label class="col-sm-3 control-label ">数据同步接口地址</label>
            <div class="col-sm-6">
                <input id="dataHost" col="DataHost" type="text" class="form-control" />
            </div>
            <div class="col-sm-3">
                如果存在数据中心的，数据中心接口地址
            </div>
        </div>

        <div class="form-group row">
            <label class="col-sm-3 control-label ">网站地址</label>
            <div class="col-sm-6">
                <input id="webHost" col="WebHost" type="text" class="form-control" />
            </div>
            <div class="col-sm-3">
                统一身份认证首页，或登录页面地址
            </div>
        </div>


         <div class="form-group row">
            <label class="col-sm-3 control-label ">ClientId</label>
            <div class="col-sm-6">
                <input id="clientId" col="ClientId" type="text" class="form-control" />
            </div>
            <div class="col-sm-3">
                ClientId
            </div>
        </div>

        <div class="form-group row">
            <label class="col-sm-3 control-label ">ClientSecret</label>
            <div class="col-sm-6">
                <input id="clientSecret" col="ClientSecret" type="text" class="form-control" />
            </div>
            <div class="col-sm-3">
                ClientSecret
            </div>
        </div>

        <div class="form-group row">
            <label class="col-sm-3 control-label ">生成的AppKey</label>
            <div class="col-sm-6">
                <input id="generateAppKey" col="GenerateAppKey" type="text" class="form-control" />
            </div>
            <div class="col-sm-3">
                生成的AppKey
            </div>
        </div>

        <div class="form-group row">
            <label class="col-sm-3 control-label ">生成的AppSecret</label>
            <div class="col-sm-6">
                <input id="generateAppSecret" col="GenerateAppSecret" type="text" class="form-control" />
            </div>
            <div class="col-sm-3">
                生成的AppSecret
            </div>
        </div>

        <div class="form-group row">
            <label class="col-sm-3 control-label ">回调地址</label>
            <div class="col-sm-6">
                <input id="callBackUrl" col="CallBackUrl" type="text" class="form-control" />
            </div>
            <div class="col-sm-3">
                回调地址
            </div>
        </div>
    </form>
</div>

<script type="text/javascript">
    var id = ys.request("id");
    $(function () {
        getForm();
        $("#appType").ysRadioBox({ data: ys.getJson([{ "Key": 1, "Value": "PC端" }, { "Key": 2, "Value": "移动端" }]) });
        $("#appType").ysRadioBox('setValue', 2);

        $("#isMain").ysRadioBox({ data: ys.getJson([{ "Key": 1, "Value": "密钥主表" }, { "Key": 2, "Value": "第三方密钥表" }]) });
        $("#isMain").ysRadioBox('setValue', 2);

        //$("#unitType").ysRadioBox({ data: ys.getJson(@Html.Raw(typeof(UnitTypeNameEnum).EnumToDictionaryString())) });
        $("#unitType").ysComboBox({
            data: ys.getJson([{ "Key": 1, "Value": "市级" }, { "Key": 2, "Value": "区县" }, { "Key": 3, "Value": "学校" }]),
            class: "form-control",
            onChange: function () {
                LoadUnit();
            }
        });
        $("#unitType").ysComboBox('setValue', @UnitTypeEnum.School.ParseToInt());
        LoadDataUnit(@UnitTypeEnum.School.ParseToInt());

        $('#form').validate({
            rules: {
                AppType: { required: true },
                AppName: { required: true }
            }
        });
    });

    function LoadUnit() {
        var selectid = parseInt($('#unitType').ysComboBox('getValue'));
       
        if (selectid == @UnitTypeEnum.County.ParseToInt() ) {
            LoadDataUnit(@UnitTypeEnum.County.ParseToInt());
        }
        else if (selectid == @UnitTypeEnum.School.ParseToInt()) {
            LoadDataUnit(@UnitTypeEnum.School.ParseToInt());
        } else if (selectid == @UnitTypeEnum.City.ParseToInt()){
            LoadDataUnit(@UnitTypeEnum.City.ParseToInt());
        }
    }

    function LoadDataUnit(selectid) {
        if (selectid > 0) {
            $('#unitId').ysComboBox({
                url: '@Url.Content("~/OrganizationManage/Unit/GetListJsonByUnitType")' + "?UnitType=" + selectid,
                class: "form-control",
                key: 'Id',
                value: 'Name'
            });
        } else {
            $('#unitId').ysComboBox({ data: [], key: 'Id', value: 'Name' });
        }
    }

    function getForm() {
        if (id > 0) {
            $("#divAppCode").show();
            ys.ajax({
                url: '@Url.Content("~/SystemManage/AppManage/GetFormJson")' + '?id=' + id,
                type: 'get',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        $('#form').setWebControls(obj.Data);
                    }
                }
            });
        }
        else {
            $("#divAppCode").hide();
            var defaultData = {};
            $('#form').setWebControls(defaultData);
        }
    }

    function saveForm(index) {
        if ($('#form').validate().form()) {
            var postData = $('#form').getWebControls({ Id: id });
           
            if (postData.AppType == "") {
                ys.msgError("请选择应用类型");
                return;
            } else if (postData.AppName == "") {
                ys.msgError("请填写应用/平台名称");
                return;
            }

            ys.ajax({
                url: '@Url.Content("~/SystemManage/AppManage/SaveFormJson")',
                type: 'post',
                data: postData,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        parent.searchGrid();
                        parent.layer.close(index);
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        }
    }
</script>

