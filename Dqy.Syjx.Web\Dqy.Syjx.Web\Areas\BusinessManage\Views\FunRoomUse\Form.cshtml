﻿@{
    Layout = "~/Views/Shared/_FormWhite.cshtml";
}
@inject Microsoft.AspNetCore.Hosting.IWebHostEnvironment HostingEnvironment
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/uploadifive/File.min.css"))
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/uploadifive/File.min.js"))
<style type="text/css">
    .check-box {
        width: 100px;
    }

    .iradio-box {
        width: 100px;
    }

    .iradio-box {
        position: initial;
        display: inline-block;
    }

    .iradio-blue {
        position: inherit;
        display: inline-block;
        /*  top: 6px;*/
    }

    .tag_msg_color {
        color: #999;
    }
</style>
<div class="wrapper animated fadeInRight">
    <span style="color:#999;">注：如还未更新本学期“个人任课信息”，请点击右边的【更新】按钮，这将便捷你以下信息的填写！</span>
    <span> <a class="btn btn-secondary btn-sm" onclick="updateClassInfo()"><i class="fa fa-refresh"></i>&nbsp;更新&nbsp;</a></span>
    <form id="form" class="form-horizontal m">
        <div class="form-group row">
            <label class="col-sm-2 control-label ">登记人<font class="red"> *</font></label>
            <div class="col-sm-6">
                <div id="divUseUserName" style="border: 0px solid #e5e6e7;" class="form-control"></div>
            </div>
            <div class="col-sm-4">
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-2 control-label ">任课学段</label>
            <div class="col-sm-6">
                <div id="SchoolStage" col="SchoolStage"></div>
            </div>
            <div class="col-sm-4">
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-2 control-label ">任课学科</label>
            <div class="col-sm-6">
                <div id="DictionaryId1005" col="DictionaryId1005"></div>
            </div>
            <div class="col-sm-4">
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-2 control-label ">任课班级</label>
            <div class="col-sm-6">
                <div id="UseClass" col="UseClass"></div>
            </div>
            <div class="col-sm-4">
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-2 control-label ">使用地点</label>
            <div class="col-sm-6">
                <div id="FunRoomId" col="FunRoomId"></div>
            </div>
            <div class="col-sm-4">
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-2 control-label ">使用日期</label>
            <div class="col-sm-6">
                <input id="UseDate" col="UseDate" type="text" class="form-control" readonly />
            </div>
            <div class="col-sm-4">
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-2 control-label ">课程节次</label>
            <div class="col-sm-6">
                <div id="CourseSectionId" col="CourseSectionId"></div>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-2 control-label ">上课内容</label>
            <div class="col-sm-6">
                <textarea id="ClassContent" col="ClassContent" class="form-control"></textarea>
            </div>
            <div class="col-sm-4">
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-2 control-label ">现场照片</label>
            <div class="col-sm-6">
                @*<div>*@
                <input type="file" name="uploadify" id="uploadify" />
                <div id="spanFile" style=" padding: 10px 0px;"></div>
                <div style="height: 55px;display:none;" id="fileQueue"></div>

                @*</div>*@
            </div>
            <div class="col-sm-4 tag_msg_color">
                文件小于5M，支持图片文件
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-2 control-label ">设备运行</label>
            <div class="col-sm-6">
                <div id="Statuz" col="Statuz" style="padding-top:7px;"></div>
            </div>
            <div class="col-sm-4">
            </div>
        </div>
        <div class="form-group row" id="divRemark">
            <label class="col-sm-2 control-label ">问题描述</label>
            <div class="col-sm-6">
                <textarea id="RepairContent" col="RepairContent" class="form-control"></textarea>
            </div>
            <div class="col-sm-4">
            </div>
        </div>
    </form>
</div>
<input type="hidden" id="hidSubject"  value="" />
<input type="hidden" id="hidGradeClass" value="" />
<input type="hidden" id="hidFunRoom" value="" />
<script type="text/javascript">
    var id = ys.request("id");
    $(function () {
        if (isNaN(id) || id == undefined) {
            id = 0;
        }
        loadUploadify();
        laydate.render({ elem: '#UseDate', format: 'yyyy-MM-dd', trigger: 'click',position: 'fixed' });
        loadStatuz();
        loadSchoolStage();
        loadSubject(0);
        loadGradeClass(0);
        loadFunRoom(0, 0);
        loadCourseSection();
        getForm();
    });
    function loadStatuz() {
        var html = '';
        html += '<label class="iradio-box"><div class="iradio-blue single" style="vertical-align:bottom;"><input name="inputType_checkbox" type="radio" value="1" style="position: absolute; opacity: 0;"></div> 正常</label>';
        html += '<label class="iradio-box"><div class="iradio-blue batch" style="vertical-align:bottom;"><input name="inputType_checkbox" type="radio" value="2" style="position: absolute; opacity: 0;"></div> 不正常</label>';
        $("#Statuz").html(html);
        $(".single").addClass("checked");
        $('#divRemark').hide();
        $('input[name="inputType_checkbox"]').change(function () {
            if ($(this).prop("checked")) {
                var val1 = $(this).val();
                statuzChange(val1);
            }
        });
    }
    function statuzChange(selectid) {
        if (selectid == 1) {
            $(".single").addClass("checked");
            $(".batch").removeClass("checked");
            $('#divRemark').hide();
        } else {
            $(".batch").addClass("checked");
            $(".single").removeClass("checked");
            $('#divRemark').show();
        }
    }
    function loadSchoolStage() {
        ys.ajax({
            url: '@Url.Content("~/BusinessManage/FunRoomUse/GetSchoolStageJson")',
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1) {
                    $('#SchoolStage').ysComboBox({
                        data: obj.Data,
                        class: 'form-control',
                        key: 'DictionaryId',
                        value: 'DicName',
                        onChange: function () {
                            var selectid = $('#SchoolStage').ysComboBox('getValue');
                            loadSubject(selectid);
                            loadGradeClass(selectid);
                        }
                    });
                } else {
                    ys.msgError(obj.Message);
                }
            }
        });
    }

    function loadSubject(schoolstage) {
        if (schoolstage > 0) {
            ys.ajax({
                url: '@Url.Content("~/BusinessManage/FunRoomUse/GetSubjectJson")' + '?schoolstage=' + schoolstage,
                type: 'get',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        $('#DictionaryId1005').ysComboBox({
                            data: obj.Data,
                            class: 'form-control',
                            key: 'DictionaryId',
                            value: 'DicName',
                            onChange: function () {
                                var schoolstageval = $('#SchoolStage').ysComboBox('getValue');
                                var subject = $('#DictionaryId1005').ysComboBox('getValue');
                                loadFunRoom(schoolstageval, subject);
                            }
                        });
                        var defaultId = $("#hidSubject").val();
                        if (defaultId != undefined && parseFloat(defaultId) > 0) {
                            $("#DictionaryId1005").ysComboBox('setValue', defaultId);
                        }
                    } else {
                        defailBindSubject();
                        ys.msgError(obj.Message);
                    }
                }
            });
        } else {
            defailBindSubject();
        }
    }
    function defailBindSubject() {
        $('#DictionaryId1005').ysComboBox({
            data: [],
            class: 'form-control',
            onChange: function () {
                var schoolstageval = $('#SchoolStage').ysComboBox('getValue');
                var subject = $('#DictionaryId1005').ysComboBox('getValue');
                loadFunRoom(schoolstageval, subject);
            }
        });
    }
    function loadGradeClass(schoolstage) {
        if (schoolstage > 0) {
            ys.ajax({
                url: '@Url.Content("~/BusinessManage/FunRoomUse/GetGradeClassJson")' + '?SchoolStage=' + schoolstage,
                type: 'get',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        $('#UseClass').ysComboBox({
                            data: obj.Data,
                            class: 'form-control',
                            key: 'Id',
                            value: 'ClassName',
                            onChange: function () {

                            }
                        });
                        var defaultId = $("#hidGradeClass").val();
                        if (defaultId != undefined && parseFloat(defaultId) > 0) {
                            $("#UseClass").ysComboBox('setValue', defaultId);
                        }
                    } else {
                        defaultBindGradeClass();
                        ys.msgError(obj.Message);
                    }
                }
            });
        } else {
            defaultBindGradeClass();
        }
    }
    function defaultBindGradeClass() {
        $('#UseClass').ysComboBox({ data: [], class: 'form-control' });
    }
    function loadFunRoom(schoolstage, subject) {
        if (schoolstage > 0 && subject > 0) {
            ys.ajax({
                url: '@Url.Content("~/BusinessManage/FunRoom/GetListJson")' + '?SchoolStage=' + schoolstage + '&DictionaryId1005=' + subject,
                type: 'get',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        $('#FunRoomId').ysComboBox({
                            data: obj.Data,
                            class: 'form-control',
                            key: 'Id',
                            value: 'Name',
                            onChange: function () {

                            }
                        });
                        var defaultId = $("#hidFunRoom").val();
                        if (defaultId != undefined && parseFloat(defaultId) > 0) {
                            $("#FunRoomId").ysComboBox('setValue', defaultId);
                        } else if (obj.Data.length == 1) {
                            $("#FunRoomId").ysComboBox('setValue', obj.Data[0].Id);
                        }
                    } else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        } else {
            defaultBindComboBox($('#FunRoomId'));
        }
    }
    function defaultBindComboBox(obj) {
        obj.ysComboBox({ data: [], class: 'form-control' });
    }
    function loadCourseSection() {
          ys.ajax({
                url: '@Url.Content("~/OrganizationManage/CourseSection/GetListJson")',
                type: 'get',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        $('#CourseSectionId').ysComboBox({
                            data: obj.Data,
                            class: 'form-control',
                            key: 'Id',
                            value: 'SectionName'
                        });
                        if (obj.Data.length == 1) {
                            $("#CourseSectionId").ysComboBox('setValue', obj.Data[0].Id);
                        }
                    } else {
                        ys.msgError(obj.Message);
                    }
                }
            });
    }
    function loadUploadify() {
        $("#uploadify").uploadifive({
            'uploadScript': '/File/UploadFile?fc=1023',
            'cancelImg': '~/lib/uploadifive/uploadifive-cancel.png',
            'buttonText': '上 传',
            //'formData': { "token": "", "UserId": "0", "folder": "/UploadFile/Attachment" },
            'queueID': 'fileQueue',
            'auto': true,
            'multi': false,
            'fileDesc': '支持格式：*.jpg;*.gif;*.png;*.pdf;',
            'fileExt': '*.jpg;*.gif;*.png;*.pdf;',
            'onAddQueueItem': function (file) {

            },
            'onUploadComplete': function (fileObj, response) {
                if (typeof (response) == "string" && response != "") {
                    response = JSON.parse(response);
                    if (response.Tag == 1) {
                        ys.msgSuccess("上传成功！");
                        var id = response.Description;
                        var filePath = response.Data;
                        var fileTitle = response.Message;
                        var fileExt = filePath.substring(filePath.lastIndexOf('.')).toUpperCase();
                        if (fileExt == ".JPG" || fileExt == ".BMP" || fileExt == ".JPEG" || fileExt == ".PNG" || fileExt == ".GIF") {
                            $("#spanFile").append('<span class="keywords" idValue="' + id + '"> <a type="del" onclick="delFile(this,\'' + id + '\')"></a><a  modal="zoomImg"  href="javascript:void(0);" src="' + filePath + '" >' + fileTitle + '</a></span>');
                        } else {
                            $("#spanFile").append('<span class="keywords" idValue="' + id + '"> <a type="del" onclick="delFile(this,\'' + id + '\')"></a><a target="_blank" href="' + filePath + '" >' + fileTitle + '</a></span>');
                        }
                        imgages.showextalink();
                    } else {
                        ys.msgError(response.Message);
                    }
                }
            },
            'onFallback': function (event) {
                ys.msgError(event);
            }
        });
    }
    function getForm() {
        ys.ajax({
            url: '@Url.Content("~/BusinessManage/FunRoomUse/GetFormJson")' + '?id=' + id,
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1) {
                    if (id > 0) {
                        $('#form').setWebControls(obj.Data);
                        if (isNaN(obj.Data.UseDate) && obj.Data.UseDate.length >= 10) {
                            $("#UseDate").val(obj.Data.UseDate.substring(0, 10));
                        }
                        $("#divUseUserName").text(obj.Data.RealName);
                        $("#hidSubject").val(obj.Data.DictionaryId1005);
                        $("#hidGradeClass").val(obj.Data.UseClass);
                        $("#hidFunRoom").val(obj.Data.FunRoomId);
                        statuzChange(obj.Data.Statuz);
                    } else {
                        //obj.Data
                        var data = {}
                        $('#form').setWebControls(data);
                        $("#divUseUserName").text(obj.Data.RealName);
                    }
                }
            }
        });
    }

    function saveForm() {
        var postData = $('#form').getWebControls({ Id: id });
        //验证
        var errorMsg = '';
        if (!(postData.SchoolStage > 0)) {
            errorMsg += '请选择任课学段。<br/>';
        }
        if (!(postData.DictionaryId1005 > 0)) {
            errorMsg += '请选择任课学科。<br/>';
        }
        if (!(postData.UseClass > 0)) {
            errorMsg += '请选择任课班级。<br/>';
        }
        if (!(postData.FunRoomId > 0)) {
            errorMsg += '请选择使用地点 。<br/>';
        }
        if (!(postData.UseDate != undefined && postData.UseDate.length > 0)) {
            errorMsg += '请选择使用日期 。<br/>';
        }
        if (!(postData.CourseSectionId > 0)) {
            errorMsg += '请选择课程节次 。<br/>';
        }
        if (postData.Statuz != 1 && postData.Statuz!=2) {
            errorMsg += '请选择设备运行状态 。<br/>';
        } else if (postData.Statuz == 2){
            if (!(postData.RepairContent != undefined && postData.RepairContent.length > 0)) {
                errorMsg += '请填写问题描述，设备运行不正常必须填写 。<br/>';
            }
        }
        var imageArr = [];
        $(".keywords").each(function () {
            var idValue = $(this).attr("idValue");
            imageArr.push(idValue);
        });
        if (imageArr.length > 0) {
            postData.Imagez = imageArr.join(',');
        }
        if (errorMsg!='') {
            ys.msgError(errorMsg);
            return;
        }
        ys.ajax({
            url: '@Url.Content("~/BusinessManage/FunRoomUse/SaveFormJson")',
            type: 'post',
            data: postData,
            success: function (obj) {
                if (obj.Tag == 1) {
                    ys.msgSuccess(obj.Message);
                    var url = '@Url.Content("~/BusinessManage/FunRoomUse/Index")';
                    createMenuAndCloseCurrent(url, "已登记列表");
                }
                else {
                    ys.msgError(obj.Message);
                }
            }
        });
    }
    //删除附件
    function delFile(obj, value) {
        $(obj).parent().remove();
        imgages.showextalink();
    }
    function updateClassInfo() {
        parent.updateClassInfo();
    }
</script>

