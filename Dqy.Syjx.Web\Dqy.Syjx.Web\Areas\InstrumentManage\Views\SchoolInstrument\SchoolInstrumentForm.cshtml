﻿@{
    Layout = "~/Views/Shared/_Index.cshtml";
}
@inject Microsoft.AspNetCore.Hosting.IWebHostEnvironment HostingEnvironment
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/fileinput/5.0.3/css/fileinput.min.css"))
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/fileinput/5.0.3/js/fileinput.min.js"))
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/uploadifive/File.min.css"))
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/uploadifive/File.min.js"))

<style type="text/css">
 /*   .check-box {
        width: 100px;
    }*/

    .iradio-box {
        width: 100px;
    }

    .iradio-box {
        position: initial;
        display: inline-block;
    }

    .iradio-blue {
        position: inherit;
        display: inline-block;
          top: 6px;
    }
</style>
<div class="wrapper animated fadeInRight">
    <div class="btn-group-sm hidden-xs" id="toolbar">
    </div>
    <div class="col-sm-12 select-table table-striped">

        <div class="ibox-title">
            <h5>仪器录入
            <span class="glyphicon glyphicon-question-sign titleprompt inputprompt" aria-hidden="true" data-container="body" data-toggle="tooltip" data-placement="right"
      data-content="如录入有误，请到【仪器列表】中进行修改或删除。"></span>
            </h5>
        </div>
        <div class="card-body">
            <form id="form" class="form-horizontal m">
                <div class="form-group row" id="divInputType">
                    <label class="col-sm-2 control-label ">录入方式<font class="red"> *</font></label>
                    <div class="col-sm-8" id="inputType" style="bottom:-6px;">
                    </div>
                </div>
                <div class="divform divSingle">
                    <div class="form-group row">
                        <label class="col-sm-2 control-label ">
                            <span class="glyphicon glyphicon-question-sign titleprompt inputprompt" aria-hidden="true" data-container="body" data-toggle="tooltip" data-placement="right"
                                  data-content="名称可修改"></span>
                            仪器名称<font class="red"> *</font>
                        </label>
                        <div class="col-sm-8">
                            <input id="name" col="Name" type="text" class="form-control" onclick="chooseStandard(0);" />
                            <input id="instrumentStandardId" col="InstrumentStandardId" type="hidden" />
                        </div>
                        <div class="col-sm-2" style="padding-left:0px;">
                            <a class="btn btn-sm btn-success" onclick="chooseStandard(1)"><i class="fa fa-search"></i> 选择</a>
                        </div>
                    </div>

                     <div class="form-group row" style="display:none;">
                        <label class="col-sm-2 control-label ">名称代码<font class="red"> *</font></label>
                        <div class="col-sm-8">
                            <input type="text" class="form-control" id="code1" col="Code1" readonly />
                        </div>
                    </div>

                    <div class="form-group row">
                        <label class="col-sm-2 control-label ">分类代码<font class="red"> *</font></label>
                        <div class="col-sm-8">
                            <input type="text" class="form-control" id="code" col="Code" readonly />
                        </div>
                    </div>
                    <div class="form-group row">
                        <label class="col-sm-2 control-label ">单位<font class="red"> *</font></label>
                        <div class="col-sm-8">
                            <input id="unitName" col="UnitName" type="text" class="form-control" readonly />
                        </div>
                    </div>
                    <div class="form-group row">
                        <label class="col-sm-2 control-label ">选择规格属性<font class="red"> *</font></label>
                        <div class="col-sm-8">
                            <div id="modelStandardId"></div>
                        </div>
                    </div>
                    <div class="form-group row" id="divModel" style="display:none;">
                        <label class="col-sm-2 control-label ">规格属性<font class="red"> *</font></label>
                        <div class="col-sm-8">
                            <input id="model" col="Model" type="text" class="form-control" />
                        </div>
                    </div>
                    <div class="form-group row">
                        <label class="col-sm-2 control-label ">数量<font class="red"> *</font></label>
                        <div class="col-sm-8">
                            <input id="num" col="Num" type="text" class="form-control" />
                        </div>
                    </div>
                    <div class="form-group row">
                        <label class="col-sm-2 control-label " id="labPrice">单价(元)</label>
                        <div class="col-sm-8">
                            <input id="price" col="Price" type="text" class="form-control" />
                        </div>
                    </div>
                    <div class="form-group row">
                        <label class="col-sm-2 control-label ">
                            <span class="glyphicon glyphicon-question-sign titleprompt inputprompt" aria-hidden="true" data-container="body" data-toggle="tooltip" data-placement="right"
                                  data-content="请确认所选仪器与适用学科匹配"></span>
                            适用学科<font class="red"> *</font>
                        </label>
                        <div class="col-sm-8">
                            <div id="courseId"></div>
                        </div>
                    </div>
                    <div class="form-group row">
                        <label class="col-sm-2 control-label ">适用学段<font class="red"> *</font></label>
                        <div class="col-sm-8">
                            <div id="stageId"></div>
                        </div>
                    </div>
                    <div class="form-group row">
                        <label class="col-sm-2 control-label ">采购日期<font class="red"> *</font></label>
                        <div class="col-sm-8">
                            <input id="purchaseDate" col="PurchaseDate" type="text" class="form-control" readonly />
                        </div>
                    </div>
                    <div class="form-group row">
                        <label class="col-sm-2 control-label " id="labWarrantyMonth">质保期(月)</label>
                        <div class="col-sm-8">
                            <input id="WarrantyMonth" col="WarrantyMonth" type="text" class="form-control" />
                        </div>
                    </div>
                    <div class="form-group row">
                        <label class="col-sm-2 control-label " id="labSupplierName">供应商</label>
                        <div class="col-sm-8">
                            <input id="SupplierName" col="SupplierName" type="text" class="form-control" />
                        </div>
                    </div>
                    <div class="form-group row">
                        <label class="col-sm-2 control-label " id="labBrand">品牌</label>
                        <div class="col-sm-8">
                            <input id="Brand" col="Brand" type="text" class="form-control" />
                        </div>
                    </div>
                    <div class="form-group row">
                        <label class="col-sm-2 control-label " id="labBrand">是否自制教具<font class="red"> *</font></label>
                        <div class="col-sm-8" id="isSelfMade" col="IsSelfMade" style="bottom:6px;left:-20px;">
                        </div>
                    </div>
                    <div class="form-group row">
                        <label class="col-sm-2 control-label " id="labImage">
                            <span class="glyphicon glyphicon-question-sign titleprompt inputprompt" aria-hidden="true" data-container="body" data-toggle="tooltip" data-placement="right"
                                  data-content="请使用仪器正面图片，小于5M，支持图片格式"></span>
                            仪器图片
                        </label>
                        <div class="col-sm-8">
                            <input type="file" name="uploadify" id="uploadify" />
                            <div style="height: 55px;display:none;" id="fileQueue"></div>
                            <input id="AttachmentId" col="AttachmentId" type="hidden" value="0" />
                            <input id="Path" col="Path" type="hidden" />
                            <a modal="zoomImg" href="javascript:void(0);" id="awardSrc">
                                <img modal="zoomImg" href="javascript:void(0);" src="" id="imgAward" class="col-sm-8" style="max-width:200px;padding: 10px 0px;" />
                            </a>
                        </div>
                    </div>
                    <div class="form-group row" id="divStoragePlace" style="display:none;">
                        <label class="col-sm-2 control-label ">
                            <span class="glyphicon glyphicon-question-sign titleprompt inputprompt" aria-hidden="true" data-container="body" data-toggle="tooltip" data-placement="right"
                                  data-content="1、如缺少场所和橱柜名称，请到实验（专用）室管理栏中添加添加；<br />
                                                2、如不存放到橱柜，则只要选择场所名称；"></span>
                            存放地<font class="red"> *</font>
                        </label>
                        <div class="col-sm-8">
                            <div id="storagePlace" col="StoragePlace"></div>
                        </div>
                    </div>
                    <div class="form-group row" id="divFloor" style="display:none;">
                        <label class="col-sm-2 control-label " id="labFloor">
                            <span class="glyphicon glyphicon-question-sign titleprompt inputprompt" aria-hidden="true" data-container="body" data-toggle="tooltip" data-placement="right"
                                  data-content="层次是指橱柜中位置，如：上3、下2等。"></span>
                            层次
                        </label>
                        <div class="col-sm-8">
                            <input id="Floor" col="Floor" class="form-control" placeholder="层次" />
                        </div>
                    </div>
                </div>
                <div class="divform divImport" style="display:none;">
                    <div class="form-group row">
                        <label class="col-sm-2 control-label ">选择文件</label>
                        <div class="col-sm-6">
                            <input id="importFile" type="file">
                        </div>
                    </div>
                    <div class="form-group row">
                        <label class="col-sm-2 control-label "></label>
                        <div class="col-sm-5">
                            <div class="control-label" style="text-align:left">
                                <a href='@Url.Content("~/template/仪器导入模板.xlsx")' class="btn btn-secondary btn-xs"><i class="fa fa-file-excel-o"></i> 下载模板</a>
                            </div>
                        </div>

                    </div>
                    <div class="form-group row">
                        <label class="col-sm-2 control-label "></label>
                        <div class="col-sm-10 text-danger">
                            提示：请先下载模板，必须严格按照模板格式导入数据！
                        </div>
                    </div>
                </div>
                <div class="divform divStockImport" style="display:none;">
                    <div class="form-group row">
                        <label class="col-sm-2 control-label ">下载模板</label>
                        <div class="col-sm-6">
                            <table id="gridTable" data-mobile-responsive="true"></table>
                        </div>
                    </div>
                    <div class="form-group row">
                        <label class="col-sm-2 control-label ">选择文件</label>
                        <div class="col-sm-6">
                            <input id="importFile2" type="file">
                        </div>
                    </div>
                    <div class="form-group row">
                        <label class="col-sm-2 control-label "></label>
                        <div class="col-sm-10 text-danger">
                            提示：请先下载模板，必须严格按照模板格式导入数据！
                        </div>
                    </div>
                </div>
            </form>
        </div>
        <div class="btn-group-sm hidden-xs" id="toolbar" style="text-align: center;">
            <a id="btnAdd" class="btn btn-info" onclick="saveForm();"><i class="fa fa-save"></i> 保存</a>
        </div>
    </div>
</div>
<script type="text/javascript">
    var url = '@Url.Content("~/InstrumentManage/SchoolInstrument/SchoolInstrumentIndex")';
    var title = '仪器列表';

    var id = ys.request("id");

    var InputType = 1; //录入方式
    var filePath = undefined; //批量导入文件
    var filePath2 = undefined;//存量导入文件
    var SaveValiDate = [];//存储需要验证的字段
    var IsShowFloor = 0;

    var IsEdit = false;
    var IsEditCourse = false;
    
    var modelStandardData = [];
    var BasePageCode = 101020;//仪器存量导入模板(101020)
    $(function () {
        $(".inputprompt").popover({
            trigger: 'hover',
            html: true
        });

        if (id > 0) {
            $('#divInputType').hide();
        }
        else {
            showInfo();
        }
        laydate.render({ elem: '#purchaseDate', format: 'yyyy-MM-dd', trigger: 'click', position: 'fixed' });
        $("#isSelfMade").ysRadioBox({ data: ys.getJson(@Html.Raw(typeof(IsEnum).EnumToDictionaryString())), class: "form-control" });
        $("#isSelfMade").ysRadioBox('setValue', 0);

        getForm();

        loadUploadify();

        $("#importFile").fileinput({
            language: 'zh',
            'uploadUrl': '@Url.Content("~/File/UploadExcel")' + '?fileModule=@UploadFileType.Import.ParseToInt()',
            showPreview: false,
            allowedFileExtensions: ['xls', 'xlsx']
        }).on("fileuploaded", function (event, data) {
            var obj = data.response;
            if (obj.Tag == 1) {
                filePath = obj.Data;
            }
            else {
                filePath = '';
            }
        });
        $("#importFile2").fileinput({
            language: 'zh',
            'uploadUrl': '@Url.Content("~/File/UploadExcel")' + '?fileModule=@UploadFileType.Import.ParseToInt()',
            showPreview: false,
            allowedFileExtensions: ['xls', 'xlsx']
        }).on("fileuploaded", function (event, data) {
            var obj = data.response;
            if (obj.Tag == 1) {
                filePath2 = obj.Data;
            }
            else {
                filePath2 = '';
            }
        });

        loadFormVali();
    });

    function showInfo() {
        var html = '';
        html += '<label class="iradio-box"><div class="iradio-blue single" style="vertical-align:bottom;"><input name="inputType_checkbox" type="radio" value="1" style="position: absolute; opacity: 0;"></div> 单条录入</label>';
        html += '<label class="iradio-box"><div class="iradio-blue batch" style="vertical-align:bottom;"><input name="inputType_checkbox" type="radio" value="2" style="position: absolute; opacity: 0;"></div> 批量导入</label>';
        html += '<label class="iradio-box"><div class="iradio-blue stock" style="vertical-align:bottom;"><input name="inputType_checkbox" type="radio" value="3" style="position: absolute; opacity: 0;"></div> 存量导入</label>';
        $("#inputType").html(html);
        $(".single").addClass("checked");

        $('input[name="inputType_checkbox"]').change(function () {
            if ($(this).prop("checked")) {
                $('.iradio-blue').removeClass('checked');
                $('.divform').hide();
                var val = $(this).val();
                if (val == 1) {
                    $(".single").addClass("checked");
                    $('.divSingle').show();
                } else if (val == 2) {
                    $(".batch").addClass("checked");
                    $('.divImport').show();
                }
                else {
                    $(".stock").addClass("checked");
                    $('.divStockImport').show();
                    initGrid();
                }
                InputType = val;
            }
        });
    }

    function chooseStandard(type) {
        if (!$('#instrumentStandardId').val() || $('#name').val() == '' || type == 1) {
            ys.openDialog({
                title: '选择仪器',
                content: '@Url.Content("~/InstrumentManage/PurchaseDeclaration/StandardChoose")',
                width: '980px',
                height: '600px',
                callback: function (index, layero) {
                    var iframeWin = window[layero.find('iframe')[0]['name']];
                    iframeWin.saveForm(index);
                }
            });
        }
    }

    function getForm() {
        if (id > 0) {
            ys.ajax({
                url: '@Url.Content("~/InstrumentManage/SchoolInstrument/GetFormJson")' + '?id=' + id,
                type: 'get',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        obj.Data.PurchaseDate = obj.Data.PurchaseDate.substring(0, 10).replace(/\//g, '-');
                        IsEdit = true;
                        IsEditCourse = true;
                        getInstrumentModel(obj.Data.InstrumentStandardId, obj.Data.ModelStandardId);
                        $('#divModel').show();
                        getCourse(obj.Data.CourseId,obj.Data.StageId);
                        getSchoolStage(obj.Data.CourseId ,obj.Data.StageId);
                        if (obj.Data.Statuz == @InstrumentInputStatuzEnum.AlreadyInputStorage.ParseToInt()) {
                            loadStoragePlace(obj.Data.FunRoomId, obj.Data.CupboardId);
                            $('#num').attr('readonly', true);
                            $('#price').attr('readonly', true);
                            $('#divStoragePlace').show();
                            url = '@Url.Content("~/InstrumentManage/SchoolInstrument/EditInfoIndex")';
                            title = '信息修改';
                        }
                        $('#form').setWebControls(obj.Data);

                        if (obj.Data.AttachmentList.length > 0) {
                            $('#AttachmentId').val(obj.Data.AttachmentList[0].Id);
                            $('#Path').val(obj.Data.AttachmentList[0].Path);
                            $('#imgAward').attr("src", obj.Data.AttachmentList[0].Path);
                            $("#awardSrc").attr("src", obj.Data.AttachmentList[0].Path);
                            imgages.showextalink();
                        }

                        if (obj.Data.VarietyAttribute == "@VarietyAttributeEnum.Reagent.ParseToInt()") {
                            //试剂类可自定义输入计量单位
                            $('#unitName').prop('readonly', false);
                        }
                        else {
                            $('#unitName').prop('readonly', true);
                        }

                        var code = obj.Data.ModelCode;
                        if(code == ""){
                            code = obj.Data.Code;
                        }

                        $("#code").val(code);

                    }
                }
            });
        }
        else {
            getInstrumentModel(0);
            getSchoolStage();
            getCourse();
        }
    }

    function setInstrumentStandard(id, name) {
        $('#instrumentStandardId').val(id);
        $('#name').val(name);
        getInstrumentEntity(id);
        getInstrumentModel(id);
    }

    function getInstrumentModel(id, defaultValue) {
        $('#modelStandardId').ysComboBox({
            data: [],
            key: 'Id',
            value: 'Model',
            class: "form-control",
            onChange: function () {
                if (!IsEdit) {

                    var modeleId = $("#modelStandardId").ysComboBox("getValue");
                    if (modeleId == '') {
                        $('#divModel').hide();
                        $('#model').val('');
                    }
                    else {
                        $('#divModel').show();
                        var model = $("#modelStandardId option:checked").text();
                        model = model == '手动填写' ? '' : model;
                        $('#model').val(model);
                    }

                    if (modeleId == 0) {
                        var modeCode = $("#code").val();
                        if (modeCode != undefined && modeCode != "") {
                            modeCode = modeCode.substring(0, 9) + "00";
                            $("#code").val(modeCode);
                        }
                    } else {
                        //
                        $.each(modelStandardData, function (index, item) {
                            if (item.Id == modeleId) {
                                $("#code").val(item.Code);
                                return false;
                            }
                        });
                    }
                }
                else {
                    IsEdit = false;
                }
            }
        });
        if (id > 0) {
            var param = { Pid: id, ClassType: 2 };
            ys.ajax({
                url: '@Url.Content("~/InstrumentManage/InstrumentStandard/GetInstrumentStandard")',
                data: { param: param },
                type: 'post',
                success: function (obj) {
                    if (obj.Tag == 1 && obj.Data) {
                        obj.Data.push({ Id: 0, Model: '手动填写' });
                        $('#modelStandardId').ysComboBox({
                            data: obj.Data,
                            key: 'Id',
                            value: 'Model',
                            class: "form-control"
                        });

                         modelStandardData = obj.Data;

                        if (defaultValue > -1) {
                            $('#modelStandardId').ysComboBox('setValue', defaultValue);
                        }
                        else {
                            if (obj.Data.length == 1) {
                                $('#modelStandardId').ysComboBox('setValue', obj.Data[0].Id);
                            }
                        }
                    }
                }
            });
        }
    }

    function getInstrumentEntity(id) {
        ys.ajax({
            url: '@Url.Content("~/InstrumentManage/InstrumentStandard/GetEntity")' + '?id=' + id,
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1 && obj.Data) {
                    $('#unitName').val(obj.Data.UnitName);
                    $('#name').val(obj.Data.Name);
                    $('#code').val(obj.Data.Code);

                    if (obj.Data.VarietyAttribute == "@VarietyAttributeEnum.Reagent.ParseToInt()") {
                        //试剂类可自定义输入计量单位
                        $('#unitName').prop('readonly', false);
                    }
                    else {
                        $('#unitName').prop('readonly', true);
                    }
                }
            }
        });
    }
    var Com_CourseId=0;
    function getSchoolStage(courseId, defaultValue) { 
        if(courseId!=Com_CourseId || Com_CourseId==0){
            Com_CourseId=courseId
        }else{
            return false;
        }
        ys.ajax({
            url: '@Url.Content("~/BusinessManage/UserSchoolStageSubject/GetSchoolStageByUser")' + '?courseId=' + (courseId || 0),
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1) {
                    $('#stageId').html('');
                    $('#stageId').ysCheckBox({
                        data: obj.Data,
                        key: 'DictionaryId',
                        value: 'DicName',
                        class: "form-control"
                    });
                    if (obj.Data.length == 0) {
                        ComBox.LoadPageMessage('您管理的学科', '/BusinessManage/UserSchoolStageSubject/StageSubjectInput', '实验员授权', @RoleEnum.BusinessManager.ParseToInt());
                    }
                    else {
                        if (defaultValue) {
                            $('#stageId').ysCheckBox('setValue', defaultValue);
                        }
                        else if (obj.Data.length == 1) {
                            $('#stageId').ysCheckBox('setValue', obj.Data[0].DictionaryId);
                        }
                    }
                }
            }
        });
    }

    function getCourse(defaultValue) {
        ys.ajax({
            url: '@Url.Content("~/BusinessManage/UserSchoolStageSubject/GetSubjectByUser")',
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1) {
                    $('#courseId').ysComboBox({
                        data: obj.Data,
                        key: 'DictionaryId',
                        value: 'DicName',
                        class: "form-control",
                        onChange: function () {
                            if (!IsEditCourse) getSchoolStage($('#courseId').ysComboBox('getValue'), '');
                            else IsEditCourse = false;
                        }
                    });
                    if (defaultValue > 0) {
                        $('#courseId').ysComboBox('setValue', defaultValue);
                    }
                    else if (obj.Data.length == 1) {
                        $('#courseId').ysComboBox('setValue', obj.Data[0].DictionaryId);
                    }
                }
            }
        });
    }

    function loadUploadify() {
        $("#uploadify").uploadifive({
            'uploadScript': '/File/UploadFile?fc=1200',
            'cancelImg': '~/lib/uploadifive/uploadifive-cancel.png',
            'buttonText': '上 传',
            'queueID': 'fileQueue',
            'auto': true,
            'multi': false,
            'fileDesc': '支持格式：*.jpg;*.jpeg;*.png;',
            'fileExt': '*.jpg;*.jpeg;*.png;',
            'onAddQueueItem': function (file) {

            },
            'onUploadComplete': function (fileObj, response) {
                if (typeof (response) == "string" && response != "") {
                    response = JSON.parse(response);
                    if (response.Tag == 1) {
                        $('#AttachmentId').val(response.Description);
                        $('#Path').val(response.Data);
                        $('#imgAward').attr("src", response.Data);
                        $("#awardSrc").attr("src", response.Data);
                        imgages.showextalink();
                        ys.msgSuccess("上传成功！");
                    } else {
                        ys.msgError(response.Message);
                    }
                }
            },
            'onFallback': function (event) {
                ys.msgError(event);
            }
        });
    }

    function saveForm(index) {
        if (InputType == 2) {
            if (!filePath) {
                ys.alertError('文件未上传或者上传失败');
                return;
            }
            var postData = $("#form").getWebControls();
            postData.FilePath = filePath;
            ys.ajax({
                url: '@Url.Content("~/InstrumentManage/SchoolInstrument/ImportInstrumentJson")',
                type: "post",
                data: postData,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        var url = '@Url.Content("~/InstrumentManage/SchoolInstrument/SchoolInstrumentIndex")';
                        createMenuAndCloseCurrent(url, "仪器列表");
                    }
                    else {
                        ys.alertError(obj.Message)

                    }
                }
            });
        }
        else if (InputType == 3) {
            if (!filePath2) {
                ys.alertError('文件未上传或者上传失败');
                return;
            }
            var postData = $("#form").getWebControls();
            postData.FilePath = filePath2;
            ys.ajax({
                url: '@Url.Content("~/InstrumentManage/SchoolInstrument/ImportStockInstrumentJson")',
                type: "post",
                data: postData,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        var url = '@Url.Content("~/InstrumentManage/SchoolInstrument/SchoolInstrumentIndex")';
                        createMenuAndCloseCurrent(url, "仪器列表");
                    }
                    else {
                        ys.alertError(obj.Message)

                    }
                }
            });
        }
        else {
            if ($('#form').validate().form()) {
                var postData = $('#form').getWebControls({
                    Id: id,
                    ModelStandardId: $('#modelStandardId').ysComboBox('getValue'),
                    StageId: $('#stageId').ysCheckBox('getValue'),
                    CourseId: $('#courseId').ysComboBox('getValue')
                });
                var checkErr = checkMessage(postData);
                if (checkErr != '') {
                    ys.msgError(checkErr);
                    return false;
                }

                if ($('#storagePlace').ysComboBoxTree('getValue') == undefined) {
                    postData.StoragePlace = "";
                }

                ys.ajax({
                    url: '@Url.Content("~/InstrumentManage/SchoolInstrument/SaveFormJson")',
                    type: 'post',
                    data: postData,
                    success: function (obj) {
                        if (obj.Tag == 1) {
                            ys.msgSuccess(obj.Message);
                            createMenuAndCloseCurrent(url, title);
                        }
                        else {
                            ys.msgError(obj.Message);
                        }
                    }
                });
            }
        }
    }

    function loadStoragePlace(defaultFunRoomId, defaultCupboardId) {
        $('#storagePlace').ysComboBoxTree({
            url: '@Url.Content("~/BusinessManage/Cupboard/GetCupboardTreeListJson")' + '?searchType=0',
            class: "form-control",
            key: 'id',
            value: 'name',
            callback: {
                customOnClick: function (event, treeId, treeNode) {
                    var storagePlace = $('#storagePlace').ysComboBoxTree('getValue');
                    if (storagePlace.indexOf(',') != -1) {
                        IsShowFloor = 1;
                        $('#divFloor').show();
                    } else {
                        IsShowFloor = 0;
                        $('#divFloor').hide();
                        $('#floor').val('');
                    }
                }
            }
        });
        if (defaultCupboardId > 0) {
            $('#storagePlace').ysComboBoxTree('setValue', defaultCupboardId);
        }
        else if (defaultFunRoomId > 0) {
            $('#storagePlace').ysComboBoxTree('setValue', defaultFunRoomId);
        }
    }

    //保存校验
    function checkMessage(postData) {
        var checkErr = '';
        if (!postData.ModelStandardId) {
            checkErr += '请选择规格属性！<br />';
        }
        else if (postData.Model.trim() == '') {
            checkErr += '请填写规格属性！<br />';
        }
        if (!postData.StageId) {
            checkErr += '请选择适用学段！<br />';
        }
        if (!postData.CourseId) {
            checkErr += '请选择适用学科 ！<br />';
        }
        if (postData.IsSelfMade == '' || postData.IsSelfMade == undefined) {
            checkErr += '请选择是否自制教具 ！<br />';
        }
        if (SaveValiDate != undefined && SaveValiDate.length > 0) {
            SaveValiDate.map(function (item, i) {
                if (item.typecode == "Image") {
                    if (!(parseFloat(postData.AttachmentId) > 0)) {
                        checkErr += (item.verifyhint + '<br/>');
                    }
                } else if (item.typecode == "SupplierName") {
                    if (!(postData.SupplierName != undefined && postData.SupplierName.length > 0)) {
                        checkErr += (item.verifyhint + '<br/>');
                    }
                } else if (item.typecode == "Brand") {
                    if (!(postData.Brand != undefined && postData.Brand.length > 0)) {
                        checkErr += (item.verifyhint + '<br/>');
                    }
                } else if (item.typecode == "Floor" && IsShowFloor == 1) {
                    if (!(postData.Floor != undefined && postData.Floor.length > 0)) {
                        checkErr += (item.verifyhint + '<br/>');
                    }
                } else if (item.typecode == "WarrantyMonth") {
                    if (!(postData.WarrantyMonth != undefined && postData.WarrantyMonth.length > 0)) {
                        checkErr += (item.verifyhint + '<br/>');
                    }
                }
            });
        }

        return checkErr;
    }



    function initGrid() {
        var queryUrl = '@Url.Content("~/InstrumentManage/SchoolInstrument/GetInstrumentEvProjectDownloadList")';
        $('#gridTable').ysTable({
            url: queryUrl,
            showRefresh: false,
            showToggle: false,
            showColumns: false,
            showToolbar: false,
            pagination: false,
            showExportSetBtn : true,
            showExportSetCode: BasePageCode,
            columns: [
                { field: 'EvaluateName', title: '评估项目名称', sortable: true, halign: 'center', valign: 'middle', },
                {
                    field: 'SchoolStage', title: '适用学段', sortable: true, width: 80, halign: 'center', valign: 'middle', align: 'center',
                    formatter: function (value, row, index) {
                        return row.SchoolStageName;
                    }
                },
                {
                    field: 'DictionaryId1005', title: '适用学科', sortable: true, width: 100, halign: 'center', valign: 'middle', align: 'center',
                    formatter: function (value, row, index) {
                        return row.SubjectName;
                    }
                },
                {
                    title: '操作',
                    halign: 'center',
                    align: 'center',
                    width: 50,
                    formatter: function (value, row, index) {
                        return $.Format('<a class="btn btn-success btn-xs" href="#" onclick="downloadExcel(\'{0}\',\'{1}\')"><i class="fa fa-download"></i> 下载</a>', row.Id, row.EvaluateName);
                    }
                }
            ],
            queryParams: function (params) {
                var pagination = $('#gridTable').ysTable('getPagination', params);
                var queryString = $('#searchDiv').getWebControls(pagination);
                return queryString;
            }
        });
    }

    function downloadExcel(id ,name) {
        var url = '@Url.Content("~/InstrumentManage/SchoolInstrument/ExportInstrumentEvStandardTemplate")';//仪器存量导入模板(101020)
        var postData = { id: id, name: name , basepagecode: 100000000 };
        postData.basepagecode = BasePageCode;//仪器存量导入模板(101020)
        ys.exportExcel(url, postData);
    }

    //#region 加载配置验证信息
    function loadFormVali() {
        ys.ajax({
            url: '@Url.Content("~/SystemManage/ConfigSet/GetListByCode")',
            type: 'Post',
            data: { typecode: '1SYYQ-2YQRK-1YQLR' },
            success: function (obj) {
                if (obj.Tag == 1) {
                    var validateRule = {};
                    var validateMessage = {};
                    validateRule.name = { required: true };
                    validateRule.model = { required: true };
                    validateRule.unitName = { required: true };
                    validateRule.num = { required: true, number: true, thanMinValue: 0 };
                    //validateRule.price = { required: true, number: true };
                    validateRule.stageId = { required: true };
                    validateRule.courseId = { required: true };
                    validateRule.purchaseDate = { required: true };
                    //validateRule.warrantyMonth = { required: true, number: true };
                    if (obj.Data != undefined && obj.Data.length > 0) {
                        for (var i = 0; i < obj.Data.length; i++) {
                            var typecode = obj.Data[i].TypeCode.replaceAll("1SYYQ-2YQRK-1YQLR-", "");
                            if (obj.Data[i].ConfigValue == "1") {
                                var verifyhint = "";
                                if (obj.Data[i].VerifyHint != undefined && obj.Data[i].VerifyHint.length > 0) {
                                    verifyhint = obj.Data[i].VerifyHint;
                                }
                                $("#lab" + typecode).append('<font class="red"> *</font>');
                                if (typecode == "SupplierName") {
                                    validateRule.SupplierName = { required: true };
                                    validateMessage.SupplierName = { required: verifyhint };
                                    SaveValiDate.push({ typecode: typecode, verifyhint: verifyhint });
                                } else if (typecode == "Brand") {
                                    validateRule.Brand = { required: true };
                                    validateMessage.Brand = { required: verifyhint };
                                } else if (typecode == "Image" || typecode == "Floor") {
                                    SaveValiDate.push({ typecode: typecode, verifyhint: verifyhint });
                                }
                                else if (typecode == "Price") {
                                    validateRule.price = { required: true };
                                    validateMessage.price = { required: verifyhint };
                                    SaveValiDate.push({ typecode: typecode, verifyhint: verifyhint });
                                }
                                else if (typecode == "WarrantyMonth") {
                                    validateRule.WarrantyMonth = { required: true, number: true };
                                    SaveValiDate.push({ typecode: typecode, verifyhint: verifyhint });
                                }
                            } else {
                                if (typecode == 'Price' && $('#price').val() == 0) $('#price').val('');

                                if (typecode == 'WarrantyMonth' && $('#WarrantyMonth').val() == 0) $('#WarrantyMonth').val('');
                            }
                        }
                    }
                    $('#form').validate({
                        rules: validateRule,
                        messages: validateMessage
                    });
                }
            }
        });
    }

    //#endregion
</script>
