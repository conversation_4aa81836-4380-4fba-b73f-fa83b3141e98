﻿@{
    Layout = "~/Views/Shared/_FormWhite.cshtml";
}

<div class="wrapper animated fadeInRight">
    <form id="form" class="form-horizontal m">
        <div class="form-group row">
            <label class="col-sm-3 control-label ">
                <span class="glyphicon glyphicon-question-sign titleprompt inputprompt" aria-hidden="true" data-container="body" data-toggle="tooltip" data-placement="right"
                      data-content="目前该字段仅作用于小程序端菜单"></span>
                角色Id<font class="red"> *</font>
            </label>
            <div class="col-sm-8">
                <input id="roleId" col="RoleId" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">角色名称<font class="red"> *</font></label>
            <div class="col-sm-8">
                <input id="roleName" col="RoleName" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">性质类型</label>
            <div class="col-sm-8" id="unitType" col="UnitType">
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">显示顺序</label>
            <div class="col-sm-8">
                <input id="roleSort" col="RoleSort" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">状态</label>
            <div class="col-sm-8" id="roleStatus" col="RoleStatus">
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">备注</label>
            <div class="col-sm-8">
                <textarea id="remark" col="Remark" class="form-control" style="height:120px;max-width:100%"></textarea>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">菜单权限</label>
            <div class="col-sm-8">
                <div id="menuTree" class="ztree"></div>
            </div>
        </div>
    </form>
</div>

<script type="text/javascript">
    var id = ys.request("id");
    $(function () {
        $(".inputprompt").popover({
            trigger: 'hover',
            placement: 'right',
            html: true
        });

        $("#unitType").ysRadioBox({ data: ys.getJson(@Html.Raw(typeof(UnitTypeEnum).EnumToDictionaryString())) });
        $("#roleStatus").ysRadioBox({ data: ys.getJson(@Html.Raw(typeof(StatusEnum).EnumToDictionaryString())) });

        if (id == 0) {
            $("#unitType").ysRadioBox('setValue', @UnitTypeEnum.School.ParseToInt());
        }

        loadMenuTree();

        getForm();

        $("#form").validate({
            rules: {
                roleName: { required: true },
                roleId: { required: true, number: true }
            }
        });
    });

    function loadMenuTree() {
        $('#menuTree').ysTree({
            async: false,
            url: '@Url.Content("~/SystemManage/Menu/GetMenuTreeListRemarkJson")',
            check: { enable: true },
            expandLevel: 0
        })
    }

    function getForm() {
        if (id > 0) {
            ys.ajax({
                url: '@Url.Content("~/SystemManage/Role/GetFormJson")' + '?id=' + id,
                type: "get",
                success: function (obj) {
                    if (obj.Tag == 1) {
                        $("#form").setWebControls(obj.Data);
                        $('#menuTree').ysTree("setCheckedNodes", obj.Data.MenuIds);
                    }
                }
            });
        }
        else {
            ys.ajax({
                url: '@Url.Content("~/SystemManage/Role/GetMaxSortJson")',
                type: "get",
                success: function (obj) {
                    if (obj.Tag == 1) {
                        var defaultData = {};
                        defaultData.RoleSort = obj.Data;
                        defaultData.RoleStatus = "@StatusEnum.Yes.ParseToInt()";
                        $("#form").setWebControls(defaultData);
                    }
                }
            });
        }
    }

    function saveForm(index) {
        if ($("#form").validate().form()) {
            var postData = $("#form").getWebControls({ Id: id });
            postData.MenuIds = $('#menuTree').ysTree("getCheckedNodes");
            ys.ajax({
                url: '@Url.Content("~/SystemManage/Role/SaveFormJson")',
                type: "post",
                data: postData,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        parent.$('#gridTable').ysTable('refresh');parent.resetToolbarStatus();
                        parent.layer.close(index);
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        }
    }
</script>
