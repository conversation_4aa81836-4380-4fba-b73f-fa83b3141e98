﻿@{
    Layout = "~/Views/Shared/_FormWhite.cshtml";
}

<div class="wrapper animated fadeInRight">
    <form id="form" class="form-horizontal m">
        <div class="form-group row">
            <label class="col-sm-3 control-label ">学段<font class="red"> *</font></label>
            <div class="col-sm-8">
                <div id="schoolStage" col="SchoolStage"></div>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">年级<font class="red"> *</font></label>
            <div class="col-sm-8">
                <div id="gradeId" col="GradeId"></div>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">学科<font class="red"> *</font></label>
            <div class="col-sm-8">
                <div id="courseId" col="CourseId"></div>
            </div>
        </div>
        @*<div class="form-group row">
            <label class="col-sm-3 control-label ">学年<font class="red"> *</font></label>
            <div class="col-sm-8">
                <div id="schoolYearStart" col="SchoolYearStart"></div>
            </div>
        </div>*@
        <div class="form-group row">
            <label class="col-sm-3 control-label ">学期<font class="red"> *</font></label>
            <div class="col-sm-8">
                <div id="schoolTerm" col="SchoolTerm"></div>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">必做演示实验</label>
            <label class="col-sm-2 control-label ">
                总数：<span id="spNeedShowNum"></span>
            </label>
            <label class="col-sm-2 control-label ">考核数：<font class="red"> *</font></label>
            <div class="col-sm-4">
                <input class="form-control" id="standardNeedShowNum" col="StandardNeedShowNum" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">必做分组实验</label>
            <label class="col-sm-2 control-label ">
                总数：<span id="spNeedGroupNum"></span>
            </label>
            <label class="col-sm-2 control-label ">考核数：<font class="red"> *</font></label>
            <div class="col-sm-4">
                <input class="form-control" id="standardNeedGroupNum" col="StandardNeedGroupNum" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">选做演示实验</label>
            <label class="col-sm-2 control-label ">
                总数：<span id="spOptionalShowNum"></span>
            </label>
            <label class="col-sm-2 control-label ">考核数：<font class="red"> *</font></label>
            <div class="col-sm-4">
                <input class="form-control" id="standardOptionalShowNum" col="StandardOptionalShowNum" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">选做分组实验</label>
            <label class="col-sm-2 control-label ">
                总数：<span id="spOptionalGroupNum"></span>
            </label>
            <label class="col-sm-2 control-label ">考核数：<font class="red"> *</font></label>
            <div class="col-sm-4">
                <input class="form-control" id="standardOptionalGroupNum" col="StandardOptionalGroupNum" />
            </div>
        </div>
        <div class="form-group row" id="divTagMsg">
            <label class="col-sm-2 control-label red "><font class="red">注：</font></label>
            <div class="col-sm-10">
                1：如果是不同区县不同版本的情况，实验室里可能不准，确保准确请点击修改或者更新数量。<br/>
                2：如是市级平台多个区县，实验室里统计的是多区县的，请添加后在对应区县点修改或者更新数量。
            </div>
        </div>
    </form>
</div>

<script type="text/javascript">
    var id = ys.request("id");
    $(function () {
        getForm();

        $('#form').validate({
            rules: {
                standardNeedShowNum: { required: true },
                standardNeedGroupNum: { required: true },
                standardOptionalShowNum: { required: true },
                standardOptionalGroupNum: { required: true }
            }
        });
    });

    function getForm() {
        if (id > 0) {
            $("#divTagMsg").hide();
            ys.ajax({
                url: '@Url.Content("~/ExperimentTeachManage/TextbookVersionParameter/GetFormJson")' + '?id=' + id,
                type: 'get',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        loadSchoolStage(obj.Data.SchoolStage);
                        loadGrade(obj.Data.SchoolStage, obj.Data.GradeId);
                        loadCourse(obj.Data.GradeId, obj.Data.CourseId);
                        //loadSchoolYear(obj.Data.SchoolYearStart);
                        loadSchoolTerm(obj.Data.SchoolTerm);
                        $('#standardNeedShowNum').val(obj.Data.StandardNeedShowNum);
                        $('#standardNeedGroupNum').val(obj.Data.StandardNeedGroupNum);
                        $('#standardOptionalShowNum').val(obj.Data.StandardOptionalShowNum);
                        $('#standardOptionalGroupNum').val(obj.Data.StandardOptionalGroupNum);
                    }
                }
            });
        }
        else {
            $("#divTagMsg").show();
            loadSchoolStage();
            loadGrade();
            loadCourse();
            loadSchoolTerm();
            //loadSchoolYear();
        }
    }

    function saveForm(index) {
        if ($('#form').validate().form()) {
            var postData = $('#form').getWebControls({ Id: id });
            ys.ajax({
                url: '@Url.Content("~/ExperimentTeachManage/TextbookVersionParameter/SaveFormJson")',
                type: 'post',
                data: postData,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        parent.$('#gridTable').ysTable('refresh');parent.resetToolbarStatus();
                        parent.layer.close(index);
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        }
    }

    function loadSchoolStage(defaultValue) {
        $('#schoolStage').ysComboBox({
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetListJson")' + '?TypeCode=@DicTypeCodeEnum.SchoolStage.ParseToInt()',
            key: 'DictionaryId',
            value: 'DicName',
            class: 'form-control',
            onChange: function () {
                loadGrade($('#schoolStage').ysComboBox('getValue'));
            }
        });
        if (defaultValue > 0) $('#schoolStage').ysComboBox('setValue', defaultValue);
    }
    function loadGrade(schoolStage, defaultValue) {
        $('#gradeId').ysComboBox({
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetChildListJson")' + '?TypeCode=@DicTypeCodeEnum.Grade.ParseToInt()&Pid=' + schoolStage,
            key: 'DictionaryId',
            value: 'DicName',
            class: 'form-control',
            onChange: function () {
                loadCourse($('#gradeId').ysComboBox('getValue'));
            }
        });
        if (defaultValue > 0) $('#gradeId').ysComboBox('setValue', defaultValue);
    }
    function loadCourse(gradeId, defaultValue) {
        $('#courseId').ysComboBox({
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetChildToListJson")' + '?TypeCode=@DicTypeCodeEnum.Course.ParseToInt()&Pid=' + gradeId + '&Ids=1005002,1005003,1005004,1005005',
            key: 'DictionaryId',
            value: 'DicName',
            class: 'form-control',
            onChange: function () {
                loadBaseNumTotal();
            }
        });
        if (defaultValue > 0) $('#courseId').ysComboBox('setValue', defaultValue);
    }
    function loadSchoolTerm(defaultValue) {
        $("#schoolTerm").ysComboBox({
            data: ys.getJson(@Html.Raw(typeof(SchoolTermEnum).EnumToDictionaryString())), class: 'form-control' ,
            onChange: function () {
                loadBaseNumTotal();
            }
        });
        if (defaultValue > 0) $('#schoolTerm').ysComboBox('setValue', defaultValue);
    }
    function loadSchoolYear(defaultValue) {
        var arr = [];
        var startYear = new Date().getFullYear() + 1;
        for (var i = startYear; i > (startYear - 3); i--) {
            var name = (i + '').substr(2);
            name = parseInt(name);
            arr.push({ id: (i - 1), name: (name - 1) + '~' + name })
        }
        $('#schoolYearStart').ysComboBox({
            data: arr,
            class: 'form-control',
            key: 'id',
            value: 'name',
            onChange: function () {
                loadBaseNumTotal();
            }
        });
        if (defaultValue > 0) $('#schoolYearStart').ysComboBox('setValue', defaultValue);
    }

    function loadBaseNumTotal() {
        var schoolStage = $('#schoolStage').ysComboBox('getValue');
        var gradeId = $('#gradeId').ysComboBox('getValue');
        var courseId = $('#courseId').ysComboBox('getValue');
        var schoolTerm = $('#schoolTerm').ysComboBox('getValue');
        //var schoolYearStart = $('#schoolYearStart').ysComboBox('getValue');

        if (schoolStage && gradeId && courseId && schoolTerm) {
            ys.ajax({
                url: '@Url.Content("~/ExperimentTeachManage/TextbookVersionParameter/GetTextbookVersionBaseNumTotal")' + '?SchoolStage=' + schoolStage
                    + '&GradeId=' + gradeId + '&CourseId=' + courseId + '&SchoolTerm=' + schoolTerm,
                type: 'get',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        var data = obj.Data[0];
                        $('#spNeedShowNum').html(data.needshownum);
                        $('#spNeedGroupNum').html(data.needgroupnum);
                        $('#spOptionalShowNum').html(data.optionalshownum);
                        $('#spOptionalGroupNum').html(data.optionalgroupnum);
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        }
    }
</script>

