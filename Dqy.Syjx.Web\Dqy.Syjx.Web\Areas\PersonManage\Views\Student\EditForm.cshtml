﻿@{
    Layout = "~/Views/Shared/_FormWhite.cshtml";
}

<div class="wrapper animated fadeInRight">
    <form id="form" class="form-horizontal m">
        <div class="form-group row">
            <label class="col-sm-3 control-label ">学号<font class="red"> *</font></label>
            <div class="col-sm-8">
                <input id="studentNo" col="StudentNo" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">姓名<font class="red"> *</font></label>
            <div class="col-sm-8">
                <input id="studentName" col="StudentName" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">性别</label>
            <div class="col-sm-8">
                <div class="col-sm-4" id="Sex" col="Sex"></div>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">学籍号<font class="red"> *</font></label>
            <div class="col-sm-8">
                <input id="iDCard" col="IDCard" type="text" class="form-control" />
            </div>
        </div>
    </form>
</div>

<script type="text/javascript">
    var id = ys.request("id");
    $(function () {
        loadBindSex();
    });
    function loadBindSex(){
        var sexArr=[];
        sexArr.push({id:"男",name:"男"});
        sexArr.push({id:"女",name:"女"});
        $("#Sex").ysRadioBox({ 
            class: 'form-control',
            key: 'id',
            value: 'name',
            data: sexArr
        });
    }

    function getForm() {
        if (id > 0) {
            ys.ajax({
                url: '@Url.Content("~/PersonManage/Student/GetFormJson")' + '?id=' + id,
                type: 'get',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        $('#form').setWebControls(obj.Data);
                    }
                }
            });
        }
        else {
            var defaultData = {};
            $('#form').setWebControls(defaultData);
        }
    }

    function saveForm(index) {
        if ($('#form').validate().form()) {
            var postData = $('#form').getWebControls({ Id: id });
            postData.SchoolGradeClassId = parent.id;
            ys.ajax({
                url: '@Url.Content("~/PersonManage/Student/SaveFormJson")',
                type: 'post',
                data: postData,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        parent.searchGrid();
                        parent.layer.close(index);
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        }
    }
</script>

