﻿@{
    Layout = "~/Views/Shared/_Index.cshtml";
}
@inject Microsoft.AspNetCore.Hosting.IWebHostEnvironment HostingEnvironment
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/uploadifive/File.min.css"))
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/uploadifive/File.min.js"))

<div class="container-div">
    <div class="btn-group-sm hidden-xs" id="toolbar">
    </div>
    <div class="col-sm-12 select-table table-striped">
        <div class="ibox-title">
            <h5>制度与队伍建设</h5>
            <!--帮助文档需要内容,id值必须为“helpBtn”-->
            <div class="fa fa-question-circle" id="helpBtn" style="margin:0px;padding:0px;padding-left:5px;"></div>
        </div>

        <div class="card-body">
            <form id="form" class="form-horizontal m">
                <input id="Id" col="Id" type="hidden" value="0" />
                <div class="form-group row">
                    <label class="col-sm-3 control-label ">校长：</label>
                    <div class="col-sm-9">
                        <input id="Headmaster" col="Headmaster" type="text" class="form-control" />
                    </div>
                </div>
                <div class="form-group row">
                    <label class="col-sm-3 control-label ">分管校长：</label>
                    <div class="col-sm-9">
                        <input id="BranchHeadmaster" col="BranchHeadmaster" type="text" class="form-control" />
                    </div>
                </div>
                <div class="form-group row">
                    <label class="col-sm-3 control-label ">
                        <span class="glyphicon glyphicon-question-sign titleprompt inputprompt" aria-hidden="true" data-container="body" data-toggle="tooltip" data-placement="right"
                              data-content="如：安全处或总务处"></span>
                        分管部门：
                    </label>
                    <div class="col-sm-9">
                        <input id="BranchDepartment" col="BranchDepartment" type="text" class="form-control" />
                    </div>
                </div>
                <div class="form-group row">
                    <label class="col-sm-3 control-label ">
                        <span class="glyphicon glyphicon-question-sign titleprompt inputprompt" aria-hidden="true" data-container="body" data-toggle="tooltip" data-placement="right"
                              data-content="可填2人"></span>
                        部门负责人：
                    </label>
                    <div class="col-sm-9">
                        <input id="DepartmentHead" col="DepartmentHead" type="text" class="form-control" />
                    </div>
                </div>


                <div class="form-group row">
                    <label class="col-sm-3 control-label ">
                        <span class="glyphicon glyphicon-question-sign titleprompt inputprompt" aria-hidden="true" data-container="body" data-toggle="tooltip" data-placement="right"
                              data-content="文件小于5M，支持pdf文件"></span>
                        安全管理制度：
                    </label>
                    <div class="col-sm-9">
                        <div style="float:left;">
                            <input type="file" name="uploadifyManager" id="uploadifyManager" />
                            <div style="height: 55px;display:none;" id="fileQueueManager"></div>
                        </div>
                        &nbsp;&nbsp;
                        <div id="spanFile_1300" style="float:left;padding-left:10px;">
                        </div>
                    </div>
                </div>


                <div class="form-group row">
                    <label class="col-sm-3 control-label ">
                        <span class="glyphicon glyphicon-question-sign titleprompt inputprompt" aria-hidden="true" data-container="body" data-toggle="tooltip" data-placement="right"
                              data-content="文件小于5M，支持pdf文件"></span>
                        安全考核制度：
                    </label>
                    <div class="col-sm-8">
                        <div style="float:left;">
                            <input type="file" name="uploadifyExamine" id="uploadifyExamine" />
                            <div style="height: 55px;display:none;" id="fileQueueExamine"></div>
                        </div>
                        &nbsp;&nbsp;
                        <div id="spanFile_1301" style="float: left; padding-left: 10px;">
                        </div>
                    </div>
                </div>

                <div class="form-group row">
                    <label class="col-sm-3 control-label ">
                        <span class="glyphicon glyphicon-question-sign titleprompt inputprompt" aria-hidden="true" data-container="body" data-toggle="tooltip" data-placement="right"
                              data-content="文件小于5M，支持pdf文件"></span>
                        事故应急预案：
                    </label>
                    <div class="col-sm-9">
                        <div style="float:left;">
                            <input type="file" name="uploadifyPlan" id="uploadifyPlan" />
                            <div style="height: 55px;display:none;" id="fileQueuePlan"></div>
                        </div>
                        &nbsp;&nbsp;
                        <div id="spanFile_1302" style="float: left; padding-left: 10px;">
                        </div>
                    </div>
                </div>
            </form>
        </div>
        <div class="btn-group-sm hidden-xs" id="toolbar" style="text-align: center;">
            <a id="btnAdd" class="btn btn-info" onclick="saveForm();"><i class="fa fa-edit"></i> 保存</a>
        </div>
    </div>
</div>
<!--帮助文档需要内容,id值必须为“tipMsg”-->
<div id="tipMsg"></div>
<script type="text/javascript">
    $(function () {
        $(".inputprompt").popover({
            trigger: 'hover',
            html: true
        });



        getForm();

        $("#form").validate({
            rules: {

            }
        });

        $("#uploadifyManager").uploadifive({
            'uploadScript': '/File/UploadFile?fc=1300',
            'cancelImg': '~/lib/uploadifive/uploadifive-cancel.png',
            'buttonText': '上 传',
            'queueID': 'fileQueueManager',
            'auto': true,
            'multi': true,
            'fileDesc': '支持格式：*.jpg;*.gif;*.png;',
            'fileExt': '*.jpg;*.gif;*.png;',
            'onAddQueueItem': function (file) {

            },
            'onUploadComplete': function (fileObj, response) {
                if (typeof (response) == "string" && response != "") {
                    response = JSON.parse(response);
                    if (response.Tag == 1) {

                        //console.log("response:" + JSON.stringify(response));

                        $('#spanLinkManager').text('上传成功，请单击保存按钮，保存文件。');
                        ys.msgSuccess("上传成功！");

                        var id = response.Description;
                        var filePath = response.Data;
                        var fileTitle = response.Message;
                        var fileExt = filePath.substring(filePath.lastIndexOf('.')).toUpperCase();
                        if (fileExt == ".JPG" || fileExt == ".BMP" || fileExt == ".JPEG" || fileExt == ".PNG" || fileExt == ".GIF") {
                            $("#spanFile_1300").append('<span class="keywords" idValue="' + id + '"> <a type="del" onclick="delFile(this,\'' + id + '\')"></a><a  modal="zoomImg"  href="javascript:void(0);" src="' + filePath + '" >' + fileTitle + '</a></span>');
                        } else {
                            $("#spanFile_1300").append('<span class="keywords" idValue="' + id + '"> <a type="del" onclick="delFile(this,\'' + id + '\')"></a><a target="_blank" href="' + filePath + '" download="' + fileTitle + '" >' + fileTitle + '</a></span>');
                        }
                        imgages.showextalink();
                    } else {
                        $('#spanLinkManager').text(response.Message);
                        ys.msgError(response.Message);
                    }
                }
            },
            'onFallback': function (event) {
                ys.msgError(event);
            }
        });

        $("#uploadifyExamine").uploadifive({
            'uploadScript': '/File/UploadFile?fc=1301',
            'cancelImg': '~/lib/uploadifive/uploadifive-cancel.png',
            'buttonText': '上 传',
            'queueID': 'fileQueueExamine',
            'auto': true,
            'multi': true,
            'fileDesc': '支持格式：*.jpg;*.gif;*.png;',
            'fileExt': '*.jpg;*.gif;*.png;',
            'onAddQueueItem': function (file) {

            },
            'onUploadComplete': function (fileObj, response) {
                if (typeof (response) == "string" && response != "") {
                    response = JSON.parse(response);
                    if (response.Tag == 1) {

                        $('#spanLinkExamine').text('上传成功，请单击保存按钮，保存文件。');
                        ys.msgSuccess("上传成功！");

                        var id = response.Description;
                        var filePath = response.Data;
                        var fileTitle = response.Message;
                        var fileExt = filePath.substring(filePath.lastIndexOf('.')).toUpperCase();
                        if (fileExt == ".JPG" || fileExt == ".BMP" || fileExt == ".JPEG" || fileExt == ".PNG" || fileExt == ".GIF") {
                            $("#spanFile_1301").append('<span class="keywords" idValue="' + id + '"> <a type="del" onclick="delFile(this,\'' + id + '\')"></a><a  modal="zoomImg"  href="javascript:void(0);" src="' + filePath + '" >' + fileTitle + '</a></span>');
                        } else {
                            $("#spanFile_1301").append('<span class="keywords" idValue="' + id + '"> <a type="del" onclick="delFile(this,\'' + id + '\')"></a><a target="_blank" href="' + filePath + '" download="' + fileTitle + '">' + fileTitle + '</a></span>');
                        }
                        imgages.showextalink();
                    } else {
                        $('#spanLinkExamine').text(response.Message);
                        ys.msgError(response.Message);
                    }
                }
            },
            'onFallback': function (event) {
                ys.msgError(event);
            }
        });

        $("#uploadifyPlan").uploadifive({
            'uploadScript': '/File/UploadFile?fc=1302',
            'cancelImg': '~/lib/uploadifive/uploadifive-cancel.png',
            'buttonText': '上 传',
            'queueID': 'fileQueuePlan',
            'auto': true,
            'multi': true,
            'fileDesc': '支持格式：*.jpg;*.gif;*.png;',
            'fileExt': '*.jpg;*.gif;*.png;',
            'onAddQueueItem': function (file) {

            },
            'onUploadComplete': function (fileObj, response) {
                if (typeof (response) == "string" && response != "") {
                    response = JSON.parse(response);
                    if (response.Tag == 1) {

                        $('#spanLinkPlan').text('上传成功，请单击保存按钮，保存文件。');
                        ys.msgSuccess("上传成功！");

                        var id = response.Description;
                        var filePath = response.Data;
                        var fileTitle = response.Message;
                        var fileExt = filePath.substring(filePath.lastIndexOf('.')).toUpperCase();
                        if (fileExt == ".JPG" || fileExt == ".BMP" || fileExt == ".JPEG" || fileExt == ".PNG" || fileExt == ".GIF") {
                            $("#spanFile_1302").append('<span class="keywords" idValue="' + id + '"> <a type="del" onclick="delFile(this,\'' + id + '\')"></a><a  modal="zoomImg"  href="javascript:void(0);" src="' + filePath + '" >' + fileTitle + '</a></span>');
                        } else {
                            $("#spanFile_1302").append('<span class="keywords" idValue="' + id + '"> <a type="del" onclick="delFile(this,\'' + id + '\')"></a><a target="_blank" href="' + filePath + '" download="' + fileTitle + '" >' + fileTitle + '</a></span>');
                        }
                        imgages.showextalink();
                    } else {
                        $('#spanLinkPlan').text(response.Message);
                        ys.msgError(response.Message);
                    }
                }
            },
            'onFallback': function (event) {
                ys.msgError(event);
            }
        });
    });

    function getForm() {
        ys.ajax({
            url: '@Url.Content("~/BusinessManage/BaseFieldConfig/GetFormJsonByUnitId")',
            type: "get",
            success: function (obj) {
                if (obj.Tag == 1) {
                    var result = obj.Data;
                    $("#form").setWebControls(result);

                    //调用显示附件信息
                    var fileList = obj.Data.Atts;
                    if (fileList.length > 0) {
                        for (var i = 0; i < fileList.length; i++) {
                            var id = fileList[i].Id;
                            var filePath = fileList[i].Path;
                            var fileExt = fileList[i].Ext.toUpperCase();
                            var fileTitle = fileList[i].Title;
                            var fileFileCategory = fileList[i].FileCategory;
                            var objDiv = $("#spanFile_" + fileFileCategory);
                            if (fileExt == ".JPG" || fileExt == ".BMP" || fileExt == ".JPEG" || fileExt == ".PNG" || fileExt == ".GIF") {
                                objDiv.append('<span class="keywords" idValue="' + id + '"> <a type="del" onclick="delFile(this,\'' + id + '\')"></a><a  modal="zoomImg"  href="javascript:void(0);" src="' + filePath + '" >' + fileTitle + '</a></span>');
                            } else {
                                objDiv.append('<span class="keywords" idValue="' + id + '"> <a type="del" onclick="delFile(this,\'' + id + '\')"></a><a target="_blank" href="' + filePath + '" download="' + fileTitle + '">' + fileTitle + '</a></span>');
                            }
                        }
                        imgages.showextalink();
                    }
                }
            }
        });
    }

    function saveForm() {
        if ($('#form').validate().form()) {
            var postData = $('#form').getWebControls();
            //获取附件Id集合
            var objFileId = [];
            $(".keywords").each(function () {
                var idValue = $(this).attr("idValue");
                objFileId.push(idValue);
            });
            if (objFileId.length > 0) {
                postData.ListFileId = objFileId;
            }
            ys.ajax({
                url: '@Url.Content("~/BusinessManage/BaseFieldConfig/SaveFormJson")',
                type: 'post',
                data: postData,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        $("#Id").val(obj.Data);
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        }
    }

    //删除附件
    function delFile(obj, value) {
        var postData = { fileId: value };
        ys.ajax({
            url: '@Url.Content("~/PersonManage/UserTrainInfo/DeleteUserTrainInfoFile")',
            type: 'post',
            data: postData,
            success: function (obj) {
                //console.log("obj" + JSON.stringify(obj));
                if (obj.Tag == 1) {
                    ys.msgSuccess("删除成功");
                }
                else {
                    ys.msgError(obj.Message);
                }
            }
        });
        var vHtml = $(obj).parent().parent().html();
        var divHtml = $(obj).parent().parent().parent().html();
        var objDivId = $(obj).parent().parent().parent().find("div").attr("id");
        $(obj).parent().remove();
        imgages.showextalink();
    }

</script>
