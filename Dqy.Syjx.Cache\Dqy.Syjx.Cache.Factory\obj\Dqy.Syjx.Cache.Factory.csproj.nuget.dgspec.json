{"format": 1, "restore": {"D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Cache\\Dqy.Syjx.Cache.Factory\\Dqy.Syjx.Cache.Factory.csproj": {}}, "projects": {"D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Cache\\Dqy.Syjx.Cache.Factory\\Dqy.Syjx.Cache.Factory.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Cache\\Dqy.Syjx.Cache.Factory\\Dqy.Syjx.Cache.Factory.csproj", "projectName": "Dqy.Syjx.Cache.Factory", "projectPath": "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Cache\\Dqy.Syjx.Cache.Factory\\Dqy.Syjx.Cache.Factory.csproj", "packagesPath": "C:\\NuGetPackages", "outputPath": "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Cache\\Dqy.Syjx.Cache.Factory\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["c:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config", "C:\\Program Files (x86)\\NuGet\\Config\\nuget.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Cache\\Dqy.Syjx.Cache.Interface\\Dqy.Syjx.Cache.Interface.csproj": {"projectPath": "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Cache\\Dqy.Syjx.Cache.Interface\\Dqy.Syjx.Cache.Interface.csproj"}, "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Cache\\Dqy.Syjx.MemoryCache\\Dqy.Syjx.MemoryCache.csproj": {"projectPath": "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Cache\\Dqy.Syjx.MemoryCache\\Dqy.Syjx.MemoryCache.csproj"}, "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Cache\\Dqy.Syjx.RedisCache\\Dqy.Syjx.RedisCache.csproj": {"projectPath": "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Cache\\Dqy.Syjx.RedisCache\\Dqy.Syjx.RedisCache.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\6.0.428\\RuntimeIdentifierGraph.json"}}}, "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Cache\\Dqy.Syjx.Cache.Interface\\Dqy.Syjx.Cache.Interface.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Cache\\Dqy.Syjx.Cache.Interface\\Dqy.Syjx.Cache.Interface.csproj", "projectName": "Dqy.Syjx.Cache.Interface", "projectPath": "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Cache\\Dqy.Syjx.Cache.Interface\\Dqy.Syjx.Cache.Interface.csproj", "packagesPath": "C:\\NuGetPackages", "outputPath": "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Cache\\Dqy.Syjx.Cache.Interface\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["c:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config", "C:\\Program Files (x86)\\NuGet\\Config\\nuget.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\6.0.428\\RuntimeIdentifierGraph.json"}}}, "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Cache\\Dqy.Syjx.MemoryCache\\Dqy.Syjx.MemoryCache.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Cache\\Dqy.Syjx.MemoryCache\\Dqy.Syjx.MemoryCache.csproj", "projectName": "Dqy.Syjx.MemoryCache", "projectPath": "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Cache\\Dqy.Syjx.MemoryCache\\Dqy.Syjx.MemoryCache.csproj", "packagesPath": "C:\\NuGetPackages", "outputPath": "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Cache\\Dqy.Syjx.MemoryCache\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["c:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config", "C:\\Program Files (x86)\\NuGet\\Config\\nuget.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Cache\\Dqy.Syjx.Cache.Interface\\Dqy.Syjx.Cache.Interface.csproj": {"projectPath": "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Cache\\Dqy.Syjx.Cache.Interface\\Dqy.Syjx.Cache.Interface.csproj"}, "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Util\\Dqy.Syjx.Util\\Dqy.Syjx.Util.csproj": {"projectPath": "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Util\\Dqy.Syjx.Util\\Dqy.Syjx.Util.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Microsoft.Extensions.Caching.Memory": {"target": "Package", "version": "[7.0.0, )"}, "MySqlConnector": {"target": "Package", "version": "[2.2.5, )"}, "Oracle.ManagedDataAccess.Core": {"target": "Package", "version": "[3.21.100, )"}, "Senparc.Weixin.WxOpen": {"target": "Package", "version": "[3.15.13, )"}, "System.Drawing.Common": {"target": "Package", "version": "[7.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\6.0.428\\RuntimeIdentifierGraph.json"}}}, "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Cache\\Dqy.Syjx.RedisCache\\Dqy.Syjx.RedisCache.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Cache\\Dqy.Syjx.RedisCache\\Dqy.Syjx.RedisCache.csproj", "projectName": "Dqy.Syjx.RedisCache", "projectPath": "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Cache\\Dqy.Syjx.RedisCache\\Dqy.Syjx.RedisCache.csproj", "packagesPath": "C:\\NuGetPackages", "outputPath": "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Cache\\Dqy.Syjx.RedisCache\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["c:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config", "C:\\Program Files (x86)\\NuGet\\Config\\nuget.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Cache\\Dqy.Syjx.Cache.Interface\\Dqy.Syjx.Cache.Interface.csproj": {"projectPath": "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Cache\\Dqy.Syjx.Cache.Interface\\Dqy.Syjx.Cache.Interface.csproj"}, "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Util\\Dqy.Syjx.Util\\Dqy.Syjx.Util.csproj": {"projectPath": "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Util\\Dqy.Syjx.Util\\Dqy.Syjx.Util.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"StackExchange.Redis": {"target": "Package", "version": "[2.2.50, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\6.0.428\\RuntimeIdentifierGraph.json"}}}, "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Entity\\Dqy.Syjx.Enum\\Dqy.Syjx.Enum.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Entity\\Dqy.Syjx.Enum\\Dqy.Syjx.Enum.csproj", "projectName": "Dqy.Syjx.Enum", "projectPath": "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Entity\\Dqy.Syjx.Enum\\Dqy.Syjx.Enum.csproj", "packagesPath": "C:\\NuGetPackages", "outputPath": "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Entity\\Dqy.Syjx.Enum\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["c:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config", "C:\\Program Files (x86)\\NuGet\\Config\\nuget.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\6.0.428\\RuntimeIdentifierGraph.json"}}}, "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Util\\Dqy.Syjx.Util\\Dqy.Syjx.Util.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Util\\Dqy.Syjx.Util\\Dqy.Syjx.Util.csproj", "projectName": "Dqy.Syjx.Util", "projectPath": "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Util\\Dqy.Syjx.Util\\Dqy.Syjx.Util.csproj", "packagesPath": "C:\\NuGetPackages", "outputPath": "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Util\\Dqy.Syjx.Util\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["c:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config", "C:\\Program Files (x86)\\NuGet\\Config\\nuget.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Entity\\Dqy.Syjx.Enum\\Dqy.Syjx.Enum.csproj": {"projectPath": "D:\\工作管理\\项目研发\\实验教学管理平台\\04系统开发\\Dqy.Syjx\\Dqy.Syjx.Entity\\Dqy.Syjx.Enum\\Dqy.Syjx.Enum.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"AutoMapper": {"target": "Package", "version": "[10.1.1, )"}, "Castle.Core.AsyncInterceptor": {"target": "Package", "version": "[1.7.0, )"}, "EFCore.Sharding.SqlServer": {"target": "Package", "version": "[5.0.16, )"}, "Microsoft.AspNetCore.Authentication.JwtBearer": {"target": "Package", "version": "[6.0.11, )"}, "Microsoft.AspNetCore.Mvc.NewtonsoftJson": {"target": "Package", "version": "[6.0.19, )"}, "NLog.Web.AspNetCore": {"target": "Package", "version": "[4.14.0, )"}, "NPOI": {"target": "Package", "version": "[2.6.2, )"}, "NetDevPack.Security.JwtExtensions": {"target": "Package", "version": "[6.0.2, )"}, "QRCoder": {"target": "Package", "version": "[1.4.1, )"}, "SkiaSharp": {"target": "Package", "version": "[2.88.8, )"}, "System.Drawing.Common": {"target": "Package", "version": "[7.0.0, )"}, "System.Linq.Dynamic.Core": {"target": "Package", "version": "[1.3.5, )"}, "System.ServiceModel.Primitives": {"target": "Package", "version": "[4.8.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\6.0.428\\RuntimeIdentifierGraph.json"}}}}}