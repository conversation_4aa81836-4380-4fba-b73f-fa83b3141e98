﻿@{
    Layout = "~/Views/Shared/_FormWhite.cshtml";
}

<div class="wrapper animated fadeInRight">
    <form id="form" class="form-horizontal m">
        <div class="form-group row">
            <label class="col-sm-3 control-label ">学年：</label>
            <div class="col-sm-8">
                <div id="schoolYearStart" col="SchoolYearStart"></div>
            </div>
        </div>
    </form>
</div>

<script type="text/javascript">
    var ids = ys.request("ids");
    $(function () {
        loadSchoolYear();
    });

    function loadSchoolYear() {
        ComBox.SchoolTermYear($('#schoolYearStart'), 'form-control', '' ,3);
    }

    function saveForm(index) {
        if (ids == '') {
            ys.msgError('请选择需要设置的数据！');
            return false;
        }

        var postData = $('#form').getWebControls({ Ids: ids });

        if (postData.SchoolYearStart == '') {
            ys.msgError('请选择学年！');
            return false;
        }

        ys.ajax({
            url: '@Url.Content("~/ExperimentTeachManage/TextbookVersionCurrent/CopyAddFormJson")',
            type: 'post',
            data: postData,
            success: function (obj) {
                if (obj.Tag == 1) {
                    ys.msgSuccess(obj.Message);
                    parent.$('#gridTable').ysTable('refresh');parent.resetToolbarStatus();
                    parent.layer.close(index);
                }
                else {
                    ys.msgError(obj.Message);
                }
            }
        });
    }
</script>

