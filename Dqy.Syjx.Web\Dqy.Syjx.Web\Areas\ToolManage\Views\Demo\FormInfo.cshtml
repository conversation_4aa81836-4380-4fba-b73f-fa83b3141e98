﻿@{
    Layout = "~/Views/Shared/_Index.cshtml";
}
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 select-table table-striped">

            <div class="ibox-title">
                <h5>单位管理</h5>
            </div>

            <div class="card-body">
                <form id="fileConfigForm" class="form-horizontal m">
                    <div class="form-group row">
                        <div class="col-sm-6">
                            <label class="col-sm-4 control-label ">类名前缀<font class="red"> *</font></label>
                            <div class="col-sm-8">
                                <input id="classPrefix" col="ClassPrefix" type="text" class="form-control" />
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <label class="col-sm-4 control-label ">类名描述</label>
                            <div class="col-sm-8">
                                <input id="classDescription" col="ClassDescription" type="text" class="form-control" />
                            </div>
                        </div>
                    </div>
                    <div class="form-group row">
                        <div class="col-sm-6">
                            <label class="col-sm-4 control-label ">实体类名<font class="red"> *</font></label>
                            <div class="col-sm-8">
                                <input id="entityName" col="EntityName" type="text" data-suffix="Entity" readonly="readonly" class="form-control" />
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <label class="col-sm-4 control-label ">映射类名<font class="red"> *</font></label>
                            <div class="col-sm-8">
                                <input id="entityMapName" col="EntityMapName" type="text" data-suffix="EntityMap" readonly="readonly" class="form-control" />
                            </div>
                        </div>
                    </div>
                    <div class="form-group row">
                        <div class="col-sm-6">
                            <label class="col-sm-4 control-label ">业务类名<font class="red"> *</font></label>
                            <div class="col-sm-8">
                                <input id="businessName" col="BusinessName" type="text" data-suffix="BLL" readonly="readonly" class="form-control" />
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <label class="col-sm-4 control-label ">服务类名<font class="red"> *</font></label>
                            <div class="col-sm-8">
                                <input id="serviceName" col="ServiceName" type="text" data-suffix="Service" readonly="readonly" class="form-control" />
                            </div>
                        </div>
                    </div>
                    <div class="form-group row">
                        <div class="col-sm-6">
                            <label class="col-sm-4 control-label ">控制器名<font class="red"> *</font></label>
                            <div class="col-sm-8">
                                <input id="controllerName" col="ControllerName" type="text" data-suffix="Controller" readonly="readonly" class="form-control" />
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <label class="col-sm-4 control-label ">列表页名<font class="red"> *</font></label>
                            <div class="col-sm-8">
                                <input id="pageIndexName" col="PageIndexName" type="text" data-suffix="Index" readonly="readonly" class="form-control" />
                            </div>
                        </div>
                    </div>
                    <div class="form-group row">
                        <div class="col-sm-12">
                            <label class="col-sm-2 control-label ">表单页名<font class="red"> *</font></label>
                            <div class="col-sm-10">
                                <input id="pageFormName" col="PageFormName" type="text" data-suffix="Form" readonly="readonly" class="form-control" />
                            </div>
                        </div>
                    </div>
                    <div class="form-group row">
                        <div class="col-sm-12">
                            <label class="col-sm-2 control-label ">状态<font class="red"> *</font></label>
                            <div class="col-sm-10">
                                <div id="roleStatus" col="RoleStatus"></div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>

        </div>
    </div>
</div>

<script type="text/javascript">
    var id = ys.request("id");
    $(function () {
        $("#roleStatus").ysRadioBox({ data: ys.getJson(@Html.Raw(typeof(StatusEnum).EnumToDictionaryString())) });

    });
     
</script>
