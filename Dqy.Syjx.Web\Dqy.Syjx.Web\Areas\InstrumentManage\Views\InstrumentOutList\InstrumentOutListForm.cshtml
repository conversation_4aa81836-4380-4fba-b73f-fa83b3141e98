﻿@{
    Layout = "~/Views/Shared/_FormWhite.cshtml";
}

<div class="wrapper animated fadeInRight">
    <form id="form" class="form-horizontal m">
        <div class="form-group row">
            <label class="col-sm-3 control-label ">出库数量<font class="red"> *</font></label>
            <div class="col-sm-8">
                <input id="outNum" col="OutNum" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label " id="labContent">用途</label>
            <div class="col-sm-8">
                <input id="Content" col="Content" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">领用人<font class="red"> *</font></label>
            <div class="col-sm-8">
                <div id="receiveUserId" col="ReceiveUserId"></div>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">出库时间<font class="red"> *</font></label>
            <div class="col-sm-8">
                <input id="outDate" col="OutDate" type="text" class="form-control" />
            </div>
        </div>
    </form>
</div>

<script type="text/javascript">
    var schoolInstrumentId = ys.request("schoolInstrumentId");
    $(function () {
        laydate.render({ elem: '#outDate', format: 'yyyy-MM-dd', trigger: 'click', position: 'fixed' });
        loadUser();
        loadFormVali();
    });

    function saveForm(index) {
        if ($('#form').validate().form()) {
            var postData = $('#form').getWebControls({ SchoolInstrumentId: schoolInstrumentId, Id: 0 });
            if (!(postData.ReceiveUserId > 0)) {
                ys.msgError('请选择领用人！');
                return false;
            }
            ys.ajax({
                url: '@Url.Content("~/InstrumentManage/InstrumentOutList/SaveFormJson")',
                type: 'post',
                data: postData,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        parent.$('#gridTable').ysTable('refresh');parent.resetToolbarStatus();
                        parent.layer.close(index);
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        }
    }

    function loadUser() {
        $('#receiveUserId').ysComboBox({
            url: '@Url.Content("~/InstrumentManage/InstrumentLend/GetUserListJson")',
            key: 'Id',
            value: 'RealName',
            class: 'form-control'
        });
    }


    //#region 加载配置验证信息
    function loadFormVali() {
        ys.ajax({
            url: '@Url.Content("~/SystemManage/ConfigSet/GetListByCode")',
            type: 'Post',
            data: { typecode:'1SYYQ-4HCGL-1HCCK'},
            success: function (obj) {
                if (obj.Tag == 1) {
                    var validateRule = {};
                    var validateMessage = {};
                    validateRule.outNum = { required: true, number: true, thanMinValue: 0 };
                    validateRule.receiveUserId = { required: true  };
                    validateRule.outDate = { required: true };

                    if (obj.Data != undefined && obj.Data.length > 0) {
                        for (var i = 0; i < obj.Data.length; i++) {
                            if (obj.Data[i].ConfigValue == "1") {
                                var typecode = obj.Data[i].TypeCode.replaceAll("1SYYQ-4HCGL-1HCCK-", "");
                                var verifyhint = "";
                                if (obj.Data[i].VerifyHint != undefined && obj.Data[i].VerifyHint.length > 0) {
                                    verifyhint = obj.Data[i].VerifyHint;
                                }
                                $("#lab" + typecode).append('<font class="red"> *</font>');
                                if (typecode == "Content") {
                                    validateRule.Content = { required: true };
                                    validateMessage.Content = { required: verifyhint };
                                }
                            }
                        }
                    }
                    $('#form').validate({
                        rules: validateRule,
                        messages: validateMessage
                    });
                }
            }
        });
    }

    //#endregion
</script>

