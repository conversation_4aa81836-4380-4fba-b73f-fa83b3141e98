﻿@using Dqy.Syjx.Model.Result.OrganizationManage;

@{
    Layout = "~/Views/Shared/_FormWhite.cshtml";
}

<div class="wrapper animated fadeInRight">
    <form id="form" class="form-horizontal m" >

    </form>
</div>

<script type="text/javascript">
    var userId = ys.request("userId");


    $(function () {
        getForm();
    });


    function getForm() {
        if (userId > 0) {
            var url = '@Url.Content("~/OrganizationManage/UserSubjectSet/GetUserSetCourseList")' + '?userId=' + userId;
            //console.log("url",url);
            ys.ajax({
                url: url,
                type: "get",
                success: function (obj) {
                    if (obj.Tag == 1 && obj.Total > 0) {
                        var result = obj.Data;
                        var html = '';

                        //console.log("result",result);

                        html += `<div class="form-group row"><div class="col-sm-12">`;

                        var checkList=[];
                        $(result).each(function (i, v) {
                            var dictionaryId = v.DictionaryId;
                            var dicName = v.DicName;
                            var userSubjectSetId = v.UserSubjectSetId;
                            if (userSubjectSetId == 0) {
                                html += `<label class="check-box" style="width:120px;"><div class="icheckbox-blue"><input name="chk_Stage" type="checkbox" value="${dictionaryId}" style="position: absolute; opacity: 0;"></div>${dicName}</label>`;
                            } else {
                                html += `<label class="check-box" style="width:120px;"><div class="icheckbox-blue checked"><input name="chk_Stage" type="checkbox" value="${dictionaryId}" checked="checked" style="position: absolute; opacity: 0;"></div>${dicName}</label>`;
                            }
                          
                        });
                        html += `</div></div>`;

                        $("#form").html(html);


                        $('input[name^="chk_Stage"]').change(function () {
                            if ($(this).prop("checked")) {
                                $(this).parent(".icheckbox-blue").addClass("checked");
                            } else {
                                $(this).parent(".icheckbox-blue").removeClass("checked");
                            }
                        });
                    }
                }
            });
        }
    }

    function saveForm(index) {

        var ids = $('input[name^="chk_Stage"]:checked').map(function () { return this.value }).get();

        var postData = {
            userId: userId,
            listId: ids,
        };
        //console.log("postData", postData);

        ys.ajax({
            url: '@Url.Content("~/OrganizationManage/UserSubjectSet/SaveCourseFormJson")',
            type: "post",
            data: postData,
            success: function (obj) {
                if (obj.Tag == 1) {
                    ys.msgSuccess(obj.Message);
                    parent.$('#gridTable').ysTable('refresh'); parent.resetToolbarStatus();
                    parent.layer.close(index);
                }
                else {
                    ys.msgError(obj.Message);
                }
            }
        });
    }
</script>