﻿@{
    Layout = "~/Views/Shared/_FormWhite.cshtml";
}



<div class="wrapper animated fadeInRight">
    <form id="form" class="form-horizontal m">
        <input type="hidden" id="Id" value="0" />
        <div class="form-group row">
            <label class="col-sm-2 control-label">
                填报年度<font class="red"> *</font>
            </label>
            <div class="col-sm-10">
                <div id="ReportYear" col="ReportYear"></div>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-2 control-label">
                学校可填报时间<font class="red"> *</font>
            </label>
            <div class="col-sm-4 inline">
                <input id="StartDate" col="StartDate" type="text" style="width:280px;" class="form-control" />
            </div>
            <label class="col-sm-1 control-label" style="padding-left:10px;">
                至
            </label>
            <div class="col-sm-4 inline">
                <input id="EndDate" col="EndDate" type="text" style="width:280px;" class="form-control" />
            </div>
        </div>
    </form>
</div>

<script type="text/javascript">
    var id = ys.request("id");
    $(function () {

        
        laydate.render({ elem: '#StartDate', format: 'yyyy-MM-dd', trigger: 'click', position: 'fixed' });
        laydate.render({ elem: '#EndDate', format: 'yyyy-MM-dd', trigger: 'click', position: 'fixed' });

        getForm();

        $('#form').validate({
            rules: {
                StartDate: { required: true },
                EndDate: { required: true },
            }
        });
    });

    function loadYear(year) {
        var currDate = new Date();
        var currYear = currDate.getFullYear();
        var yearData = [];

        yearData.push({ id: currYear - 1, name: currYear - 1 });
        yearData.push({ id: currYear, name: currYear });
        yearData.push({ id: currYear + 1, name: currYear + 1 });

   
        $('#ReportYear').ysComboBox({              
                key: 'id',
                value: 'name',
                data: yearData,
                width:100
         });


        if (year > 0) {
            $('#ReportYear').ysComboBox('setValue', year);
        }

    }

    function getForm() {
        ys.ajax({
            url: '@Url.Content("~/EquipmentStatisticsManage/ReportDate/GetReportDate")',
            type: 'get',
            success: function (obj) {
                var year = 0;
                if (obj.Total > 0) {
                    var obj = obj.Data[0];
                    if (obj.IsOver == 0){
         
                        var startDate = ys.formatDate(obj.StartDate, "yyyy-MM-dd");
                        var endDate = ys.formatDate(obj.EndDate, "yyyy-MM-dd");

                        $("#StartDate").val(startDate);
                        $("#EndDate").val(endDate);
                        year = obj.ReportYear;
                    }
                }
                //加载年度
                loadYear(year);
            }
        });
    }

    function saveForm(index) {

        if ($('#form').validate().form()) {
            var postData = $('#form').getWebControls({ Id: 0 });
            if (postData.ReportYear == -1){
                ys.msgError("请选择填报年度");
                return;
            }
            if (postData.StartDate == "") {
                ys.msgError("请选择开始时间");
                return;
            }
            if (postData.EndDate == "") {
                ys.msgError("请选择结束时间");
                return;
            }
            ys.ajax({
                url: '@Url.Content("~/EquipmentStatisticsManage/ReportDate/SaveFormJson")',
                type: 'post',
                data: postData,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        parent.getForm();
                        parent.layer.close(index);
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        }
    }
</script>

