﻿@{
    Layout = "~/Views/Shared/_Index.cshtml";
}
<style type="text/css">
    .check-box {
        width: 100px;
    }
</style>
<div class="container-div">
    <div class="row" style="height:auto;" id="divSchoolCourse">
        <div class="ibox float-e-margins" style="margin-bottom:10px;">
            <div class="ibox-title">
                <h5>授权学科</h5>
                <span style="color:#999;padding-left:15px;">（是指中学理化生和小学科学，由学校授权）</span>
                <!--帮助文档需要内容,id值必须为“helpBtn”-->
                <div class="fa fa-question-circle" id="helpBtn" style="margin:0;padding:0;padding-left:5px;"></div>
                <div class="ibox-tools">
                    <a class="collapse-link">
                        <i class="fa fa-chevron-up"></i>
                    </a>
                </div>
            </div>
            <div class="ibox-content" style="padding:0px;">
                <div class="row">
                    <div class="col-sm-12">
                        <div class="card-body">
                            <div class="col-sm-12  table-striped form-horizontal m" id="divCourseClass">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="row" style="height:auto;">
        <div class="ibox float-e-margins" style="margin-bottom:0px;">
            <div class="ibox-title">
                <h5>自设学科</h5>
                <span style="color:#999;padding-left:15px;">（是指除中学理化生和小学科学以外，自行设置）</span>
                <div class="ibox-tools">
                    <a class="collapse-link">
                        <i class="fa fa-chevron-up"></i>
                    </a>
                </div>
            </div>
            <div class="ibox-content" style="padding:0px;">
                <div class="row">
                    <div class="col-sm-12">
                        <div class="card-body">
                            <form id="form" class="form-horizontal m">
                                @* <div class="form-group row">
                                <label class="col-sm-2 control-label">任课学段<font class="red"> *</font></label>
                                <div class="col-sm-6">
                                <div id="schoolStageIdz" col="SchoolStageIdz"></div>
                                </div>
                                <span class="col-sm-4 control-label" style="text-align:left;color:#999;">
                                （只有九年制、完中、十二年制学校才需要选择您的学段）
                                </span>
                                </div>*@
                                <div class="form-group row">
                                    <label class="col-sm-2 control-label ">任课学科<font class="red"> *</font></label>
                                    <div class="col-sm-6">
                                        <div id="subjectIdz" col="SubjectIdz"></div>
                                    </div>
                                    <span class="col-sm-4 control-label" style="text-align:left;color:#999;">
                                        （请正确选择，否则会影响您的考核）
                                    </span>
                                </div>
                                <div class="form-group row">
                                    <label class="col-sm-2 control-label ">任课班级<font class="red"> *</font></label>
                                    <div class="col-sm-6">
                                        <div class="bootstrap-table fixed-table-container fixed-table-body">
                                            <div class="fixed-table-container">
                                                <div class="fixed-table-body">
                                                    <table class="table table-hover table-striped">
                                                        <thead>
                                                            <tr>
                                                                <th style="text-align: center; width: 100px;"><div class="th-inner ">任课学科</div></th>
                                                                <th style="text-align: center; width: 500px;"><div class="th-inner ">任课班级</div></th>
                                                                <th style="text-align: center; width: 80px;"><div class="th-inner ">选择班级</div></th>
                                                            </tr>
                                                        </thead>
                                                        <tbody id="tbCourse">
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!--帮助文档需要内容,id值必须为“tipMsg”-->
<div id="tipMsg"></div>

<script type="text/javascript">
    var Com_SchoolStageIdz="";
    $(function () {
        getForm();
        
        
    });
    var IsLoadForm = 1;
    function getForm() {
        ys.ajax({
            url: '@Url.Content("~/PersonManage/UserClassInfo/GetFormJson")',
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1) {
                    //加载教务主任设置任课信息。物理、化学、生物、科学
                    if (obj.Data.ObjList != undefined && obj.Data.ObjList.length > 0) {
                        for (var i = 0; i < obj.Data.ObjList.length; i++) {
                            const itemObj = obj.Data.ObjList[i];
                            var html = '';
                            html += '<div class="form-group row" style="padding:10px;margin-bottom: 0px;border-bottom: 1px solid #e7e6e4;">';
                            html += $.Format('<label class="col-sm-2 control-label " style="padding-top:0 !important;">{0}</label>', itemObj.CourseName);
                            if (itemObj != undefined && itemObj.ClassNameList != undefined && itemObj.ClassNameList.length > 0) {
                                html += ' <div class="col-sm-10">';
                                for (var j = 0; j < itemObj.ClassNameList.length; j++) {
                                    html += $.Format('<div class="course-classname" style="display:inline-block;width:150px;">{0}</div>', itemObj.ClassNameList[j]);
                                }
                                html += '</div>';
                            }
                            html += '</div>';
                            $("#divCourseClass").append(html);
                        }
                    } else {
                        //不存在学校授课学科直接删除
                        $("#divSchoolCourse").remove();
                    }
                    Com_SchoolStageIdz = obj.Data.SchoolStageIdz;
                    loadCourse(obj.Data.SchoolStageIdz, obj.Data.SubjectIdz);
                    IsLoadForm = 0;
                }
            }
        });
    }

    function loadCourse(pids, defaultValue) {
        $('#subjectIdz').html('');
        if (pids) {
            ys.ajax({
                url: '@Url.Content("~/SystemManage/StaticDictionary/GetChildToListJson")' + '?TypeCode=@DicTypeCodeEnum.Course.ParseToInt()&Pids=' + pids + '&OptType=9&Nature=1',
                type: 'get',
                success: function (obj) {
                    var html = '';
                    for (var i = 0; i < obj.Data.length; i++) {
                        html += '<label class="check-box">';
                        if (defaultValue && defaultValue.indexOf(obj.Data[i].DictionaryId) > -1) {
                            html += $.Format('<div class="icheckbox-blue checked"><input name="subjectIdz_checkbox" type="checkbox" value="{0}" dicname="{1}" style="position: absolute; opacity: 0;" checked="checked"></div>', obj.Data[i].DictionaryId, obj.Data[i].DicName);
                        } else {
                            html += $.Format('<div class="icheckbox-blue"><input name="subjectIdz_checkbox" type="checkbox" value="{0}" dicname="{1}" style="position: absolute; opacity: 0;"></div>', obj.Data[i].DictionaryId, obj.Data[i].DicName);
                        }
                        html += $.Format('{0}</label>', obj.Data[i].DicName);
                    }
                    $('#subjectIdz').html(html);

                    $('input[name="subjectIdz_checkbox"]').change(function () {
                        var itemObj = $(this);
                        var courseid = $(this).attr("value");
                        var coursename = $(this).attr("dicname");
                        if ($(this).prop("checked")) {
                            $(this).parent(".icheckbox-blue").addClass("checked");
                            setSaveCourse(itemObj, courseid, coursename, 1);
                        } else {
                            var confirmtitle = $.Format('您确认要删除"{0}"任课学科及班级信息吗？', coursename);
                            $(this).parent(".icheckbox-blue").removeClass("checked");
                            ys.confirm(confirmtitle, function () {

                                setSaveCourse(itemObj, courseid, coursename, 2);
                            }, function () {
                                itemObj.prop("checked", true);
                                itemObj.parent(".icheckbox-blue").addClass("checked");
                            });
                        }
                        //var courseIds = $('input[name="subjectIdz_checkbox"]:checked').map(function () { return this.value }).get().join(',');
                        //loadTeachClassList(defaultValue);
                    });
                    loadTeachClassList(defaultValue);
                }
            });
        }
    }

    function showChooseClass(courseId) {
        ys.openDialog({
            title: "选择任课班级",
            content: '@Url.Content("~/PersonManage/UserClassInfo/ChooseClassForm")' + '?stage=' + Com_SchoolStageIdz + '&classIdz=' + $('#hClassIdz_' + courseId).val() + '&courseid=' + courseId,
            callback: function (index, layero) {
                var iframeWin = window[layero.find('iframe')[0]['name']];
                iframeWin.saveForm(index);
            }
        });
    }

    function setSelectClass(courseid, selectedRow) {
        var className = '';
        var classIdz = '';
        $.each(selectedRow, function (i, v) {
            className += v.GradeName + v.ClassDesc + '班、';
            classIdz += v.Id + ',';
        });
        className = className.length > 0 ? className.substr(0, className.length - 1) : '';
        classIdz = classIdz.length > 0 ? classIdz.substr(0, classIdz.length - 1) : '';
        $('#tdClassName_' + courseid).html(className);
        $('#hClassIdz_' + courseid).val(classIdz);
    }

    function loadTeachClassList(courseIds) {
        $('#tbCourse').html('');
        ys.ajax({
            url: '@Url.Content("~/PersonManage/UserClassInfo/GetUserTeachClassListJson")' + '?courseIdz=' + courseIds,
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1) {
                    var html = '';
                    $.each(obj.Data, function (i, v) {
                        if (parseInt(v.Nature) != 1) {
                            html += $.Format('<tr id="trCourseClassz_{0}">', v.CourseId);
                            html += $.Format('<td style="text-align:center;">{0}', v.CourseName);
                            html += $.Format('  <input type="hidden" id="hClassIdz_{0}" value="{1}" />', v.CourseId, v.ClassIdz || '');
                            html += $.Format('  <input type="hidden" id="hCourseId_{0}" value="{1}" />', i, v.CourseId);
                            html += $.Format('</td>');
                            html += $.Format('<td style="text-align:left;" id="tdClassName_{1}">{0}</td>', v.ClassNamez, v.CourseId);
                            html += $.Format('<td style="text-align: center;"><a class="btn btn-success btn-xs" onclick="showChooseClass({0})"><i class="fa fa-search"></i> 选择</a></td>', v.CourseId);
                            html += '</tr>';
                        }
                    });
                    $('#tbCourse').html(html);
                }
            }
        });
    }
    function setSaveCourse(objCourse, courseid, coursename, savetype) {
        var postdata = { CourseId: courseid, SaveType: savetype, GradeClassId: 0 };
        $.ajax({
            url: '@Url.Content("~/PersonManage/UserClassInfo/SaveFormJson")',
            type: 'post',
            data: postdata,
            success: function (obj) {
                if (obj.Tag == 1) {
                    if (savetype == 1) {
                        AddCourseClassHtml(courseid, coursename, '');
                    } else {
                        $("#trCourseClassz_" + courseid).remove();
                    }

                    layer.msg('保存成功！', { icon: 1, time: 3000, area: ['400px'], shade: false, offset: [15,] });
                } else {
                    layer.msg(obj.Message, { icon: 2, time: 3000, area: ['400px'], shade: false, offset: [15,] });
                }
            }
        });
    }

    function AddCourseClassHtml(courseid, coursename, classnamez) {
        var html = '';
        html += $.Format('<tr id="trCourseClassz_{0}">', courseid);
        html += $.Format('<td style="text-align:center;">{0}', coursename);
        html += $.Format('  <input type="hidden" id="hClassIdz_{0}" value="{1}" />', courseid, classnamez);
        html += $.Format('  <input type="hidden" id="hCourseId_{0}" value="{1}" />', courseid);
        html += $.Format('</td>');
        html += $.Format('<td style="text-align:left;" id="tdClassName_{1}">{0}</td>', classnamez, courseid);
        html += $.Format('<td style="text-align: center;"><a class="btn btn-success btn-xs" onclick="showChooseClass({0})"><i class="fa fa-search"></i> 选择</a></td>', courseid);
        html += '</tr>';
        $('#tbCourse').append(html);
    }
    function removeConfirm() {
        $(".layui-layer-btn0").hide();
    }
</script>
