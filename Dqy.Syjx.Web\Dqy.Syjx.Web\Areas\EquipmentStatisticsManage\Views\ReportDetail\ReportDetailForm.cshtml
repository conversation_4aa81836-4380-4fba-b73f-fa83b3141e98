﻿@{
    Layout = "~/Views/Shared/_FormWhite.cshtml";
}

<div class="wrapper animated fadeInRight">
    <form id="form" class="form-horizontal m">
        <div class="form-group row">
            <label class="col-sm-2 control-label ">ReportId<font class="red"> *</font></label>
            <div class="col-sm-4">
                <input id="reportId" col="ReportId" type="text" class="form-control" />
            </div>
            <label class="col-sm-2 control-label ">SportFootballNum<font class="red"> *</font></label>
            <div class="col-sm-4">
                <input id="sportFootballNum" col="SportFootballNum" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-2 control-label ">SportPingPongNum<font class="red"> *</font></label>
            <div class="col-sm-4">
                <input id="sportPingPongNum" col="SportPingPongNum" type="text" class="form-control" />
            </div>
            <label class="col-sm-2 control-label ">SportRoomNum<font class="red"> *</font></label>
            <div class="col-sm-4">
                <input id="sportRoomNum" col="SportRoomNum" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-2 control-label ">BookdRoomSeatNum<font class="red"> *</font></label>
            <div class="col-sm-4">
                <input id="bookdRoomSeatNum" col="BookdRoomSeatNum" type="text" class="form-control" />
            </div>
            <label class="col-sm-2 control-label ">BookPavilionNum<font class="red"> *</font></label>
            <div class="col-sm-4">
                <input id="bookPavilionNum" col="BookPavilionNum" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-2 control-label ">BookNewYearNum<font class="red"> *</font></label>
            <div class="col-sm-4">
                <input id="bookNewYearNum" col="BookNewYearNum" type="text" class="form-control" />
            </div>
            <label class="col-sm-2 control-label ">NetworkFulltimeNum<font class="red"> *</font></label>
            <div class="col-sm-4">
                <input id="networkFulltimeNum" col="NetworkFulltimeNum" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-2 control-label ">ComputerPortableNum<font class="red"> *</font></label>
            <div class="col-sm-4">
                <input id="computerPortableNum" col="ComputerPortableNum" type="text" class="form-control" />
            </div>
            <label class="col-sm-2 control-label ">ComputerTeacherNum<font class="red"> *</font></label>
            <div class="col-sm-4">
                <input id="computerTeacherNum" col="ComputerTeacherNum" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-2 control-label ">MediaOtherNum<font class="red"> *</font></label>
            <div class="col-sm-4">
                <input id="mediaOtherNum" col="MediaOtherNum" type="text" class="form-control" />
            </div>
            <label class="col-sm-2 control-label ">MedialaboratoryNum<font class="red"> *</font></label>
            <div class="col-sm-4">
                <input id="medialaboratoryNum" col="MedialaboratoryNum" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-2 control-label ">MediaDeviceNum<font class="red"> *</font></label>
            <div class="col-sm-4">
                <input id="mediaDeviceNum" col="MediaDeviceNum" type="text" class="form-control" />
            </div>
            <label class="col-sm-2 control-label ">StudentNum<font class="red"> *</font></label>
            <div class="col-sm-4">
                <input id="studentNum" col="StudentNum" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-2 control-label ">CurrentYearAmount<font class="red"> *</font></label>
            <div class="col-sm-4">
                <input id="currentYearAmount" col="CurrentYearAmount" type="text" class="form-control" />
            </div>
            <label class="col-sm-2 control-label ">TotalAmount<font class="red"> *</font></label>
            <div class="col-sm-4">
                <input id="totalAmount" col="TotalAmount" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-2 control-label ">SportVolleyballNum<font class="red"> *</font></label>
            <div class="col-sm-4">
                <input id="sportVolleyballNum" col="SportVolleyballNum" type="text" class="form-control" />
            </div>
            <label class="col-sm-2 control-label ">SportBasketballNum<font class="red"> *</font></label>
            <div class="col-sm-4">
                <input id="sportBasketballNum" col="SportBasketballNum" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-2 control-label ">SportRunwayLength<font class="red"> *</font></label>
            <div class="col-sm-4">
                <input id="sportRunwayLength" col="SportRunwayLength" type="text" class="form-control" />
            </div>
            <label class="col-sm-2 control-label ">BookLibraryArea<font class="red"> *</font></label>
            <div class="col-sm-4">
                <input id="bookLibraryArea" col="BookLibraryArea" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-2 control-label ">BookCabinetNum<font class="red"> *</font></label>
            <div class="col-sm-4">
                <input id="bookCabinetNum" col="BookCabinetNum" type="text" class="form-control" />
            </div>
            <label class="col-sm-2 control-label ">BookTotalNum<font class="red"> *</font></label>
            <div class="col-sm-4">
                <input id="bookTotalNum" col="BookTotalNum" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-2 control-label ">NetworkAdminNum<font class="red"> *</font></label>
            <div class="col-sm-4">
                <input id="networkAdminNum" col="NetworkAdminNum" type="text" class="form-control" />
            </div>
            <label class="col-sm-2 control-label ">ComputerPadNum<font class="red"> *</font></label>
            <div class="col-sm-4">
                <input id="computerPadNum" col="ComputerPadNum" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-2 control-label ">ComputerStudentNum<font class="red"> *</font></label>
            <div class="col-sm-4">
                <input id="computerStudentNum" col="ComputerStudentNum" type="text" class="form-control" />
            </div>
            <label class="col-sm-2 control-label ">MediaInteractiveNum<font class="red"> *</font></label>
            <div class="col-sm-4">
                <input id="mediaInteractiveNum" col="MediaInteractiveNum" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-2 control-label ">MediaFunroomNum<font class="red"> *</font></label>
            <div class="col-sm-4">
                <input id="mediaFunroomNum" col="MediaFunroomNum" type="text" class="form-control" />
            </div>
            <label class="col-sm-2 control-label ">TeacherNum<font class="red"> *</font></label>
            <div class="col-sm-4">
                <input id="teacherNum" col="TeacherNum" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-2 control-label ">ClassNum<font class="red"> *</font></label>
            <div class="col-sm-4">
                <input id="classNum" col="ClassNum" type="text" class="form-control" />
            </div>
    </form>
</div>

<script type="text/javascript">
    var id = ys.request("id");
    $(function () {
        getForm();

        $('#form').validate({
            rules: {
                reportId: { required: true }
            }
        });
    });

    function getForm() {
        if (id > 0) {
            ys.ajax({
                url: '@Url.Content("~/EquipmentStatisticsManage/ReportDetail/GetFormJson")' + '?id=' + id,
                type: 'get',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        $('#form').setWebControls(obj.Data);
                    }
                }
            });
        }
        else {
            var defaultData = {};
            $('#form').setWebControls(defaultData);
        }
    }

    function saveForm(index) {
        if ($('#form').validate().form()) {
            var postData = $('#form').getWebControls({ Id: id });
            ys.ajax({
                url: '@Url.Content("~/EquipmentStatisticsManage/ReportDetail/SaveFormJson")',
                type: 'post',
                data: postData,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        parent.searchGrid();
                        parent.layer.close(index);
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        }
    }
</script>

