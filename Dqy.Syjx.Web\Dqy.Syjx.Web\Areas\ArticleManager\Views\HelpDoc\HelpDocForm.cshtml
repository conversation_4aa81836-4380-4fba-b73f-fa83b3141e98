﻿@{
    Layout = "~/Views/Shared/_FormWhite.cshtml";
}
@inject Microsoft.AspNetCore.Hosting.IWebHostEnvironment HostingEnvironment
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/uploadifive/File.min.css"))
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/uploadifive/File.min.js"))
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/ckeditor/ckeditor.js"),true)

<div class="wrapper animated fadeInRight">
    <form id="form" class="form-horizontal m">
        <!--前台验证图片格式-->
        <input type="hidden" id="hidImageType" />

        <div class="form-group row">
            <label class="col-sm-2 control-label ">内容</label>
            <div class="col-sm-10">
                <textarea id="ItemContent" col="ItemContent" class="form-control" autocomplete="off"></textarea>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-2 control-label ">类型</label>
            <div class="col-sm-10">
                <div class="col-sm-4" id="FileType" col="FileType"></div>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-2 control-label ">路径<font class="red"> *</font></label>
            <div class="col-sm-6">
                <input id="ItemPath" col="ItemPath" type="text" class="form-control" readonly="readonly" />
            </div>
            <div class="col-sm-4">
                <a class="btn btn-primary btn-xs" onclick="showUploadFileForm()"><i class="fa fa-plus"></i> 上传</a>
                <div style="display:none;">
                    <input type="file" name="uploadify" id="uploadify" />
                    <div style="height: 55px;display:none;" id="fileQueue"></div>
                </div>
             </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-2 control-label ">排序值</label>
            <div class="col-sm-10">
                <input id="ShowOrder" col="ShowOrder" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-2 control-label ">状态</label>
            <div class="col-sm-10">
                <div class="col-sm-4" id="Status" col="Status"></div>
            </div>
        </div>
    </form>
</div>

<script type="text/javascript">
    var id = ys.request("id");
    var sysMenuId = ys.request("sysMenuId");
    var editorItem;
    
    $(function () {

        createItemEditor();
        //console.log("id:" + id);
        //console.log("sysMenuId:" + sysMenuId);

        $("#FileType").ysRadioBox({ data: ys.getJson(@Html.Raw(typeof(FileTypeEnum).EnumToDictionaryString())) });
        $("#Status").ysRadioBox({ data: ys.getJson(@Html.Raw(typeof(StatusEnum).EnumToDictionaryString())) });

        loadUploadify();

        $('#form').validate({
            rules: {
                ItemContent: { ItemContent: true },
            }
        });

        var defaultData = {};
        defaultData.Status = "@StatusEnum.Yes.ParseToInt()";
        defaultData.FileType = "@FileTypeEnum.Img.ParseToInt()";
        $("#form").setWebControls(defaultData);
    });

    function createItemEditor() {
        if (editorItem) {
            editorItem.destroy();
        }
        editorItem = CKEDITOR.replace('ItemContent');
    }

    function showUploadFileForm() {
        var obj = undefined;
        $("#uploadifive-uploadify input").map(function (index, item) {
            obj = $(this);
            var styleStr = obj.attr("style");
            if (styleStr.indexOf('display') > -1) {
                obj = undefined;
            }
        });
        if (obj != undefined) {
            obj.click();
        }
    }

    function loadUploadify() {
        $("#uploadify").uploadifive({
            'uploadScript': '/File/UploadFile?fc=4100',
            'cancelImg': '~/lib/uploadifive/uploadifive-cancel.png',
            'buttonText': '上 传',
            //'formData': { "token": "", "UserId": "0", "folder": "/UploadFile/Attachment" },
            'queueID': 'fileQueue',
            'auto': true,
            'multi': false,
            'fileDesc': '支持格式：*.jpg;*.gif;*.png;*.pdf;',
            'fileExt': '*.jpg;*.gif;*.png;*.pdf;',
            'onAddQueueItem': function (file) {
                //读取值
                var fileType = $("#FileType").ysRadioBox('getValue');
                var typeValue = ".JPG.PNG.BMP.JPEG.GIF";
                if (fileType == 2){
                    typeValue = ".MP4";
                }
                typeValue = typeValue.toLowerCase();
                if (checkIsAllowUpload(file.name, typeValue))
                    return true;
                else {
                    $('#uploadify').uploadifive('cancel', $('.uploadifive-queue-item').first().data('file'));
                    //$('#spanLink').html('<font style="color:red;">上传文件只能是(' + fileType + ')格式。</font>');
                    ys.msgError("上传文件只能是" + typeValue + "格式。");
                    return false;
                }
            },
            'onUploadComplete': function (fileObj, response) {
                if (typeof (response) == "string" && response != "") {
                    response = JSON.parse(response);
                    if (response.Tag == 1) {
                        ys.msgSuccess("上传成功！");
                        var id = response.Description;
                        var filePath = response.Data;
                        $("#ItemPath").val(filePath);
                    } else {
                        ys.msgError(response.Message);
                    }
                }
            },
            'onFallback': function (event) {
                ys.msgError(event);
            }
        });
    }

    function checkIsAllowUpload(fileName, allowExt) {
        var index = fileName.lastIndexOf(".");
        var suffix = fileName.substring(index) + '.';
        allowExt = allowExt.toLowerCase() + '.';
        suffix = suffix.toLowerCase();
        if (allowExt.indexOf(suffix) == -1)
            return false;
        else
            return true;
    }

    function saveForm(index) {
        if ($('#form').validate().form()) {
            var postData = $('#form').getWebControls();
            if (postData.ShowOrder==""){
                postData.ShowOrder = 0;
            }
            //if (postData.ItemContent == ""){
            //    ys.msgError("请填写内容");
            //    return;
            //}
            var itemContent = editorItem.getData();
            //if (itemContent == "") {
            //    ys.msgError("请填写内容");
            //    return;
            //}
            if(itemContent.length > 500){
                ys.msgError("内容不能超出500字符");
                return;
            }
            if (postData.ItemPath == ""){
                ys.msgError("请上传图片或视频");
                return;
            }
            //console.log("postData:" + JSON.stringify(postData));
            postData.SysMenuId = sysMenuId;
            postData.HelpDocId = id;
            postData.ItemContent = itemContent;

            ys.ajax({
                url: '@Url.Content("~/ArticleManager/HelpItem/SaveFormJson")',
                type: 'post',
                data: postData,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        parent.searchGrid();
                        parent.layer.close(index);
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        }
    }
</script>

