﻿
@{
    Layout = "~/Views/Shared/_Index.cshtml";
}
@inject Microsoft.AspNetCore.Hosting.IWebHostEnvironment HostingEnvironment
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/uploadifive/File.min.css"))
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/uploadifive/File.min.js"))
<div class="wrapper animated fadeInRight">
    <div class="btn-group-sm hidden-xs" id="toolbar">
    </div>
    <div class="col-sm-12 select-table table-striped">
        <div class="card-body">
            <form id="form" class="form-horizontal m">
                <div class="form-group row" id="divSchoolName" style="display:none;">
                    <label class="col-sm-3 control-label ">单位名称</label>
                    <div class="col-sm-9">
                        <input id="SchoolName" col="SchoolName" type="text" class="form-control" readonly />
                    </div>
                </div>

                <div class="form-group row">
                    <label class="col-sm-3 control-label ">一级分类</label>
                    <div class="col-sm-3">
                        <input id="ClassNameA" col="ClassNameA" type="text" class="form-control" readonly />
                    </div>
                    <label class="col-sm-2 control-label ">二级分类</label>
                    <div class="col-sm-4">
                        <input id="ClassNameB" col="ClassNameB" type="text" class="form-control" readonly />
                    </div>
                </div>

                <div class="form-group row">
                    <label class="col-sm-3 control-label ">实验（专用）室名称</label>
                    <div class="col-sm-3">
                        <input id="Name" col="Name" type="text" class="form-control" readonly />
                    </div>
                    <label class="col-sm-2 control-label ">地点</label>
                    <div class="col-sm-4">
                        <input id="AddressName" col="AddressName" type="text" class="form-control" readonly />
                    </div>
                </div>

                <div class="form-group row">
                    <label class="col-sm-3 control-label ">适用学科</label>
                    <div class="col-sm-3">
                        <input id="SubjectName" col="SubjectName" type="text" class="form-control" readonly />
                    </div>
                    <label class="col-sm-2 control-label ">属性</label>
                    <div class="col-sm-4">
                        <input id="NatureName" col="NatureName" type="text" class="form-control" readonly />
                    </div>
                </div>

                <div class="form-group row">
                    <label class="col-sm-3 control-label ">管理部门</label>
                    <div class="col-sm-3">
                        <input id="DepartmentName" col="DepartmentName" type="text" class="form-control" readonly />
                    </div>
                    <label class="col-sm-2 control-label ">管理人</label>
                    <div class="col-sm-4">
                        <input id="RealName" col="RealName" type="text" class="form-control" readonly />
                    </div>
                </div>



                <div class="form-group row">
                    <label class="col-sm-3 control-label ">功能简介</label>
                    <div class="col-sm-9">

                        <div id="spanFile_1021"></div>
                    </div>
                </div>

                <div class="form-group row">
                    <label class="col-sm-3 control-label ">管理制度</label>
                    <div class="col-sm-9">

                        <div id="spanFile_1022"></div>
                    </div>
                </div>
            </form>
        </div>

        </div>
    
</div>
<script type="text/javascript">
    var id = ys.request("id");
    var schoolName = decodeURI(ys.request("schoolName"));
    $(function () {

        getForm();

    });

    function getForm() {
        if (id > 0) {
            ys.ajax({
                url: '@Url.Content("~/QueryStatisticsManage/FunRoom/GetFunRoomSingleJson")' + '?id=' + id,
                type: 'get',
                success: function (obj) {
                    if (obj.Tag == 1) {

                        $('#form').setWebControls(obj.Data);
                        if (isNaN(obj.Data.AttachmentList) && obj.Data.AttachmentList != undefined) {
                            var fileList = obj.Data.AttachmentList;
                            if (fileList.length > 0) {
                                for (var i = 0; i < fileList.length; i++) {
                                    var id = fileList[i].Id;
                                    var filePath = fileList[i].Path;
                                    var fileExt = fileList[i].Ext.toUpperCase();
                                    var fileTitle = fileList[i].Title;
                                    if (fileExt == ".JPG" || fileExt == ".BMP" || fileExt == ".JPEG" || fileExt == ".PNG" || fileExt == ".GIF") {
                                        $("#spanFile_" + fileList[i].FileCategory).append('<span class="keywords" idValue="' + id + '"><a  modal="zoomImg"  href="javascript:void(0);" src="' + filePath + '" >' + fileTitle + '</a></span>');
                                    } else {
                                        $("#spanFile_" + fileList[i].FileCategory).append('<span class="keywords" idValue="' + id + '"><a target="_blank" href="' + filePath + '" >' + fileTitle + '</a></span>');
                                    }
                                }
                                imgages.showextalink();
                            }
                        }

                        if (schoolName != null && schoolName != "") {
                            $("#divSchoolName").show();
                            $("#SchoolName").val(schoolName);
                        } else {
                            $("#divSchoolName").hide();
                        }
                    }
                }
            });
        }
    }

</script>
