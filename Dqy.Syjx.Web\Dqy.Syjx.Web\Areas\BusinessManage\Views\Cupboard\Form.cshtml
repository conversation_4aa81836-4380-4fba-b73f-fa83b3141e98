﻿@{
    Layout = "~/Views/Shared/_FormWhite.cshtml";
}

<div class="wrapper animated fadeInRight">
    <form id="form" class="form-horizontal m">
        <div class="form-group row">
            <label class="col-sm-3 control-label " id="labFunRoomId">实验（专用）室名称<font class="red"> *</font></label>
            <div class="col-sm-8">
                <div id="FunRoomId" col="FunRoomId"></div>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label " id="labName">橱柜名称</label>
            <div class="col-sm-8">
                <input id="Name" col="Name" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label " id="labModel">规格参数</label>
            <div class="col-sm-8">
                <input id="Model" col="Model" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">排序</label>
            <div class="col-sm-8">
                <input id="Sort" col="Sort" type="text" class="form-control" />
            </div>
        </div>
    </form>
</div>

<script type="text/javascript">
    var id = ys.request("id");
    $(function () {
        loadFunRoom();
        loadFormVali();
    });
    function loadFunRoom() {
        ys.ajax({
            url: '@Url.Content("~/BusinessManage/FunRoom/GetListByUserId")',
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1) {
                    $('#FunRoomId').ysComboBox({ data: obj.Data, class: 'form-control', key: 'Id', value: 'RoomNatureName' });
                    if (obj.Data.length == 1) {
                        $("#FunRoomId").ysComboBox('setValue', obj.Data[0].Id);
                    }
                    getForm();
                } else {
                    $('#FunRoomId').ysComboBox({ data: [], class: 'form-control', key: 'Id', value: 'RoomNatureName' });
                    ys.msgError(obj.Message);
                }
            }
        });
    }
    function getForm() {
        ys.ajax({
            url: '@Url.Content("~/BusinessManage/Cupboard/GetFormJson")' + '?id=' + id,
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1) {
                    $('#form').setWebControls(obj.Data);
                }
            }
        });
    }

    function saveForm(index) {
        if ($('#form').validate().form()) {
            var postData = $('#form').getWebControls({ Id: id });
            var errorMsg = '';
            if (!(parseInt(postData.FunRoomId) > 0)) {
                errorMsg += '请选择实验（专用）室名称。<br/>';
            }
            if (!(postData.Name != undefined && postData.Name.length > 0)) {
                errorMsg += '请填写橱柜名称。<br/>';
            }
            if (postData.Sort != undefined && postData.Sort != "" && !(parseInt(postData.Sort) >= 0)) {
                errorMsg += '请填写橱柜排序值（排序值必须不小于0）。<br/>';
            }
            if (parseInt(postData.Sort) > 100000) {
                errorMsg += '填写的橱柜排序值，必须小于100000。<br/>';
            }
            if (errorMsg != '') {
                ys.msgError('验证失败，请填写完成再提交！<br/>' + errorMsg);
                return;
            }
            ys.ajax({
                url: '@Url.Content("~/BusinessManage/Cupboard/SaveFormJson")',
                type: 'post',
                data: postData,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        parent.$('#gridTable').ysTable('refresh');parent.resetToolbarStatus();
                        parent.layer.close(index);
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        }
    }


    //#region
    //加载配置验证信息
    function loadFormVali() {
        ys.ajax({
            url: '@Url.Content("~/SystemManage/ConfigSet/GetListByCode")',
            type: 'Post',
            data: { typecode:'2SYSG-1CJWH-2CGGL'},
            success: function (obj) {
                if (obj.Tag == 1) {
                    var validateRule = {};
                    var validateMessage = {};
                    validateRule.FunRoomId = { required: true, min: 1 };
                    validateMessage.FunRoomId = { required: "请选择存放地名称。", min: '请选择存放地名称。' };
                    if (obj.Data != undefined && obj.Data.length > 0) {
                        for (var i = 0; i < obj.Data.length; i++) {
                            if (obj.Data[i].ConfigValue == "1") {
                                var typecode = obj.Data[i].TypeCode.replaceAll("2SYSG-1CJWH-2CGGL-", "");
                                var verifyhint = "";
                                if (obj.Data[i].VerifyHint != undefined && obj.Data[i].VerifyHint.length > 0) {
                                    verifyhint = obj.Data[i].VerifyHint;
                                }
                                $("#lab" + typecode).append('<font class="red"> *</font>');
                                if (typecode == "Name") {
                                    validateRule.Name = { required: true };
                                    validateMessage.Name = { required: verifyhint };
                                    SaveValiDate.push({ typecode: typecode, verifyhint: verifyhint });
                                } else if (typecode == "Model") {
                                    validateRule.Model = { required: true };
                                    validateMessage.Model = { required: verifyhint };
                                }
                            }
                        }
                    }
                    $('#form').validate({
                        rules: validateRule,
                        messages: validateMessage
                    });
                }
            }
        });
    }

    //#endregion
</script>

