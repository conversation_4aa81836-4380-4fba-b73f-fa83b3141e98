﻿
@{
    Layout = "~/Views/Shared/_FormWhite.cshtml";
    var IsAllowEditModel = ViewBag.IsAllowEditModel;
}

@inject Microsoft.AspNetCore.Hosting.IWebHostEnvironment HostingEnvironment
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/uploadifive/File.min.css"))
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/uploadifive/File.min.js"))

<style type="text/css">

    .iradio-box {
        width: 100px;
    }

    .iradio-box {
        position: initial;
        display: inline-block;
    }

    .iradio-blue {
        position: inherit;
        display: inline-block;
        top: 6px;
    }
</style>
<div class="wrapper animated fadeInRight">
    <div class="btn-group-sm hidden-xs" id="toolbar">
    </div>
    <div class="col-sm-12 select-table table-striped">

        <div class="ibox-title">
            <h5>
                仪器录入
                <span class="glyphicon glyphicon-question-sign titleprompt inputprompt" aria-hidden="true" data-container="body" data-toggle="tooltip" data-placement="right"
                      data-content="如录入有误，请到【仪器审核】中进行修改或删除。"></span>
            </h5>
        </div>
        <div class="card-body">
            <form id="form" class="form-horizontal m">
                <div class="divform divSingle">
                    <div class="form-group row">
                        <label class="col-sm-2 control-label ">
                            <span class="glyphicon glyphicon-question-sign titleprompt inputprompt" aria-hidden="true" data-container="body" data-toggle="tooltip" data-placement="right"
                                  data-content="@(IsAllowEditModel == 1 ? "名称可修改" : "")"></span>
                            仪器名称<font class="red"> *</font>
                        </label>
                        <div class="col-sm-8">
                            <input id="name" col="Name" readonly="@(IsAllowEditModel == 1 ? false : true)" type="text" class="form-control" onclick="chooseStandard(0);" />
                            <input id="instrumentStandardId" col="InstrumentStandardId" type="hidden" />
                        </div>
                        <div class="col-sm-2" style="padding-left:0px;">
                            <a class="btn btn-sm btn-success" onclick="chooseStandard(1)"><i class="fa fa-search"></i> 选择</a>
                        </div>
                    </div>
                    <div class="form-group row" style="display:none;">
                        <label class="col-sm-2 control-label ">名称代码<font class="red"> *</font></label>
                        <div class="col-sm-8">
                            <input type="text" class="form-control" id="code1" col="Code1" readonly />
                        </div>
                    </div>
                    <div class="form-group row" id="divCode" style="display:none;">
                        <label class="col-sm-2 control-label ">分类代码<font class="red"> *</font></label>
                        <div class="col-sm-8">
                            <input type="text" class="form-control" id="code" col="Code" readonly />
                        </div>
                    </div>

                    <div class="form-group row">
                        <label class="col-sm-2 control-label ">单位<font class="red"> *</font></label>
                        <div class="col-sm-8">
                            <input id="unitName" col="UnitName" type="text" class="form-control"/>
                        </div>
                    </div>
                    <div class="form-group row">
                        <label class="col-sm-2 control-label ">选择规格属性<font class="red"> *</font></label>
                        <div class="col-sm-8">
                            <div id="modelStandardId"></div>
                        </div>
                    </div>

                    <div class="form-group row" id="divModel" style="display:none;">
                        <label class="col-sm-2 control-label ">规格属性<font class="red"> *</font></label>
                        <div class="col-sm-8">
                            <input id="model" col="Model" type="text" class="form-control" />
                        </div>
                    </div>
                    <div class="form-group row">
                        <label class="col-sm-2 control-label ">数量<font class="red"> *</font></label>
                        <div class="col-sm-8">
                            <input id="num" col="Num" type="text" class="form-control" />
                        </div>
                    </div>
                    <div class="form-group row">
                        <label class="col-sm-2 control-label " id="labPrice">单价(元)</label>
                        <div class="col-sm-8">
                            <input id="price" col="Price" type="text" class="form-control" />
                        </div>
                    </div>
                    <div class="form-group row">
                        <label class="col-sm-2 control-label ">
                            <span class="glyphicon glyphicon-question-sign titleprompt inputprompt" aria-hidden="true" data-container="body" data-toggle="tooltip" data-placement="right"
                                  data-content="请确认所选仪器与适用学科匹配"></span>
                            适用学科<font class="red"> *</font>
                        </label>
                        <div class="col-sm-8">
                            <div id="courseId"></div>
                        </div>
                    </div>
                    <div class="form-group row">
                        <label class="col-sm-2 control-label ">适用学段<font class="red"> *</font></label>
                        <div class="col-sm-8">
                            <div id="stageId"></div>
                        </div>
                    </div>
                    <div class="form-group row" style="display:none;">
                        <label class="col-sm-2 control-label ">采购日期<font class="red"> *</font></label>
                        <div class="col-sm-8">
                            <input id="purchaseDate" col="PurchaseDate" type="text" class="form-control" readonly />
                        </div>
                    </div>
                    <div class="form-group row">
                        <label class="col-sm-2 control-label ">保修期(月)<font class="red"> *</font></label>
                        <div class="col-sm-8">
                            <input id="warrantyMonth" col="WarrantyMonth" type="text" class="form-control" />
                        </div>
                    </div>
                    <div class="form-group row" style="display:none;">
                        <label class="col-sm-2 control-label " id="labSupplierName">供应商</label>
                        <div class="col-sm-8">
                            <input id="SupplierName" col="SupplierName" type="text" class="form-control" />
                        </div>
                    </div>
                    <div class="form-group row">
                        <label class="col-sm-2 control-label " id="labBrand">品牌</label>
                        <div class="col-sm-8">
                            <input id="Brand" col="Brand" type="text" class="form-control" />
                        </div>
                    </div>
                    <div class="form-group row">
                        <label class="col-sm-2 control-label " id="labBrand">是否自制教具<font class="red"> *</font></label>
                        <div class="col-sm-8" id="isSelfMade" col="IsSelfMade" style="bottom:6px;left:-20px;">
                        </div>
                    </div>
                    <div class="form-group row">
                        <label class="col-sm-2 control-label " id="labImage">
                            <span class="glyphicon glyphicon-question-sign titleprompt inputprompt" aria-hidden="true" data-container="body" data-toggle="tooltip" data-placement="right"
                                  data-content="请使用仪器正面图片，小于5M，支持图片格式"></span>
                            仪器图片
                        </label>
                        <div class="col-sm-8">
                            <input type="file" name="uploadify" id="uploadify" />
                            <div style="height: 55px;display:none;" id="fileQueue"></div>
                            <input id="AttachmentId" col="AttachmentId" type="hidden" value="0" />
                            <input id="Path" col="Path" type="hidden" />
                            <a modal="zoomImg" href="javascript:void(0);" id="awardSrc">
                                <img modal="zoomImg" href="javascript:void(0);" src="" id="imgAward" class="col-sm-8" style="max-width:200px;padding: 10px 0px;" />
                            </a>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
<script type="text/javascript">
    var url = '@Url.Content("~/InstrumentManage/SchoolInstrument/SchoolInstrumentIndex")';
    var title = '仪器审核';

    var id = ys.request("id");

    var SaveValiDate = [];//存储需要验证的字段
    var IsShowFloor = 0;

    var IsEdit = false;
    var IsEditCourse = false;

    var modelStandardData = [];

    $(function () {


        $(".inputprompt").popover({
            trigger: 'hover',
            html: true
        });

        laydate.render({ elem: '#purchaseDate', format: 'yyyy-MM-dd', trigger: 'click', position: 'fixed' });
        $("#isSelfMade").ysRadioBox({ data: ys.getJson(@Html.Raw(typeof(IsEnum).EnumToDictionaryString())), class: "form-control" });
        $("#isSelfMade").ysRadioBox('setValue', 0);

        getForm();

        loadUploadify();

        loadFormVali();

    });

    function chooseStandard(type) {
        if (!$('#instrumentStandardId').val() || $('#name').val() == '' || type == 1) {
            ys.openDialog({
                title: '选择仪器',
                content: '@Url.Content("~/InstrumentManage/PurchaseDeclaration/StandardChoose")',
                width: '980px',
                //height: '600px',
                callback: function (index, layero) {
                    var iframeWin = window[layero.find('iframe')[0]['name']];
                    iframeWin.saveForm(index);
                    $("#divCode").show();
                }
            });
        }
    }

    function getForm() {
        if (id > 0) {
            ys.ajax({
                url: '@Url.Content("~/InstrumentManage/SchoolInstrument/GetFormJson")' + '?id=' + id,
                type: 'get',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        $("#divCode").show();
                        obj.Data.PurchaseDate = obj.Data.PurchaseDate.substring(0, 10).replace(/\//g, '-');
                        IsEdit = true;
                        IsEditCourse = true;
                        getInstrumentModel(obj.Data.InstrumentStandardId, obj.Data.ModelStandardId, obj.Data.Model);
                        $('#divModel').show();
                        getCourse(obj.Data.CourseId);
                        getSchoolStage(obj.Data.CourseId, obj.Data.StageId);
                       
                        $('#form').setWebControls(obj.Data);

                        if (obj.Data.AttachmentList.length > 0) {
                            $('#AttachmentId').val(obj.Data.AttachmentList[0].Id);
                            $('#Path').val(obj.Data.AttachmentList[0].Path);
                            $('#imgAward').attr("src", obj.Data.AttachmentList[0].Path);
                            $("#awardSrc").attr("src", obj.Data.AttachmentList[0].Path);
                            imgages.showextalink();
                        }
 
                        //if (obj.Data.VarietyAttribute == "@VarietyAttributeEnum.Reagent.ParseToInt()") {
                        //    //试剂类可自定义输入计量单位
                        //    $('#unitName').prop('readonly', false);
                        //}
                        //else {
                        //    $('#unitName').prop('readonly', true);
                        //}

                        if (obj.Data.ModelStandardId == 0) {
                            var modelData = [{ Id: 0, Model: '手动填写' }];
                            $('#modelStandardId').ysComboBox({
                                data: modelData,
                                key: 'Id',
                                value: 'Model',
                                class: "form-control",
                            });
                            $('#modelStandardId').ysComboBox('setValue', 0);

                            var model = obj.Data.Model;
                            if (model == '手动填写'){
                                model = "";
                            }
                            $("#model").val(model);
                        }
                    }
                }
            });
        }
        else {
            //通过父级对象赋值
            var obj = parent.objData;
            console.log("parent.objData:" + JSON.stringify(parent.objData));
            if (obj.InstrumentStandardId >0){
                $("#divCode").show();
                getInstrumentModel(obj.InstrumentStandardId, obj.ModelStandardId, obj.Model);
            }else{
                $("#divCode").hide();
                getInstrumentModel(0);
            }
            if (obj.ModelStandardId == 0){

                var modelData = [{ Id: 0, Model: '手动填写' }];
                $('#modelStandardId').ysComboBox({
                    data: modelData,
                    key: 'Id',
                    value: 'Model',
                    class: "form-control",
                });
                $('#modelStandardId').ysComboBox('setValue', 0);
            }
   
            //console.log("obj:"+JSON.stringify(obj));
            getSchoolStage(obj.StageId);
            getCourse(obj.CourseId);
            $('#form').setWebControls(obj);
        }
    }

    function setInstrumentStandard(id, name) {
        $('#instrumentStandardId').val(id);
        $('#name').val(name);
        getInstrumentEntity(id);
        getInstrumentModel(id);
    }

    function getInstrumentModel(id, defaultValue,modelValue) {
    
        $('#modelStandardId').ysComboBox({
            data: [],
            key: 'Id',
            value: 'Model',
            class: "form-control",
            onChange: function () {
                if (!IsEdit) {
                    var modeleId = $("#modelStandardId").ysComboBox("getValue");

                    if (modeleId == '') {
                        $('#divModel').hide();
                        $('#model').val('');
                    }
                    else {
                        $('#divModel').show();
                        var model = $("#modelStandardId option:checked").text();
                        var showText = model == '手动填写' ? '' : model;
                        if (modelValue != undefined && modelValue != "" && model == "手动填写") {
                            $('#model').val(modelValue);
                        }else{
                            $('#model').val(showText);
                        }

                        if (modeleId == 0) {
                            var modeCode = $("#code").val();
                            if (modeCode != undefined && modeCode != "") {
                                modeCode = modeCode.substring(0, 9) + "00";
                                $("#code").val(modeCode);
                            }
                        }else{
                            //
                            $.each(modelStandardData, function (index, item) {
                                if (item.Id == modeleId) {
                                    $("#code").val(item.Code);
                                    return false;
                                }
                            });
                        }
                    }
                }
                else {
                    IsEdit = false;
                }

               
            }
        });

        if (id > 0) {
            var param = { Pid: id, ClassType: 2 };
            ys.ajax({
                url: '@Url.Content("~/InstrumentManage/InstrumentStandard/GetInstrumentStandard")',
                data: { param: param },
                type: 'post',
                success: function (obj) {
                    if (obj.Tag == 1 && obj.Data) {
                        obj.Data.push({ Id: 0, Model: '手动填写' });
                        $('#modelStandardId').ysComboBox({
                            data: obj.Data,
                            key: 'Id',
                            value: 'Model',
                            class: "form-control",
                        });

                        //console.log("obj.Data", obj.Data);
                        
                        modelStandardData = obj.Data;

                        if (defaultValue > -1) {
                            $('#modelStandardId').ysComboBox('setValue', defaultValue);
                        }
                        else {
                            if (obj.Data.length == 1) {
                                $('#modelStandardId').ysComboBox('setValue', obj.Data[0].Id);
                            }
                        }
                    }
                }
            });
        }

        
    }

    function getInstrumentEntity(id) {
        ys.ajax({
            url: '@Url.Content("~/InstrumentManage/InstrumentStandard/GetEntity")' + '?id=' + id,
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1 && obj.Data) {
                    $('#unitName').val(obj.Data.UnitName);
                    $('#name').val(obj.Data.Name);
                    $('#code').val(obj.Data.Code);

                    //if (obj.Data.VarietyAttribute == "@VarietyAttributeEnum.Reagent.ParseToInt()") {
                    //    //试剂类可自定义输入计量单位
                    //    $('#unitName').prop('readonly', false);
                    //}
                    //else {
                    //    $('#unitName').prop('readonly', true);
                    //}
                }
            }
        });
    }

    function getSchoolStage(courseId, defaultValue) {
        courseId = 0;
        ys.ajax({
            url: '@Url.Content("~/BusinessManage/UserSchoolStageSubject/GetSchoolStageByUser")' + '?courseId=' + (courseId || 0),
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1) {
                    $('#stageId').html('');
                    $('#stageId').ysCheckBox({
                        data: obj.Data,
                        key: 'DictionaryId',
                        value: 'DicName',
                        class: "form-control"
                    });
                    if (obj.Data.length == 0) {
                        ComBox.LoadPageMessage('您管理的学科', '/BusinessManage/UserSchoolStageSubject/StageSubjectInput', '实验员授权', @RoleEnum.BusinessManager.ParseToInt());
                    }
                    else {
                        if (defaultValue) {
                            $('#stageId').ysCheckBox('setValue', defaultValue);
                        }
                        else if (obj.Data.length == 1) {
                            $('#stageId').ysCheckBox('setValue', obj.Data[0].DictionaryId);
                        }
                    }
                }
            }
        });
    }

    function getCourse(defaultValue) {
        ys.ajax({
            url: '@Url.Content("~/BusinessManage/UserSchoolStageSubject/GetSubjectByUser")',
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1) {
                    $('#courseId').ysComboBox({
                        data: obj.Data,
                        key: 'DictionaryId',
                        value: 'DicName',
                        class: "form-control",
                        onChange: function () {
                            if (!IsEditCourse) getSchoolStage($('#courseId').ysComboBox('getValue'), '');
                            else IsEditCourse = false;
                        }
                    });
                    if (defaultValue > 0) {
                        $('#courseId').ysComboBox('setValue', defaultValue);
                    }
                    else if (obj.Data.length == 1) {
                        $('#courseId').ysComboBox('setValue', obj.Data[0].DictionaryId);
                    }
                }
            }
        });
    }

    function loadUploadify() {
        $("#uploadify").uploadifive({
            'uploadScript': '/File/UploadFile?fc=1200',
            'cancelImg': '~/lib/uploadifive/uploadifive-cancel.png',
            'buttonText': '上 传',
            'queueID': 'fileQueue',
            'auto': true,
            'multi': false,
            'fileDesc': '支持格式：*.jpg;*.jpeg;*.png;',
            'fileExt': '*.jpg;*.jpeg;*.png;',
            'onAddQueueItem': function (file) {

            },
            'onUploadComplete': function (fileObj, response) {
                if (typeof (response) == "string" && response != "") {
                    response = JSON.parse(response);
                    if (response.Tag == 1) {
                        $('#AttachmentId').val(response.Description);
                        $('#Path').val(response.Data);
                        $('#imgAward').attr("src", response.Data);
                        $("#awardSrc").attr("src", response.Data);
                        imgages.showextalink();
                        ys.msgSuccess("上传成功！");
                    } else {
                        ys.msgError(response.Message);
                    }
                }
            },
            'onFallback': function (event) {
                ys.msgError(event);
            }
        });
    }

    function saveForm(index) {
        if ($('#form').validate().form()) {
            var Stage = "";
            var listGrade = [];
            $("input:checkbox[name='stageId_checkbox']:checked").each(function () {
                listGrade.push($(this).parent().parent().text());
            });
            if (listGrade.length > 0){
                Stage = listGrade.join("|");
            }
            var postData = $('#form').getWebControls({
                Id: id,
                ModelStandardId: $('#modelStandardId').ysComboBox('getValue'),
                StageId: $('#stageId').ysCheckBox('getValue'),
                CourseId: $('#courseId').ysComboBox('getValue'),
                Course: $("#courseId option:selected").text(),
                Stage : Stage
            });

           
            //console.log("listGrade:"+JSON.stringify(listGrade));
            //return;

            if (postData.SupplierName==""){
                postData.SupplierName = "未知";
            }
            if (postData.PurchaseDate == "") {
                var date = new Date();
                var year = date.getFullYear();
                var month = date.getMonth() + 1;
                var day = date.getDate();
                month = (month > 9) ? month : ("0" + month);
                day = (day < 10) ? ("0" + day) : day;
                var today = year + "-" + month + "-" + day;
                postData.PurchaseDate = today;
            }
       
            //console.log("postData:"+JSON.stringify(postData));
            //return;

            var checkErr = checkMessage(postData);
            if (checkErr != '') {
                ys.msgError(checkErr);
                return false;
            }

            if ($('#storagePlace').ysComboBoxTree('getValue') == undefined) {
                postData.StoragePlace = "";
            }

            ys.ajax({
                url: '@Url.Content("~/InstrumentManage/SchoolInstrument/SaveInstrumentFormJson")',
                type: 'post',
                data: postData,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        //console.log("obj.Data:" + obj.Data);
                        postData.Id = obj.Data;
                        if (postData.Path != ""){
                            postData.AttachId = obj.Data;
                        }
                        parent.$.observable(parent.course_info.data).remove(parent.objDataIndex);
                        parent.$.observable(parent.course_info.data).insert(parent.objDataIndex, postData);

                        parent.layer.close(index);
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        }
    }

    //保存校验
    function checkMessage(postData) {
        var checkErr = '';
        if (!postData.ModelStandardId) {
            checkErr += '请选择规格属性！<br />';
        }
        else if (postData.Model.trim() == '') {
            checkErr += '请填写规格属性！<br />';
        }
        if (!postData.StageId) {
            checkErr += '请选择适用学段！<br />';
        }
        if (!postData.CourseId) {
            checkErr += '请选择适用学科 ！<br />';
        }
        if (postData.IsSelfMade == '' || postData.IsSelfMade == undefined) {
            checkErr += '请选择是否自制教具 ！<br />';
        }
        if (SaveValiDate != undefined && SaveValiDate.length > 0) {
            SaveValiDate.map(function (item, i) {
                if (item.typecode == "Image") {
                    if (!(parseFloat(postData.AttachmentId) > 0)) {
                        checkErr += (item.verifyhint + '<br/>');
                    }
                } else if (item.typecode == "SupplierName") {
                    if (!(postData.SupplierName != undefined && postData.SupplierName.length > 0)) {
                        checkErr += (item.verifyhint + '<br/>');
                    }
                } else if (item.typecode == "Brand") {
                    if (!(postData.Brand != undefined && postData.Brand.length > 0)) {
                        checkErr += (item.verifyhint + '<br/>');
                    }
                } else if (item.typecode == "Floor" && IsShowFloor == 1) {
                    if (!(postData.Floor != undefined && postData.Floor.length > 0)) {
                        checkErr += (item.verifyhint + '<br/>');
                    }
                }
            });
        }

        return checkErr;
    }
   
    //#region 加载配置验证信息
    function loadFormVali() {
        ys.ajax({
            url: '@Url.Content("~/SystemManage/ConfigSet/GetListByCode")',
            type: 'Post',
            data: { typecode: '1SYYQ-2YQRK-1YQLR' },
            success: function (obj) {
                if (obj.Tag == 1) {
                    var validateRule = {};
                    var validateMessage = {};
                    validateRule.name = { required: true };
                    validateRule.model = { required: true };
                    validateRule.unitName = { required: true };
                    validateRule.num = { required: true, number: true, thanMinValue: 0 };
                    //validateRule.price = { required: true, number: true };
                    validateRule.stageId = { required: true };
                    validateRule.courseId = { required: true };
                    validateRule.purchaseDate = { required: true };
                    validateRule.warrantyMonth = { required: true, number: true };
                    if (obj.Data != undefined && obj.Data.length > 0) {
                        for (var i = 0; i < obj.Data.length; i++) {
                            var typecode = obj.Data[i].TypeCode.replaceAll("1SYYQ-2YQRK-1YQLR-", "");
                            if (obj.Data[i].ConfigValue == "1") {
                                var verifyhint = "";
                                if (obj.Data[i].VerifyHint != undefined && obj.Data[i].VerifyHint.length > 0) {
                                    verifyhint = obj.Data[i].VerifyHint;
                                }
                                $("#lab" + typecode).append('<font class="red"> *</font>');
                                if (typecode == "SupplierName") {
                                    validateRule.SupplierName = { required: true };
                                    validateMessage.SupplierName = { required: verifyhint };
                                    SaveValiDate.push({ typecode: typecode, verifyhint: verifyhint });
                                } else if (typecode == "Brand") {
                                    validateRule.Brand = { required: true };
                                    validateMessage.Brand = { required: verifyhint };
                                } else if (typecode == "Image" || typecode == "Floor") {
                                    SaveValiDate.push({ typecode: typecode, verifyhint: verifyhint });
                                }
                                else if (typecode == "Price") {
                                    validateRule.price = { required: true };
                                    validateMessage.price = { required: verifyhint };
                                    SaveValiDate.push({ typecode: typecode, verifyhint: verifyhint });
                                }
                            } else {
                                if (typecode == 'Price' && $('#price').val() == 0) $('#price').val('');
                            }
                        }
                    }
                    $('#form').validate({
                        rules: validateRule,
                        messages: validateMessage
                    });
                }
            }
        });
    }

</script>

