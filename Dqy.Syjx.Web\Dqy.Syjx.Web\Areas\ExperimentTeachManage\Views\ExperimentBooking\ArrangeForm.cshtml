﻿@{
    Layout = "~/Views/Shared/_Index.cshtml";
}
<style type="text/css">
    .check-box {
        width: 100px;
    }

    .iradio-box {
        width: 100px;
    }

    .iradio-box {
        position: initial;
        display: inline-block;
    }

    .iradio-blue {
        position: inherit;
        display: inline-block;
        /*  top: 6px;*/
    }
    .div-experit {
        border: 1px dashed #999;
        margin: 15px 0px 15px 0px;
        padding-top: 15px;
    }
</style>
<div class="container-div">

    <div class="row" style="height:auto;">
        <div class="col-sm-12">
            <div class="ibox float-e-margins">
                <div class="ibox-title">
                    <h5>实验预约</h5>
                    <div class="ibox-tools">
                        <a class="collapse-link">
                            <i class="fa fa-chevron-up"></i>
                        </a>
                    </div>
                </div>
                <div class="ibox-content">
                    <div class="row">
                        <div class="col-sm-12" style="font-size:14px">
                            <div class="card-body">
                                <form id="baseForm" class="form-horizontal m">
                                    <div class="form-group row">
                                        <label class="col-sm-2 control-label ">预约人</label>
                                        <div class="col-sm-8">
                                            <input id="bookUserName" col="BookUserName" class="form-control" readonly="readonly" />
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-sm-2 control-label ">上课班级</label>
                                        <div class="col-sm-8">
                                            <input id="className" col="ClassName" class="form-control" readonly="readonly" />
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-sm-2 control-label ">学科</label>
                                        <div class="col-sm-8">
                                            <input id="courseName" col="CourseName" class="form-control" readonly="readonly" />
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-sm-2 control-label ">上课时间</label>
                                        <div class="col-sm-8">
                                            <input id="classTime" col="ClassTime" class="form-control" readonly="readonly" />
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-sm-2 control-label ">上课节次</label>
                                        <div class="col-sm-8">
                                            <input id="sectionShow" col="SectionShow" class="form-control" readonly="readonly" />
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-sm-2 control-label ">上课地点</label>
                                        <div class="col-sm-8">
                                            <input id="funRoomName" col="FunRoomName" class="form-control" readonly="readonly" />
                                        </div>
                                    </div>

                                    @*<div class="divOne">
                                        <div class="form-group row">
                                            <label class="col-sm-2 control-label ">实验来源</label>
                                            <div class="col-sm-8">
                                                <input id="sourceType" col="SourceType" class="form-control" readonly="readonly" />
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label class="col-sm-2 control-label ">实验名称</label>
                                            <div class="col-sm-8">
                                                <input id="experimentName" col="ExperimentName" class="form-control" readonly="readonly" />
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label class="col-sm-2 control-label ">实验类型</label>
                                            <div class="col-sm-8">
                                                <input id="experimentType" col="ExperimentType" class="form-control" readonly="readonly" />
                                            </div>
                                        </div>
                                        <div class="form-group row" id="divGroupz" style="display:none;">
                                            <label class="col-sm-2 control-label ">分组数</label>
                                            <div class="col-sm-8">
                                                <input id="groupz" col="Groupz" class="form-control" readonly="readonly" />
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label class="col-sm-2 control-label ">所需仪器</label>
                                            <div class="col-sm-8">
                                                <textarea id="equipmentNeed" col="EquipmentNeed" class="form-control" readonly></textarea>
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label class="col-sm-2 control-label ">所需耗材</label>
                                            <div class="col-sm-8">
                                                <textarea id="materialNeed" col="MaterialNeed" class="form-control" readonly></textarea>
                                            </div>
                                        </div>
                                    </div>*@
                                    <div id="divMore">
                                    </div>

                                    <div class="form-group row">
                                        <label class="col-sm-2 control-label ">预约说明</label>
                                        <div class="col-sm-8">
                                            <textarea id="remark" col="Remark" class="form-control" readonly></textarea>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row" style="height:auto;">
        <div class="col-sm-12">
            <div class="ibox float-e-margins">
                <div class="ibox-title">
                    <h5>实验安排</h5><span style="margin-left:20px;color:#999;">核实实验仪器是否满足上课需要</span>
                    <div class="ibox-tools">
                    </div>
                </div>
                <div class="ibox-content">
                    <div class="row">
                        <div class="col-sm-12" style="font-size:14px">
                            <div class="card-body">
                                <form id="fileConfigForm" class="form-horizontal m">
                                    @*<div class="divOne">
                                        <div class="form-group row">
                                            <label class="col-sm-2 control-label " id="labEquipmentNeed">所需仪器</label>
                                            <div class="col-sm-8">
                                                <textarea id="arrangerEquipmentNeed" col="ArrangerEquipmentNeed" class="form-control"></textarea>
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label class="col-sm-2 control-label " id="labMaterialNeed">所需耗材</label>
                                            <div class="col-sm-8">
                                                <textarea id="arrangerMaterialNeed" col="ArrangerMaterialNeed" class="form-control"></textarea>
                                            </div>
                                        </div>
                                    </div>*@
                                    <div id="divMoreAp">

                                    </div>
                                    <div class="form-group row">
                                        <label class="col-sm-2 control-label ">安排结果<font class="red"> *</font></label>
                                        <div class="col-sm-10">
                                            <div id="statuz" col="Statuz"></div>
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-sm-2 control-label ">安排意见<font class="red"> *</font></label>
                                        <div class="col-sm-8">
                                            <textarea id="approvalRemark" col="ApprovalRemark" class="form-control"></textarea>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row" style="text-align:center;" id="divButton">
        <a id="btnAdd" class="btn btn-info" onclick="saveData()"><i class="fa fa-save"></i> 提交</a>
        <span style="margin-left:20px;color:#999;">下一步转交预约人登记实验</span>
    </div>
</div>

<script type="text/javascript">
    var id = ys.request("id");
    var SaveValiDate = [];
    var IsRequiredEquipmentNeed = 0;
    var IsRequiredMaterialNeed = 0;

    $(function () {
        loadStatuz();
        getForm();
        loadPageSetValid();
    });

    function getForm() {
        if (id > 0) {
            ys.ajax({
                url: '@Url.Content("~/ExperimentTeachManage/ExperimentBooking/GetDetailJson")' + '?id=' + id,
                type: 'get',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        obj.Data.ClassTime = ys.formatDate(obj.Data.ClassTime, "yyyy-MM-dd");
                        $('#baseForm').setWebControls(obj.Data);

                        $.each(obj.Data.ChildList, function (i, v) {
                            console.log(v.SchoolExperimentId);

                            v.SourceType = v.SourceType == @SourceTypeEnum.ExperimentPlan.ParseToInt() ? "@SourceTypeEnum.ExperimentPlan.GetDescription()" : "@SourceTypeEnum.ExperimentList.GetDescription()";
                            v.ExperimentType = v.ExperimentType == @ExperimentTypeEnum.Demo.ParseToInt() ? "@ExperimentTypeEnum.Demo.GetDescription()" : "@ExperimentTypeEnum.Group.GetDescription()";
                            var html = $.Format('<div class="{0}">', obj.Data.ChildList.length > 1 ? 'div-experit' : '');
                            html += '<div class="form-group row">';
                            html += $.Format('<label class="col-sm-2 control-label ">实验来源</label><div class="col-sm-8"><input class="form-control" readonly="readonly" value="{0}" /></div>', v.SourceType);
                            html += '</div>';
                            html += '<div class="form-group row">';
                            html += $.Format('<label class="col-sm-2 control-label ">实验名称</label><div class="col-sm-8"><input class="form-control" readonly="readonly" value="{0}" /></div>', v.ExperimentName);
                            html += '</div>';
                            html += '<div class="form-group row">';
                            html += $.Format('<label class="col-sm-2 control-label ">实验类型</label><div class="col-sm-8"><input class="form-control" readonly="readonly" value="{0}" /></div>', v.ExperimentType);
                            html += '</div>';
                            if (v.ExperimentType == "@ExperimentTypeEnum.Group.GetDescription()") {
                                html += '<div class="form-group row">';
                                html += $.Format('<label class="col-sm-2 control-label ">分组数</label><div class="col-sm-8"><input class="form-control" readonly="readonly" value="{0}" /></div>', v.Groupz);
                                html += '</div>';
                            }
                            html += '<div class="form-group row">';
                            html += $.Format('<label class="col-sm-2 control-label ">所需仪器</label><div class="col-sm-8"><textarea class="form-control" readonly>{0}</textarea></div>', v.EquipmentNeed ?? '');
                            html += '</div>';
                            html += '<div class="form-group row">';
                            html += $.Format('<label class="col-sm-2 control-label ">所需耗材</label><div class="col-sm-8"><textarea class="form-control" readonly>{0}</textarea></div>', v.MaterialNeed ?? '');
                            html += '</div>';
                            html += ' </div>';
                            $('#divMore').append(html);

                            html = $.Format('<div class="{0}">', obj.Data.ChildList.length > 1 ? 'div-experit' : '');
                            if (obj.Data.ChildList.length > 1) {
                                html += '<div class="form-group row">';
                                html += $.Format('<label class="col-sm-2 control-label ">实验名称</label><div class="col-sm-8"><input class="form-control" readonly="readonly" value="{0}" /></div>', v.ExperimentName);
                                html += '</div>';
                            }

                            html += getEquipmentNeedHtml(v.Id, v.EquipmentNeed, v.PlanDetailId, v.TextbookVersionDetailId, v.SchoolExperimentId);
                            html += getMaterialNeedHtml(v.Id, v.MaterialNeed);

                            //html += '<div class="form-group row">';
                            //html += $.Format('<label class="col-sm-2 control-label ">所需仪器</label><div class="col-sm-8"><textarea id="arrangerEquipmentNeed_{0}" bookid="{0}" plandetailid="{1}" textbookversiondetailid="{2}" schoolexperimentid="{3}" class="form-control"></textarea></div>', v.Id, v.PlanDetailId, v.TextbookVersionDetailId, v.SchoolExperimentId);
                            //html += '</div>';
                            //html += '<div class="form-group row">';
                            //html += $.Format('<label class="col-sm-2 control-label ">所需耗材</label><div class="col-sm-8"><textarea id="arrangerMaterialNeed_{0}" class="form-control"></textarea></div>', v.Id);
                            //html += '</div>';

                            html += ' </div>';
                            $('#divMoreAp').append(html);

                            loadNeedValue(v.Id);
                        });
                    }
                }
            });
            //loadNeedValue();
        }
    }

    function loadStatuz() {
        var html = '';
        html += '<label class="iradio-box"><div class="iradio-blue pass" style="vertical-align:bottom;"><input name="inputType_checkbox" type="radio" value="@ExperimentBookStatuzEnum.WaitRecord.ParseToInt()" style="position: absolute; opacity: 0;"></div> 通过</label>';
        html += '<label class="iradio-box"><div class="iradio-blue nopass" style="vertical-align:bottom;"><input name="inputType_checkbox" type="radio" value="@ExperimentBookStatuzEnum.ArrangeNoPass.ParseToInt()" style="position: absolute; opacity: 0;"></div> 不通过</label>';
        $("#statuz").html(html);

        $('input[name="inputType_checkbox"]').change(function () {
            if ($(this).prop("checked")) {
                var val = $(this).val();
                if (val == @ExperimentBookStatuzEnum.WaitRecord.ParseToInt()) {
                    $(".pass").addClass("checked");
                    $(".nopass").removeClass("checked");
                    if ($('#approvalRemark').val() == '')
                        $('#approvalRemark').val('同意');

                } else {
                    $(".nopass").addClass("checked");
                    $(".pass").removeClass("checked");
                    if ($('#approvalRemark').val() == '同意')
                        $('#approvalRemark').val('');
                }
            }
        });
    }

    function saveData(index) {
        var statuz = $('input[name="inputType_checkbox"]:checked').val();
        if (statuz > 0) {
            var remark = $('#approvalRemark').val();
            if (statuz % 2 == 1 && remark == '') {
                ys.msgError('不通过必须填写审批意见！');
                return false;
            }

            var errorMsg = '';
            if (SaveValiDate != undefined && SaveValiDate.length > 0) {
                SaveValiDate.map(function (item, i) {

                    if (item.typecode == "EquipmentNeed") {
                        $("[id^='arrangerEquipmentNeed_']").each(function () {
                            if ($(this).val() == '') {
                                errorMsg += (item.verifyhint + '。<br/>');
                                return false;
                            }
                        });
                        //if (!($('#arrangerEquipmentNeed').val() != undefined && $('#arrangerEquipmentNeed').val().length > 0)) {
                        //    errorMsg += (item.verifyhint + '。<br/>');
                        //}
                    } else if (item.typecode == "MaterialNeed") {
                        //if (!($('#arrangerMaterialNeed').val() != undefined && $('#arrangerMaterialNeed').val().length > 0)) {
                        //    errorMsg += (item.verifyhint + '。<br/>');
                        //}
                        $("[id^='arrangerMaterialNeed_']").each(function () {
                            if ($(this).val() == '') {
                                errorMsg += (item.verifyhint + '。<br/>');
                                return false;
                            }
                        });
                    }
                });
            }
            if (errorMsg != '') {
                ys.msgError('验证失败，请填写完成再提交！<br/>' + errorMsg);
                return;
            }

            ys.confirm('您确认安排' + (statuz % 2 == 1 ? '不通过' : '通过') + '吗？', function () {
                var descList = [];
                $("[id^='arrangerEquipmentNeed_']").each(function () {
                    var bookId = $(this).attr('bookid');
                    descList.push({
                        PlanDetailId: $(this).attr('plandetailid') || 0,
                        TextbookVersionDetailId: $(this).attr('textbookversiondetailid') || 0,
                        SchoolExperimentId: $(this).attr('schoolexperimentid') || 0,
                        ArrangerEquipmentNeed: $(this).val(),
                        ArrangerMaterialNeed: $('#arrangerMaterialNeed_' + bookId).val()
                    });
                });
                var postData = {
                    Id: id,
                    Statuz: statuz,
                    ArrangerRemark: $('#approvalRemark').val(),
                    ExperimentOperaType: @ExperimentOperaEnum.Arrange.ParseToInt() ,
                    DescList: descList
                }
                ys.ajax({
                    url: '@Url.Content("~/ExperimentTeachManage/ExperimentBooking/ArrangeRecordJson")',
                    type: 'post',
                    data: { model: postData},
                    success: function (obj) {
                        if (obj.Tag == 1) {
                            ys.msgSuccess(obj.Message);
                            var url = '@Url.Content("~/ExperimentTeachManage/ExperimentBooking/WaitArrangeList")';
                            createMenuAndCloseCurrent(url, "待安排实验");
                        }
                        else {
                            ys.msgError(obj.Message);
                        }
                    }
                });
            });
        }
        else {
            ys.msgError('请选择审批结果！');
        }
    }

    function loadNeedValue(bookId) {
        ys.ajax({
            url: '@Url.Content("~/ExperimentTeachManage/ExperimentBooking/GetArrangerNeedValue")' + '?id=' + bookId,
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1) {
                    $('#arrangerEquipmentNeed_' + bookId).val(obj.Data.ArrangerEquipmentNeed);
                    $('#arrangerMaterialNeed_' + bookId).val(obj.Data.ArrangerMaterialNeed);
                }
            }
        });
    }

    /*获取数据库配置的填报验证*/
    function loadPageSetValid() {

        ys.ajax({
            url: '@Url.Content("~/SystemManage/ConfigSet/GetListByCode")',
            type: 'Post',
            data: { typecode: '3SYJX-2SYAP-1APSY' },
            async: false,
            success: function (obj) {
                if (obj.Tag == 1) {
                    if (obj.Data != undefined && obj.Data.length > 0) {
                        for (var i = 0; i < obj.Data.length; i++) {
                            if (obj.Data[i].ConfigValue == "1") {
                                var typecode = obj.Data[i].TypeCode.replaceAll("3SYJX-2SYAP-1APSY-", "");

                                var verifyhint = "";
                                if (obj.Data[i].VerifyHint != undefined && obj.Data[i].VerifyHint.length > 0) {
                                    verifyhint = obj.Data[i].VerifyHint;
                                }
                                $("#lab" + typecode).append('<font class="red"> *</font>');

                                if (typecode == "EquipmentNeed") {
                                    IsRequiredEquipmentNeed = 1;
                                    SaveValiDate.push({ typecode: typecode, verifyhint: verifyhint });
                                } else if (typecode == "MaterialNeed") {
                                    IsRequiredMaterialNeed = 1;
                                    SaveValiDate.push({ typecode: typecode, verifyhint: verifyhint });
                                }
                            }
                        }
                    }
                }
            }
        });
    }

    function getEquipmentNeedHtml(id, textval, PlanDetailId, TextbookVersionDetailId, SchoolExperimentId) {
        var requiredHtml = '';
        if (IsRequiredEquipmentNeed == 1) {
            requiredHtml = '<font class="red">*</font>';
        }
        var html = '<div class="form-group row">';
        html += '<label class="col-sm-2 control-label " for="arrangerEquipmentNeed_' + id + '">所需仪器' + requiredHtml + '</label>';
        html += '<div class="col-sm-6">';
        html += '  <textarea id="arrangerEquipmentNeed_' + id + '" bookid=' + id + ' plandetailid=' + PlanDetailId + ' textbookversiondetailid=' + TextbookVersionDetailId + ' schoolexperimentid=' + SchoolExperimentId+' class="form-control">' + textval + '</textarea>';
        html += ' </div>';
        html += ' </div>';
        return html;
    }

    function getMaterialNeedHtml(id, textval) {
        var requiredHtml = '';
        if (IsRequiredMaterialNeed == 1) {
            requiredHtml = '<font class="red">*</font>';
        }
        var html = '<div class="form-group row">';
        html += '<label class="col-sm-2 control-label " for="arrangerMaterialNeed_' + id + '">所需耗材' + requiredHtml + '</label>';
        html += '<div class="col-sm-6">';
        html += '  <textarea id="arrangerMaterialNeed_' + id + '" class="form-control">' + textval + '</textarea>';
        html += ' </div>';
        html += ' <div class="col-sm-3 tag_msg_color">可修改 </div>';
        html += ' </div>';
        return html;
    }
</script>

