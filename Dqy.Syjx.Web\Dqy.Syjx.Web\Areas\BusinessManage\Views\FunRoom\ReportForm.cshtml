﻿@{
    Layout = "~/Views/Shared/_FormWhite.cshtml";
}
@inject Microsoft.AspNetCore.Hosting.IWebHostEnvironment HostingEnvironment
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/uploadifive/File.min.css"))
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/uploadifive/File.min.js"))
<style type="text/css">
    .tag_msg_color {
        padding-top:8px; color: #999;
    }
</style>
<div class="wrapper animated fadeInRight">
    <form id="form" class="form-horizontal m">
        <input type="hidden" id="Id" col="Id" value="0" />
        <div style="height: 55px;display:none;" id="fileQueue"></div>
        <div class="form-group row">
            <label class="col-sm-2 control-label ">功能简介</label>
            <div class="col-sm-7" style="min-height:60px;">
                <div>
                    <input type="file" name="uploadify" id="uploadify_1021"/> 
                </div>
                &nbsp;&nbsp;
                <div id="spanFile_1021"></div>
            </div>
            <div class="col-sm-2 tag_msg_color">
                <span class="glyphicon glyphicon-question-sign titleprompt inputprompt" aria-hidden="true" data-container="body" data-toggle="tooltip" data-content="该室的功能简介、特色教学等；小于5M，支持PDF格式"></span>

            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-2 control-label ">管理制度</label>
            <div class="col-sm-7" style="min-height:60px;">
                <div>
                    <input type="file" name="uploadify" id="uploadify_1022"/>

                </div>
                &nbsp;&nbsp;
                <div id="spanFile_1022"></div>
            </div>
            <div class="col-sm-2 tag_msg_color"> 
                    <span class="glyphicon glyphicon-question-sign titleprompt inputprompt" aria-hidden="true" data-container="body" data-toggle="tooltip" data-content="该室的管理制度、操作流程、考核制度；小于5M，支持PDF格式"></span>
            </div>
        </div>
    </form>
</div>
<script type="text/javascript">
    var id = ys.request("id");
    $(function () {
        $(".inputprompt").popover({
            trigger: 'hover',
            placement: 'left',
            html: true
        });
        parent.removeConfirm();
        loadUploadify();
        getForm();
    });
    function loadUploadify() {
        $("#uploadify_1021").uploadifive({
            'uploadScript': '/File/UploadFile?fc=1021',
            'cancelImg': '~/lib/uploadifive/uploadifive-cancel.png',
            'buttonText': '上 传',
            //'formData': { "token": "", "UserId": "0", "folder": "/UploadFile/Attachment" },
            'queueID': 'fileQueue',
            'auto': true,
            'multi': false,
            'fileDesc': '支持格式：*.jpg;*.gif;*.png;*.pdf;',
            'fileExt': '*.jpg;*.gif;*.png;*.pdf;',
            'onAddQueueItem': function (file) {

            },
            'onUploadComplete': function (fileObj, response) {
                saveForm(response, 1021);
            },
            'onFallback': function (event) {
                ys.msgError(event);
            }
        });
        $("#uploadify_1022").uploadifive({
            'uploadScript': '/File/UploadFile?fc=1022',
            'cancelImg': '~/lib/uploadifive/uploadifive-cancel.png',
            'buttonText': '上 传',
            //'formData': { "token": "", "UserId": "0", "folder": "/UploadFile/Attachment" },
            'queueID': 'fileQueue',
            'auto': true,
            'multi': false,
            'fileDesc': '支持格式：*.jpg;*.gif;*.png;*.pdf;',
            'fileExt': '*.jpg;*.gif;*.png;*.pdf;',
            'onAddQueueItem': function (file) {

            },
            'onUploadComplete': function (fileObj, response) {
                saveForm(response, 1022);
            },
            'onFallback': function (event) {
                ys.msgError(event);
            }
        });
    }
    function getForm() {
        if (id > 0) {
            ys.ajax({
                url: '@Url.Content("~/BusinessManage/FunRoom/GetReportFormJson")' + '?id=' + id,
                type: 'get',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        //调用显示附件信息
                        if (isNaN(obj.Data) && obj.Data != undefined) {
                            var fileList = obj.Data;
                            if (fileList.length > 0) {
                                for (var i = 0; i < fileList.length; i++) {
                                    var id = fileList[i].Id;
                                    var filePath = fileList[i].Path;
                                    var fileExt = fileList[i].Ext.toUpperCase();
                                    var fileTitle = fileList[i].Title;
                                    if (fileExt == ".JPG" || fileExt == ".BMP" || fileExt == ".JPEG" || fileExt == ".PNG" || fileExt == ".GIF") {
                                        $("#spanFile_" + fileList[i].FileCategory).append('<span class="keywords" idValue="' + id + '"> <a type="del" onclick="delFile(this,\'' + id + '\')"></a><a  modal="zoomImg"  href="javascript:void(0);" src="' + filePath + '" >' + fileTitle + '</a></span>');
                                    } else {
                                        $("#spanFile_" + fileList[i].FileCategory).append('<span class="keywords" idValue="' + id + '"> <a type="del" onclick="delFile(this,\'' + id + '\')"></a><a target="_blank" href="' + filePath + '" >' + fileTitle + '</a></span>');
                                    }
                                }
                                imgages.showextalink();
                            }
                        }
                    }
                }
            });
        }
    }
    function saveForm(response, catelogry) {

        if (typeof (response) == "string" && response != "") {
            response = JSON.parse(response);
            if (response.Tag == 1) {
                var attachmentid = response.Description;
                var postData = { id: id, attachmentid: attachmentid, catelogry: catelogry};
                ys.ajax({
                    url: '@Url.Content("~/BusinessManage/FunRoom/SaveReportJson")',
                    type: 'post',
                    data: postData,
                    success: function (obj) {
                        if (obj.Tag == 1) {
                            ys.msgSuccess("上传成功！");
                            var filePath = response.Data;
                            var fileTitle = response.Message;
                            var fileExt = filePath.substring(filePath.lastIndexOf('.')).toUpperCase();
                            if (fileExt == ".JPG" || fileExt == ".BMP" || fileExt == ".JPEG" || fileExt == ".PNG" || fileExt == ".GIF") {
                                $("#spanFile_" + catelogry).append('<span class="keywords" idValue="' + id + '"> <a type="del" onclick="delFile(this,\'' + id + '\')"></a><a  modal="zoomImg"  href="javascript:void(0);" src="' + filePath + '" >' + fileTitle + '</a></span>');
                            } else {
                                $("#spanFile_" + catelogry).append('<span class="keywords" idValue="' + id + '"> <a type="del" onclick="delFile(this,\'' + id + '\')"></a><a target="_blank" href="' + filePath + '" >' + fileTitle + '</a></span>');
                            }
                            parent.$('#gridTable').ysTable('refresh');parent.resetToolbarStatus();
                        }
                        else {
                            ys.msgError(obj.Message);
                        }
                    }
                });
            } else {
                ys.msgError(response.Message);
            }
        }
    } 
    function delFile(obj, value) {
        //删除附件
        var postData = { id: value };
        ys.ajax({
            url: '@Url.Content("~/BusinessManage/FunRoom/DeleteReportFormJson")',
            type: 'post',
            data: postData,
            success: function (obj) {
                console.log("obj" + JSON.stringify(obj));
                if (obj.Tag == 1) {
                    ys.msgSuccess("删除成功");
                    parent.$('#gridTable').ysTable('refresh');parent.resetToolbarStatus();
                }
                else {
                    ys.msgError(obj.Message);
                }
            }
        });
        $(obj).parent().remove();
        imgages.showextalink();
    }
</script>

