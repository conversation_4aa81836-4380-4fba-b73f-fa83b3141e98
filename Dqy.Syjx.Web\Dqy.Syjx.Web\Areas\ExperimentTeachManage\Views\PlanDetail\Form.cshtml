﻿@{
    Layout = "~/Views/Shared/_FormWhite.cshtml";
}

<div class="wrapper animated fadeInRight">
    <form id="form" class="form-horizontal m">
        <div class="form-group row">
            <label class="col-sm-3 control-label ">实验名称<font class="red"> *</font></label>
            <div class="col-sm-7">
                <input id="ExperimentName" col="ExperimentName" type="text" class="form-control" autocomplete="off"/>
            </div>
            <div class="col-sm-2"> </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">实验类型<font class="red"> *</font></label>
            <div class="col-sm-7">
                <div id="ExperimentType" col="ExperimentType"></div>
            </div>
            <div class="col-sm-2"> </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">实验要求<font class="red"> *</font></label>
            <div class="col-sm-7">
                <div id="IsNeedDo" col="IsNeedDo"></div>
            </div>
            <div class="col-sm-2"> </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">周次<font class="red"> *</font></label>
            <div class="col-sm-7">
                <div id="WeekNum" col="WeekNum"></div>
            </div>
            <div class="col-sm-2"> </div>
        </div>
    </form>
</div>

<script type="text/javascript">
    var id = ys.request("id");
    $(function () {
        $("#IsNeedDo").ysComboBox({ data: ys.getJson(@Html.Raw(typeof(IsNeedEnum).EnumToDictionaryString())), class: 'form-control', defaultName: '实验要求' });
        $("#ExperimentType").ysComboBox({
            data: ys.getJson(@Html.Raw(typeof(ExperimentTypeEnum).EnumToDictionaryString())),
            class: 'form-control',
            defaultName: '实验类型'
        });
        loadWeekNum();
        getForm();
        $('#form').validate({
            rules: {
                ExperimentName: { required: true }
            }
        });
    });
    function loadWeekNum() {
        var arr = [];
        for (var i = 1; i <= 30; i++) {
            arr.push({ id: i, name: i + ''  })
        }
        $('#WeekNum').ysComboBox({
            data: arr,
            class: 'form-control',
            key: 'id',
            value: 'name'
        });
    }
    function getForm() {
        if (id > 0) {
            ys.ajax({
                url: '@Url.Content("~/ExperimentTeachManage/PlanDetail/GetFormJson")' + '?id=' + id,
                type: 'get',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        $('#form').setWebControls(obj.Data);
                    }
                }
            });
        }
        else {
            var defaultData = {};
            $('#form').setWebControls(defaultData);
        }
    }

    function saveForm(index) {
        if ($('#form').validate().form()) {
            var postData = $('#form').getWebControls({ Id: id });
            //验证
            var errorMsg = '';
            if (!(postData.ExperimentName != undefined && postData.ExperimentName.length > 0)) {
                errorMsg += '请填写实验名称。<br/>';
            } else if (postData.ExperimentName.length > 100){
                errorMsg += '填写实验名称字符长度不能超过100。<br/>';
            }
            if (!(postData.ExperimentType > 0)) {
                errorMsg += '请选择实验类型。<br/>';
            }
            if (!(postData.IsNeedDo == @IsNeedEnum.MustDo.ParseToInt() || postData.IsNeedDo == @IsNeedEnum.SelectToDo.ParseToInt())) {
                errorMsg += '请选择实验要求。<br/>';
            }
            if (!(postData.WeekNum > 0)) {
                errorMsg += '请选择周次。<br/>';
            }
            if (errorMsg != '') {
                ys.msgError(errorMsg);
                return;
            }
            postData.PlanInfoId = parent.planinfoid;
            ys.ajax({
                url: '@Url.Content("~/ExperimentTeachManage/PlanDetail/SaveFormJson")',
                type: 'post',
                data: postData,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        parent.$('#gridTable').ysTable('refresh');parent.resetToolbarStatus();
                        parent.layer.close(index);
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        }
    }
</script>

