@{
    Layout = "~/Views/Shared/_FormWhite.cshtml";
}

<div class="wrapper animated fadeInRight">
    <form id="form" class="form-horizontal m">
        <div class="form-group row">
            <label class="col-sm-2 control-label ">登录名称<font class="red"> *</font></label>
            <div class="col-sm-4">
                <input id="userName" col="UserName" type="text" class="form-control" readonly="readonly" />
            </div>
            <label class="col-sm-2 control-label ">姓名</label>
            <div class="col-sm-4">
                <input id="realName" col="RealName" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">

            <label class="col-sm-2 control-label ">性别</label>
            <div class="col-sm-4" id="gender" col="Gender"></div>
            <label class="col-sm-2 control-label ">生日</label>
            <div class="col-sm-4">
                <input id="birthday" col="Birthday" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-2 control-label ">部门</label>
            <div class="col-sm-4">
                <input id="departmentNames" col="DepartmentNames" type="text" class="form-control" readonly="readonly" />
            </div>
            @*<label class="col-sm-2 control-label ">职位</label>
              <div class="col-sm-4">
                  <div id="positionId" col="PositionIds"></div>
              </div>*@
            <label class="col-sm-2 control-label ">手机</label>
            <div class="col-sm-4">
                <input id="mobile" col="Mobile" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-2 control-label ">邮箱</label>
            <div class="col-sm-4">
                <input id="email" col="Email" type="text" class="form-control" />
            </div>
        </div>
    </form>
</div>

<script type="text/javascript">

    var id = ys.request("id");

    $(function () {

        $("#userStatus").ysRadioBox({ data: ys.getJson(@Html.Raw(typeof(StatusEnum).EnumToDictionaryString())) });
        $("#gender").ysRadioBox({ data: ys.getJson(@Html.Raw(typeof(Dqy.Syjx.Enum.OrganizationManage.GenderTypeEnum).EnumToDictionaryString())) });

        $("#positionId").ysComboBox({
            url: '@Url.Content("~/OrganizationManage/Position/GetListJson")',
            dataName: "Result",
            key: "Id",
            value: "PositionName",
            class: "form-control",
            multiple: true
        });
        $("#positionId_select").attr("disabled", "disabled");

        laydate.render({ elem: '#birthday', format: 'yyyy-MM-dd', trigger: 'click', position: 'fixed' });

        getForm(id);

        $("#form").validate({
            rules: {
                userName: { required: true },
                password: {
                    required: true,
                    minlength: 8,
                    maxlength: 20
                },
                mobile: { isPhone: true },
                email: { email: true }
            }
        });
    });

    function getForm() {
        if (id > 0) {
            ys.ajax({
                url: '@Url.Content("~/OrganizationManage/User/GetFormJson")' + '?id=' + id,
                type: "get",
                success: function (obj) {
                    if (obj.Tag == 1) {
                        var result = obj.Data;
                        $("#form").setWebControls(result);
                    }
                }
            });
        }
    }

    function saveForm(index) {
        if ($("#form").validate().form()) {
            var postData = $("#form").getWebControls({ Id: id });
            ys.ajax({
                url: '@Url.Content("~/OrganizationManage/User/ChangeUserJson")',
                type: "post",
                data: postData,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        parent.getForm();
                        parent.layer.close(index);
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        }
    }
</script>
