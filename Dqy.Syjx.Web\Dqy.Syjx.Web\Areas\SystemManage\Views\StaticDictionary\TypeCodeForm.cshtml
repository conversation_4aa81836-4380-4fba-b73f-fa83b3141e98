﻿@{ Layout = "~/Views/Shared/_FormWhite.cshtml"; }

<div class="wrapper animated fadeInRight">
    <form id="form" class="form-horizontal m">
        <div class="form-group row">
            <label class="col-sm-3 control-label ">名称<font class="red"> *</font></label>
            <div class="col-sm-8">
                <div>
                    <input id="DicName" col="DicName" type="text" class="form-control" />
                </div>
                <span></span>
            </div>
        </div>
        <div class="form-group row" id="divNature">
            <label class="col-sm-3 control-label ">属性<font class="red"> *</font></label>
            <div class="col-sm-8">
                <div>
                   <div id="Nature" col="Nature"></div>
                </div>
                <span></span>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">排序<font class="red"> *</font></label>
            <div class="col-sm-8">
                <input id="Sequence" col="Sequence" type="text" class="form-control" />
            </div>
        </div>
    </form>
</div>

<script type="text/javascript">
    var id = ys.request("id");
    var typecode = ys.request("typecode");
    $(function () {
        loadNature();
        getForm();
        

        $("#form").validate({
            rules: {
                Sequence: { required: true, number: true },
                DicName: { required: true }
            }
        });
    });

    function loadNature() {
        if (parent.NatureDataJson != undefined && parent.NatureDataJson.length> 0) {
            $("#Nature").ysComboBox({
                data: parent.NatureDataJson,
                class: 'form-control',
                key: 'key',
                value: 'value'
            });
        } else {
            $("#divNature").hide();
        }
    }


    function getForm() {
        if (id > 0) {
            ys.ajax({
                url: '@Url.Content("~/SystemManage/StaticDictionary/GetFormJson")' + '?id=' + id,
                type: "get",
                success: function (obj) {
                    if (obj.Tag == 1) {
                        $("#form").setWebControls(obj.Data);
                    }
                }
            });
        }
        else {
            var defaultData = {};
            defaultData.Sequence = 0;
            defaultData.DicName = "";
            $("#form").setWebControls(defaultData);
        }
    }

    function saveForm(index) {
        if ($("#form").validate().form()) {
            var postData = $("#form").getWebControls({ Id: id });
            if (postData.Sequence != undefined && (postData.Sequence + '').indexOf('.') > -1) {
                ys.msgError("排序值请输入整数。");
                return false;
            }
            if (parent.NatureDataJson != undefined && parent.NatureDataJson.length > 0) {
                if (!(postData.Nature != undefined && parseInt(postData.Nature) > 0)) {
                    ys.msgError("请选择数据属性。");
                    return false;
                }
            }
            postData.TypeCode = typecode;
            postData.TypeName = typecode;
            postData.DictionaryId = 0;
            ys.ajax({
                url: '@Url.Content("~/SystemManage/StaticDictionary/SaveTypeCodeFormJson")',
                type: "post",
                data: postData,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        parent.$('#gridTable').ysTable('refresh');parent.resetToolbarStatus();
                        parent.layer.close(index);
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        }
    }
</script>

