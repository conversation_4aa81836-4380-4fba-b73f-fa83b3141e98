﻿@{
    Layout = "~/Views/Shared/_Index.cshtml";
}


<div class="container-div">
    <div class="row">
        <div class="col-sm-12" style="height:auto">
            <div class="form-group row" style="padding:5px;">
                <label id="labReportDate"></label>
            </div>
            <form id="form" class="form-horizontal m">
                <div class="card " style="margin-left:-15px;">
                    <div class="card-heading">
                        <b><i class="fa fa-user"></i><span style="padding-left:15px;">填报时间设置</span></b>
                    </div>
                    <div class="card-body">

                        <div class="form-group row">
                            <div class="col-sm-12">
                                <span id="spanHtml"> </span>
                                &nbsp;&nbsp;&nbsp;&nbsp;<a id="btnAdd" class="btn btn-info small" onclick="showTimeSlot();"><i class="fa fa-edit"></i>设置学校填报时间</a>
                            </div>
                        </div>
                        
                    </div>
                </div>             
            </form>
        </div>
    </div>
</div>


<script type="text/javascript">
    $(function () {
        getForm();
    });

    function getForm() {
        ys.ajax({
            url: '@Url.Content("~/EquipmentStatisticsManage/ReportDate/GetReportDate")',
            type: 'get',
            success: function (obj) {
                if (obj.Total > 0) {
                    var obj = obj.Data[0];
                    var html = "<span style='font-size:14px;'>填报开启时间段 “" + ys.formatDate(obj.StartDate, "yyyy-MM-dd") + "”至 “" + ys.formatDate(obj.EndDate, "yyyy-MM-dd") + "”</span>";
                    $("#spanHtml").html(html);
                } else {
                    $("#spanHtml").html("<span style='color:red;font-size:14px;'>暂未开启学校填报</span>");
                }
            }
        });
    }

    function showTimeSlot() {
        ys.openDialog({
            title: "填报时间设置",
            width: "800px",
            height: "550px",
            content: '@Url.Content("~/EquipmentStatisticsManage/ReportDate/ReportDateForm")',
            callback: function (index, layero) {
                var iframeWin = window[layero.find('iframe')[0]['name']];
                iframeWin.saveForm(index);
            }
        });
    }
</script>
