﻿@{
    Layout = "~/Views/Shared/_FormWhite.cshtml";
}
@inject Microsoft.AspNetCore.Hosting.IWebHostEnvironment HostingEnvironment

@section header{
    @BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/imageupload/1.0/css/imgup.min.css"))
    @BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/imageupload/1.0/js/imgup.min.js"))
    @BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/bootstrap.tagsinput/0.8.0/bootstrap-tagsinput.min.css"))
    @BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/bootstrap.tagsinput/0.8.0/bootstrap-tagsinput.min.js"))
    @BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/uploadifive/File.min.css"))
    @BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/uploadifive/File.min.js"))

}
<div class="wrapper animated fadeInRight">
    <form id="form" class="form-horizontal m">
        <input type="hidden" id="articleId" col="ArticleId" />
        <div class="form-group row">
            <label class="col-sm-3 control-label ">资讯图片</label>
            <div class="col-sm-7">
                <input id="imageSrc" col="ImageSrc" type="text" class="form-control" />
            </div>
            <div class="col-sm-2">
                <input type="file" name="uploadifyFileImageUrl" id="uploadifyFileImageUrl" />
                <div style="height:55px;display:none;" id="fileImgUrlFile"></div>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">资讯内容<font class="red">*</font></label>
            <div class="col-sm-8">
                <textarea id="remark" col="Remark"  class="form-control" style="height:100px;"></textarea>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">排序值<font class="red">*</font></label>
            <div class="col-sm-8">
                <input id="sort" col="Sort" type="text" class="form-control" />
            </div>
        </div>
    </form>
</div>

<script type="text/javascript">
    var id = ys.request("id");
    var articleId = ys.request("articleId");
    $(function () {
        getForm();

        $("#articleId").val(articleId);
         $("#uploadifyFileImageUrl").uploadifive({
            'uploadScript': '/File/UploadFile?fc=8101',
            'cancelImg': '~/lib/uploadifive/uploadifive-cancel.png',
            'buttonText': '上 传',
            'queueID': 'fileImgUrlFile',
            'auto': true,
            'multi': true,
            'fileDesc': '支持格式：*.jpg;*.gif;*.png;',
            'fileExt': '*.jpg;*.gif;*.png;',
            'onAddQueueItem': function (file) {

            },
            'onUploadComplete': function (fileObj, response) {
                if (typeof (response) == "string" && response != "") {
                    response = JSON.parse(response);
                    if (response.Tag == 1) {
                        ys.msgSuccess("上传成功！");
                        var filePath = response.Data;
                        $("#imageSrc").val(filePath);
                    } else {
                        ys.msgError(response.Message);
                    }
                }
            },
            'onFallback': function (event) {
                ys.msgError(event);
            }
        });

        $('#form').validate({
            rules: {
                remark: { required: true },
                sort: { required: true, digits: true, max: 9999 }
            }
        });
    });

    function getForm() {
        if (id > 0) {
            ys.ajax({
                url: '@Url.Content("~/ArticleManager/ArticleContext/GetFormJson")' + '?id=' + id,
                type: 'get',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        $('#form').setWebControls(obj.Data);
                    }
                }
            });
        }
        else {
            var defaultData = {};
            $('#form').setWebControls(defaultData);
        }
    }

    function saveForm(index) {
        if ($('#form').validate().form()) {
            var postData = $('#form').getWebControls({ Id: id });
            if (postData.Remark == "") {
                ys.msgError("请输入资讯内容");
                return;
            }
            ys.ajax({
                url: '@Url.Content("~/ArticleManager/ArticleContext/SaveFormJson")',
                type: 'post',
                data: postData,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        var html = "";
                        var htmlContent = "";
                        if (postData.ImageSrc != "") {
                            html = $.Format('<div class="row" id="div_{0}"><div class="col-sm-4"><a  modal="zoomImg" href="javascript:void(0);" src="{1}" ><img src="{1}" class="img-rounded" style="width:300px; height:200px;"/></a></div><div class="col-sm-6">{2}</div><div class="col-sm-2"><input class="btn btn-secondary" type="button" id="{0}" onclick="del(this)" value="删除" />&nbsp;&nbsp;<input class="btn btn-danger" type="button" id="{0}" onclick="edit(this)" value="修改" /></div></div >', obj.Data
                                , postData.ImageSrc, postData.Remark);
                            htmlContent = $.Format('<div class="col-sm-4"><a  modal="zoomImg" href="javascript:void(0);" src="{0}" ><img src="{0}" class="img-rounded" style="width:300px; height:200px;"/></a></div><div class="col-sm-6">{1}</div><div class="col-sm-2"><input class="btn btn-secondary" type="button" id="{2}" onclick="del(this)" value="删除" />&nbsp;&nbsp;<input class="btn btn-danger" type="button" id="{2}" onclick="edit(this)" value="修改" /></div></div >',
                                postData.ImageSrc, postData.Remark, obj.Data);
                        } else {
                            html = $.Format('<div class="row" id="div_{0}"><div class="col-sm-4"></div><div class="col-sm-6">{1}</div><div class="col-sm-6"><input class="btn btn-secondary" id="{0}" onclick="del(this)" type="button" value="删除" />&nbsp;&nbsp;<input class="btn btn-danger" id="{0}" onclick="edit(this)" type="button" value="修改" /></div></div >', obj.Data
                                , postData.Remark);
                            htmlContent = $.Format('<div class="col-sm-4"></div><div class="col-sm-6">{0}</div><div class="col-sm-6"><input class="btn btn-secondary" id="{1}" onclick="del(this)" type="button" value="删除" />&nbsp;&nbsp;<input class="btn btn-danger" id="{1}" onclick="edit(this)" type="button" value="修改" /></div></div >', 
                                postData.Remark, obj.Data);
                        }
                        if (id > 0) {
                            parent.$("#div_" + id).html(htmlContent);
                        } else {
                            parent.$("#divDetailList").append(html);
                        }
                        parent.imgages.showextalink();

                        parent.layer.close(index);
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        }
    }
</script>

