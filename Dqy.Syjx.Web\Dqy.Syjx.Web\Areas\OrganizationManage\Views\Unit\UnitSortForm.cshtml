﻿@{
    Layout = "~/Views/Shared/_FormWhite.cshtml";
}
<div class="wrapper animated fadeInRight">
    <form id="form" class="form-horizontal m">
        <div class="form-group row">
            <label class="col-sm-3 control-label ">单位代码</label>
            <div class="col-sm-9">
                <input id="Code" col="Code" type="text" class="form-control" readonly/>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">单位名称</label>
            <div class="col-sm-9">
                <input id="Name" col="Name" type="text" class="form-control" readonly/>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">社会统一信用代码</label>
            <div class="col-sm-9">
                <input id="OrganizationCode" col="OrganizationCode" type="text" class="form-control" readonly/>
            </div>
        </div>       
        <div class="form-group row">
            <label class="col-sm-3 control-label ">排序<font class="red">*</font></label>
            <div class="col-sm-9">
                <input id="Sort" col="Sort" type="text" class="form-control" />
            </div>
        </div>
    </form>
</div>

<script type="text/javascript">
    var id = ys.request("id");
    $(function () {
        getForm();
        $("#form").validate({
            rules: {
                Sort: { required: true }
            },
            messages: {
                Address: "请填写排序。",
            }
        });
    });

    function getForm() {
        if (id > 0) {
            ys.ajax({
                url: '@Url.Content("~/OrganizationManage/Unit/GetFormJson")' + '?id=' + id,
                type: 'get',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        $('#form').setWebControls(obj.Data);
                    }
                }
            });
        }
        else {
            var defaultData = {};
            $('#form').setWebControls(defaultData);
        }
    }

    function saveForm(index) {
        if ($('#form').validate().form()) {
            var postData = $('#form').getWebControls({ Id: id });
            ys.ajax({
                url: '@Url.Content("~/OrganizationManage/Unit/SaveSortFormJson")',
                type: 'post',
                data: { ids: id, sort: postData.Sort},
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        parent.$('#gridTable').ysTable('refresh');parent.resetToolbarStatus();
                        parent.layer.close(index);
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        }
    }
</script>

