﻿@{
    Layout = "~/Views/Shared/_FormWhite.cshtml";
}

<div class="wrapper animated fadeInRight">
    <form id="form" class="form-horizontal m">
        <div class="form-group row">
            <label class="col-sm-2 control-label ">状态<font class="red"> *</font></label>
            <div class="col-sm-6">
                <div id="Statuz" col="Statuz"></div>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-2 control-label ">学期<font class="red"> *</font></label>
            <div class="col-sm-6">
                <div id="SchoolTerm" col="SchoolTerm"></div>
            </div>
            <div class="col-sm-1"></div>
        </div>
        <div class="form-group row">
            <label class="col-sm-2 control-label ">学科<font class="red"> *</font></label>
            <div class="col-sm-6">
                <div id="CourseId" col="CourseId"></div>
            </div>
            <div class="col-sm-4 tag_msg_color">
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-2 control-label ">年级<font class="red"> *</font></label>
            <div class="col-sm-6">
                <div id="GradeId" col="GradeId"></div>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-2 control-label ">章节</label>
            <div class="col-sm-6">
                <input id="Chapter" col="Chapter" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-2 control-label ">实验代码</label>
            <div class="col-sm-6">
                <input id="ExperimentCode" col="ExperimentCode" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-2 control-label ">实验名称<font class="red"> *</font></label>
            <div class="col-sm-6">
                <input id="ExperimentName" col="ExperimentName" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-2 control-label ">实验类型<font class="red"> *</font></label>
            <div class="col-sm-6">
                <div id="experimentType" col="ExperimentType"></div>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-2 control-label ">实验要求<font class="red"> *</font></label>
            <div class="col-sm-6">
                <div id="isNeedDo" col="IsNeedDo"></div>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-2 control-label ">所需仪器</label>
            <div class="col-sm-6">
                <textarea id="equipmentNeed" style="max-width:100%" col="EquipmentNeed" type="text" class="form-control"></textarea>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-2 control-label ">所需耗材</label>
            <div class="col-sm-6">
                <textarea id="materialNeed" style="max-width:100%" col="MaterialNeed" type="text" class="form-control"></textarea>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-2 control-label ">备注</label>
            <div class="col-sm-6">
                <textarea id="Remark" style="max-width:100%" col="Remark" type="text" class="form-control"></textarea>
            </div>
        </div>
    </form>
</div>
<input type="hidden" id="hidCourseId" value="0" />
<input type="hidden" id="hidGradeId" value="0" />
<script type="text/javascript">
    var id = ys.request("id");
    var isview = ys.request("v");
    var paramSchoolStage = 0;
    $(function () {
        ysComboboxInit();
        
        $("#SchoolTerm").ysComboBox({
            data: ys.getJson(@Html.Raw(typeof(SchoolTermEnum).EnumToDictionaryString())),
            class: 'form-control'
        });
        loadExperimentType();
        loadIsNeedDo();
        loadStatuz();
        getForm();
        $('#form').validate({
            rules: {
                schoolId: { required: true }
            }
        });
    });
    //#region 初始化下拉加载

    function ysComboboxInit() {
        $('#GradeId').ysComboBox({
            key: 'DictionaryId',
            value: 'DicName',
            class: 'form-control'
        });
        $('#CourseId').ysComboBox({
            key: 'DictionaryId',
            value: 'DicName',
            class: 'form-control',
            onChange: function () {
                loadGrade();
            }
        });
    }


    //#endregion


    //#region 加载下拉基础数据
    function loadGrade() {
        var courseid = $('#CourseId').ysComboBox("getValue");
        if (!(courseid != undefined && parseInt(courseid) > 0)) {
            courseid = 0;
        }
        $('#GradeId').ysComboBox({
            //url: '@Url.Content("~/SystemManage/StaticDictionary/GetChildListJson")' + '?TypeCode=@DicTypeCodeEnum.Grade.ParseToInt()&Pid=' + schoolStage,
            url: '@Url.Content("~/ExperimentTeachManage/PlanInfo/GetGradeJson")' + "?OptType=8&TypeCode=1003&Pid=" + courseid,
            key: 'DictionaryId',
            value: 'DicName',
            class: 'form-control',
        });
        var defaultValue = $("#hidGradeId").val();
        if (defaultValue != undefined && parseInt(defaultValue) > 0) {
            $('#GradeId').ysComboBox('setValue', defaultValue);
        }
    }
    function loadCourse() {
        $('#CourseId').ysComboBox({
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetCourseListJson")',
            key: 'DictionaryId',
            value: 'DicName',
            class: 'form-control',
        });
        var defaultValue = $("#hidCourseId").val()
        if (defaultValue != undefined && parseInt(defaultValue) > 0) $('#CourseId').ysComboBox('setValue', defaultValue);
    }
    function loadExperimentType() {
        $("#experimentType").ysComboBox({ data: ys.getJson(@Html.Raw(typeof(ExperimentTypeEnum).EnumToDictionaryString())), class: 'form-control' });
    }
    function loadIsNeedDo() {
        $("#isNeedDo").ysComboBox({ data: ys.getJson(@Html.Raw(typeof(IsNeedEnum).EnumToDictionaryString())), class: 'form-control' });
    }
    function loadStatuz() {
        $("#Statuz").ysComboBox({ data: ys.getJson(@Html.Raw(typeof(StatusEnum).EnumToDictionaryString())), class: 'form-control' });
    }

    //#endregion
    function getForm() {
        if (id > 0) {
            ys.ajax({
                url: '@Url.Content("~/ExperimentTeachManage/SchoolExperiment/GetFormJson")' + '?id=' + id,
                type: 'get',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        $('#form').setWebControls(obj.Data);

                        $("#hidCourseId").val(obj.Data.CourseId);
                        $("#hidGradeId").val(obj.Data.GradeId);
                        loadCourse();
                        if (isview == 1) {
                            $("textarea,input").attr("disabled", "disabled");
                            parent.removeBtn();
                        }
                    }
                }
            });
        } else {
            loadCourse(); 
        }
    }

    function saveForm(index) {
        if ($('#form').validate().form()) {
            var postData = $('#form').getWebControls({ Id: id });
            //验证必填项。
            var errMsg='';
            if (!(postData.Statuz != undefined && postData.Statuz  >0)) {
                errMsg += '请选择实验状态.</br>';
            }
            if (!(postData.GradeId != undefined && postData.GradeId > 0)) {
                errMsg += '请选择年级.</br>';
            }
            if (!(postData.CourseId != undefined && postData.CourseId > 0)) {
                errMsg += '请选择学期.</br>';
            }
            if (!(postData.SchoolTerm != undefined && postData.SchoolTerm > 0)) {
                errMsg += '请选择学科.</br>';
            }
            if (!(postData.ExperimentName != undefined && postData.ExperimentName.length>1)){
                errMsg+='请填写实验名称，并且实验名称字符请控制在2至50.</br>';
            }
            if (!(postData.ExperimentType != undefined && postData.ExperimentType > 0)) {
                errMsg += '请选择实验类型。</br>';
            }
            if (!(postData.IsNeedDo != undefined && (postData.IsNeedDo == 2 || postData.IsNeedDo==1))) {
                errMsg += '请选择实验要求。</br>';
            }
            if (errMsg!='') {
                layer.msg(errMsg, { icon: 2, time: 3000, btn: ['关闭'], yes: function () { layer.closeAll(); }, area: ['400px'] });
                return false;
            }

            ys.ajax({
                url: '@Url.Content("~/ExperimentTeachManage/SchoolExperiment/SaveFormJson")',
                type: 'post',
                data: postData,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        parent.searchGrid();
                        parent.layer.close(index);
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        }
    }
</script>

