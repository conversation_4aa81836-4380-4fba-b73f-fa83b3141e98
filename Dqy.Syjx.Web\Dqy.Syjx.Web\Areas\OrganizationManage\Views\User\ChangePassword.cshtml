﻿@{
    Layout = "~/Views/Shared/_FormWhite.cshtml";
}
<div class="wrapper animated fadeInRight">
    <form id="form" class="form-horizontal m">
        <div class="form-group row">
            <label class="col-sm-3 control-label ">用户名</label>
            <div class="col-sm-8">
                <input id="userName" type="text" class="form-control" readonly="readonly" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">旧密码<font class="red"> *</font></label>
            <div class="col-sm-8">
                <input id="password" col="Password" type="password" autocomplete="new-password" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">新密码<font class="red"> *</font></label>
            <div class="col-sm-8">
                <input id="newPassword" col="NewPassword" type="password" autocomplete="new-password" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">新密码确认<font class="red"> *</font></label>
            <div class="col-sm-8">
                <input id="confirmPassword" col="ConfirmPassword" type="password" autocomplete="new-password" class="form-control" />
            </div>
        </div>
    </form>
</div>

<script type="text/javascript">
    var id = ys.request("id");

    $(function () {
        $("#userName").val("@ViewBag.OperatorInfo.UserName");
        $("#password").val("");

        $("#form").validate({
            rules: {
                password: {
                    required: true
                },
                newPassword: {
                    required: true,
                    minlength: 8,
                    maxlength: 20,
                    isLoginpass: true
                },
                confirmPassword: {
                    required: true,
                    equalTo: "#newPassword"
                }
            },
            messages: {
                password: {
                    required: "请输入原密码"
                },
                newPassword: {
                    required: "请输入新密码",
                    minlength: "密码不能小于8个字符",
                    maxlength: "密码不能大于20个字符"
                },
                confirmPassword: {
                    required: "请再次输入新密码",
                    equalTo: "两次密码输入不一致"
                }
            }
        });
    });

    function saveForm(index) {
        if ($("#form").validate().form()) {
            var postData = $("#form").getWebControls({ Id: id });
            ys.ajax({
                url: '@Url.Content("~/OrganizationManage/User/ChangePasswordJson")',
                type: "post",
                data: postData,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        parent.layer.close(index);
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        }
    }
</script>

