﻿@{
    Layout = "~/Views/Shared/_Index.cshtml";
}
<style type="text/css">
    .check-box {
        width: 100px;
    }

    .iradio-box {
        width: 100px;
    }

    .iradio-box {
        position: initial;
        display: inline-block;
    }

    .iradio-blue {
        position: inherit;
        display: inline-block;
        /*  top: 6px;*/
    }

    .tag_msg_color {
        color: #999;
    }

    .select2-container {
        display: block;
        width: auto !important;
    }
    .div-experit {
        border: 1px dashed #999;
        margin: 15px 0px 15px 0px;
        padding-top: 15px;
    }
</style>
<div class="container-div">
    <div class="row" style="height:auto;">
        <div class="ibox float-e-margins border-bottom" style="margin-bottom:0px;">
            <div class="ibox-title">
                <h5 class="table-tswz">友情提示</h5>
                <div class="ibox-tools">
                    <a class="collapse-link">
                        <i class="fa fa-chevron-down"></i>
                    </a>
                </div>
            </div>
            <div class="ibox-content" style="padding:0px;display:none;">
                <div class="row">
                    <div class="col-sm-12">
                        <div class="card-body table-tswz">
                            注：如还未更新本学期“个人任课信息”，请点击【 <a class="btn btn-secondary btn-sm" onclick="updateClassInfo()"><i class="fa fa-default"></i>&nbsp;设置&nbsp;</a>】按钮，这将便捷你以下信息的填写！
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="row">

        <div class="col-sm-12 select-table table-striped">
            <div class="ibox-title">
                <h5>实验预约</h5>
            </div>
            <form id="form" class="form-horizontal m">
                <div class="form-group row">
                    <label class="col-sm-2 control-label ">预约人<font class="red"> *</font></label>
                    <div class="col-sm-6">
                        <div id="divUseUserName" style="border: 0px solid #e5e6e7;" class="form-control"></div>
                    </div>
                </div>
                <div class="form-group row">
                    <label class="col-sm-2 control-label ">学段<font class="red"> *</font></label>
                    <div class="col-sm-6">
                        <div id="SchoolStage" col="SchoolStage"></div>
                    </div>
                    <div class="col-sm-3">
                    </div>
                </div>
                <div class="form-group row">
                    <label class="col-sm-2 control-label ">学科<font class="red"> *</font></label>
                    <div class="col-sm-6">
                        <div id="CourseId" col="CourseId"></div>
                    </div>
                    <div class="col-sm-3 tag_msg_color">
                        新学年需要更新任课信息【 <a class="btn btn-secondary btn-sm btn-xs" onclick="updateClassInfo()"><i class="fa fa-default"></i>&nbsp;设置&nbsp;</a>】
                    </div>
                </div>
                <div class="form-group row">
                    <label class="col-sm-2 control-label ">上课班级<font class="red"> *</font></label>
                    <div class="col-sm-6">
                        <div id="SchoolGradeClassIdz" col="SchoolGradeClassIdz"></div>
                    </div>
                    <div class="col-sm-3 tag_msg_color">禁止跨年级</div>
                </div>
                <div class="form-group row">
                    <label class="col-sm-2 control-label ">上课时间<font class="red"> *</font></label>
                    <div class="col-sm-6">
                        <input id="ClassTime" col="ClassTime" type="text" readonly class="form-control" />
                    </div>
                    <div class="col-sm-3 tag_msg_color">只能选择本学期内的时间</div>
                </div>
                <div class="form-group row">
                    <label class="col-sm-2 control-label ">上课节次<font class="red"> *</font></label>
                    <div class="col-sm-6">
                        <div id="SectionId" col="SectionId"></div>
                    </div>

                </div>
                <div class="form-group row">
                    <label class="col-sm-2 control-label ">上课地点<font class="red"> *</font></label>
                    <div class="col-sm-6">
                        <div id="FunRoomId" col="FunRoomId"></div>
                    </div>
                    <div class="col-sm-3 tag_msg_color">只包含中学理化生和小学科学实验室</div>
                </div>
                <div class="form-group row" id="divArrangerId" style="display:none;">
                    <label class="col-sm-2 control-label ">安排人<font class="red"> *</font></label>
                    <div class="col-sm-6">
                        <div id="ArrangerId" col="ArrangerId"></div>
                    </div>
                    <div class="col-sm-3 tag_msg_color"></div>
                </div>
                <div class="form-group row">
                    <label class="col-sm-2 control-label ">实验来源<font class="red"> *</font></label>
                    <div class="col-sm-3">
                        <div id="SourceType" col="SourceType" style="padding-top:7px;"></div>
                    </div>
                    <div class="col-sm-3">
                        <a class="btn btn-success" onclick="showExperimentForm()"><i class="fa fa-plus"></i> 选择添加实验</a>
                    </div>
                    <div class="col-sm-3 tag_msg_color">如有实验计划，优先从实验计划中选择</div>
                </div>

                <div id="divExperimentBody">


                </div> 
                <div class="form-group row">
                    <label class="col-sm-2 control-label " id="labRemark">预约说明</label>
                    <div class="col-sm-6">
                        <textarea id="Remark" col="Remark" class="form-control"></textarea>
                    </div>
                    <div class="col-sm-3 tag_msg_color">
                    </div>
                </div>
            </form>
            <div class="btn-group-sm hidden-xs col-sm-8" id="toolbar" style="text-align: center;">
                <a id="btnAdd" class="btn btn-info" onclick="saveForm();"><i class="fa fa-edit"></i> 提 交</a>
                <span style="margin-left:20px;color:#999;">下一步转交实验员安排实验</span>
            </div>
        </div>
    </div>
</div>
<input type="hidden" id="hidClassId" value="" />
<input type="hidden" id="hidCourseId" value="" />
<input type="hidden" id="hidFunRoomId" value="" />
<input type="hidden" id="hidExperimentName" value="" />
<input type="hidden" id="hidPlanDetailId" value="" />
<input type="hidden" id="hidTextbookVersionDetailId" value="" />
<input type="hidden" id="hidArrangerId" value="" />
<input type="hidden" id="hidSectionId" value="" />
<script type="text/javascript">
    var id = ys.request("id");
    var taskid = ys.request("taskid");
    var taskgradeclassid = ys.request("taskgradeclassid");
    var IsAdminInfo;
    //记录改变前的值，避免触发事件中的其他调用加载多次
    var paramSchoolStage = 0;
    var paramCourseId = 0;
    var paramGradeClassId = 0;
    var IsLoadEnd = false;
    var SaveValiDate = [];
    var IsRequiredEquipmentNeed = 0;
    var IsRequiredMaterialNeed = 0;
    var VerifyResult = false;
    var VerifyMsg = '';
    $(function () {
        if (!(parseFloat(id) > 0)) {
            id = 0;
        }
        initysComboBox();
        loadIsAdminInfo();
        loadSchoolStage();
        loadSection();
        loadClass(0);
        loadCourse(0);
        loadClassTime();
        loadSourceType();
        loadFunRoom(0);
        if (id == 0 && parseInt(taskid) > 0) {
            //加载
            loadTaskInfo();
        } else {
            getForm();
        }
        loadPageSetValid();
        /*$('#form').validate({
            rules: {
                ClassTime: { required: true },
            }
        });*/
    });

    function showTaskForm() {
        var url = '@Url.Content("~/ExperimentTeachManage/ExperimentBooking/TaskList")';
        createMenuItem(url, "实验任务");
    }

    function clearExperimentSelect() {
        $("#ExperimentName").val("");
        $("#PlanDetailId").val(0);
        $("#TextbookVersionDetailId").val(0);
    }
    function initysComboBox() {
        $('#SectionId').ysComboBox({
            onChange: function () {
                verifyForm();
            }
        });
        $('#FunRoomId').ysComboBox({
            onChange: function () {
                var selectid = $('#FunRoomId').ysComboBox('getValue');
                $("#hidFunRoomId").val(selectid);
                if (selectid == 1) {
                    $("#divArrangerId").show();
                } else {
                    $("#divArrangerId").hide();
                }
                verifyForm();
            }
        });
        $('#ArrangerId').ysComboBox({});
        $('#SchoolGradeClassIdz').ysComboBox({
            multiple: 'true',
            onChange: function (data, node, e) {
                verifyForm();
                //获取当前班级的年级Id
                var classids = $('#SchoolGradeClassIdz').ysComboBox('getValue');
                if (classids!=undefined && classids.length >0) {
                    paramGradeClassId = ys.getLastValue(classids);
                }
            }
        });
    }
    function loadSchoolStage() {
        ys.ajax({
            url: '@Url.Content("~/BusinessManage/FunRoomUse/GetSchoolStageJson")',
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1) {
                    loadSchoolStageData(obj.Data);
                } else {
                    loadSchoolStageData([]);
                    ys.msgError(obj.Message);
                }
            }
        });
    }
    function loadSchoolStageData(data) {
        $('#SchoolStage').ysComboBox({
            data: data,
            class: 'form-control',
            key: 'DictionaryId',
            value: 'DicName',
            onChange: function () {
                var selectid = $('#SchoolStage').ysComboBox('getValue');
                if (selectid != paramSchoolStage) {
                    paramSchoolStage = selectid;
                    clearExperimentSelect();
                    loadCourse(selectid);
                    loadClass(selectid);
                }
            }
        });
        if (data != undefined && data.length == 1) {
            $("#SchoolStage").ysComboBox('setValue', data[0].DictionaryId);
        }
    }
    function loadClass(schoolstage) {
        if (schoolstage > 0) {
            ys.ajax({
                url: '@Url.Content("~/ExperimentTeachManage/ExperimentBooking/GetUserClassListJson")' + '?schoolstage=' + schoolstage,
                type: 'get',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        loadClassData(obj.Data);
                    } else {
                        loadClassData({});
                    }
                }
            });
        } else {
            loadClassData({});
        }
    }
    function loadClassData(data) {
         $('#SchoolGradeClassIdz').ysComboBox({
             data: data,
             class: 'form-control',
             key: 'Id',
             value: 'ClassName',
             multiple: 'true',
            defaultName: '请选择'
         });
        var classid = $("#hidClassId").val();
        if (data != undefined && data.length > 0 && parseInt(classid) > 0) {
            $('#SchoolGradeClassIdz').ysComboBox('setValue', classid);
        } else if (data != undefined && data.length == 1) {
            $('#SchoolGradeClassIdz').ysComboBox('setValue', data[0].Id);
        }

        //只能选择学段后再验证
        var schoolstage = $('#SchoolStage').ysComboBox('getValue');
        if (schoolstage > 0 && (data == undefined || data.length == 0)) {
            if (IsAdminInfo.IsAdmin == true) {
                layer.open({
                    title: '未配置',
                    content: '单位还未创建班级信息。<br />请去【系统设置】- 【<a class="btn btn-primary btn-sm" href="javascript:void(0);" onclick="updateGradeClass();">年级班级信息</a>】先设置年级班级。',
                });
                return;
            } else {
                var adminName = "";
                if (IsAdminInfo.AdminName != undefined && IsAdminInfo.AdminName.length > 0) {
                    adminName = ("【" + IsAdminInfo.AdminName + "】");
                }
                layer.open({
                    title: '未配置',
                    content: '单位还未创建班级信息。请去联系管理员' + adminName + '设置年级班级。',
                });
                return;
            }
        }
    }
    function loadCourse(schoolstage) {
        if (schoolstage > 0) {
            ys.ajax({
                url: '@Url.Content("~/ExperimentTeachManage/ExperimentBooking/GetUserCourseListJson")' + '?schoolstage=' + schoolstage,
                type: 'get',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        bindCourseData(obj.Data);
                    } else {
                        bindCourseData([]);
                    }
                }
            });
        } else {
            bindCourseData([]);
        }
    }
    function bindCourseData(data) {
        $('#CourseId').ysComboBox({
            data: data,
            class: 'form-control',
            key: 'DictionaryId',
            value: 'DicName',
            onChange: function () {
                var selectid = $('#CourseId').ysComboBox('getValue');
                if (selectid != paramCourseId) {
                    paramCourseId = selectid;
                    clearExperimentSelect();
                    loadFunRoom(selectid);
                    loadArrangerId();
                    loadGradeClassByCourse(selectid);
                }
            }
        });
        var courseid = $("#hidCourseId").val();
        if (parseInt(courseid) > 0 && data != undefined && data.length > 0) {
            $('#CourseId').ysComboBox('setValue', courseid);

            //重新给清空的，名称和Id赋值。
            $("#ExperimentName").val($("#hidExperimentName").val());
            $("#PlanDetailId").val($("#hidPlanDetailId").val());
            $("#TextbookVersionDetailId").val($("#hidTextbookVersionDetailId").val());
        } else if (data != undefined && data.length == 1) {
            $('#CourseId').ysComboBox('setValue', data[0].DictionaryId);
        }
    }

    //根据学科加载班级
    function loadGradeClassByCourse(couseid) {
        if (couseid > 0) {
            ys.ajax({
                url: '@Url.Content("~/ExperimentTeachManage/ExperimentBooking/GetGradeClassByCourseJson")' + '?courseid=' + couseid,
                type: 'get',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        loadClassData(obj.Data);
                    }
                }
            });
        }
    }

    function loadClassTime() {
        ys.ajax({
            url: '@Url.Content("~/OrganizationManage/SchoolTerm/GetCurrentFormJson")' +'?modetype=1',
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1) {
                    laydate.render({
                        elem: '#ClassTime',
                        format: 'yyyy-MM-dd',
                        min: obj.Data.TermStart,
                        max: obj.Data.TermEnd,
                        position: 'fixed',
                        trigger: 'click',
                        done: function (value, date) { //监听日期被切换
                            verifyForm();
                        }
                    });
                } else {
                    ys.alertWarning("当前日期不在平台配置的学期内，禁止登记。");
                    return;
                }
            }
        });
    }
    function loadSourceType() {
         ys.ajax({
            url: '@Url.Content("~/ExperimentTeachManage/ExperimentBooking/GetIsOpenExperiment")',
            type: 'get',
             success: function (obj) {
                 var html = '';
                 if (obj.Tag == 1 && obj.Data == 1) {
                     html += '<label class="iradio-box"><div class="iradio-blue single" style="vertical-align:bottom;"><input name="inputType_checkbox" type="radio" value="1" style="position: absolute; opacity: 0;"></div> 实验计划</label>';
                     html += '<label class="iradio-box"><div class="iradio-blue batch" style="vertical-align:bottom;"><input name="inputType_checkbox" type="radio" value="2" style="position: absolute; opacity: 0;"></div> 实验目录</label>';
                 } else {
                     html += '<label class="iradio-box"><div class="iradio-blue single checked" style="vertical-align:bottom;"><input name="inputType_checkbox" type="radio" value="1" checked="checked" style="position: absolute; opacity: 0;"></div> 实验计划</label>';
                 }
                 $("#SourceType").html(html);
                 $('input[name="inputType_checkbox"]').change(function () {
                     if ($(this).prop("checked")) {
                         var val1 = $(this).val();
                         sourcetypeChange(val1);
                     }
                 });
             }
        });
    }
    function sourcetypeChange(selectid) {
        if (selectid == 1) {
            $(".single").addClass("checked");
            $(".batch").removeClass("checked");
       /*     $('#divRemark').hide();*/
        } else {
            $(".batch").addClass("checked");
            $(".single").removeClass("checked");
         /*   $('#divRemark').show();*/
        }
    }
    function showExperimentForm() {
        var sourceType = $('[name="inputType_checkbox"]:checked').val();
        var classid = $('#SchoolGradeClassIdz').ysComboBox('getValue');
        var courseid = $('#CourseId').ysComboBox('getValue');
        var sectionid = $('#SectionId').ysComboBox('getValue');
        if (!(classid != undefined && classid.length > 0 && parseFloat(courseid) > 0) || (sourceType != 1 && sourceType != 2)) {
            ys.msgError("请顺序操作。先选择上课班级、选择学科、上课时间和实验来源再选择实验名称。");
            return;
        }
        var classTime = $("#ClassTime").val();
        if (!(classTime != null && classTime.length > 0)) {
            ys.msgError("请顺序选择操作。你上课时间未选择。");
            return;
        }
        var title = '选择实验计划';
        var contentUrl = '@Url.Content("~/ExperimentTeachManage/ExperimentBooking/PlanList")' + '?classidz=' + classid + '&courseid=' + courseid + '&classtime=' + classTime + '&sectionid=' + sectionid;
        if (sourceType == 2) {
            title = '选择实验目录';
            contentUrl = '@Url.Content("~/ExperimentTeachManage/ExperimentBooking/CatalogList")' + '?classidz=' + classid + '&courseid=' + courseid + '&classtime=' + classTime + '&sectionid=' + sectionid;
        }
         ys.openDialog({
             title: title,
             content: contentUrl,//+ '?id=' + id,
             width: '988px',
            /* height: "200px",*/
            callback: function (index, layero) {
                var iframeWin = window[layero.find('iframe')[0]['name']];
                iframeWin.saveForm(index);
            }
        });
    }
    function loadSection() {
        ys.ajax({
            url: '@Url.Content("~/OrganizationManage/CourseSection/GetListJson")',
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1 && obj.Data.length > 0) {
                    $('#SectionId').ysComboBox({
                        data: obj.Data,
                        class: 'form-control',
                        key: 'Id',
                        value: 'SectionName',
                    });
                    if (obj.Data != undefined && obj.Data.length == 1) {
                        $('#SectionId').ysComboBox('setValue', obj.Data[0].Id);
                    }
                    var hidSectionId = $("#hidSectionId").val();
                    if (parseInt(hidSectionId) > 0) {
                        if (obj.Data != undefined) {
                            $.map(obj.Data, function (item) {
                                if (item.Id == hidSectionId) {
                                    $('#SectionId').ysComboBox('setValue', hidSectionId);
                                }
                            })
                        }
                    }
                } else {
                    if (IsAdminInfo.IsAdmin == true) {
                        layer.open({
                            title: '未配置',
                            content: '单位还未设置课程节次表。<br />请去【系统设置】- 【<a class="btn btn-primary btn-sm" href="javascript:void(0);" onclick="updateCourseSection();">课程节次表</a>】先设置课程节次。',
                        });
                        return;
                    } else {
                        var adminName = "";
                        if (IsAdminInfo.AdminName != undefined && IsAdminInfo.AdminName.length > 0) {
                            adminName = ("【" + IsAdminInfo.AdminName + "】");
                        }
                        layer.open({
                            title: '未配置',
                            content: '单位还未设置课程节次表。请去联系管理员' + adminName + '设置课程节次。',
                        });
                        return;
                    }
                }
            }
        });
    }
    function loadFunRoom(courseid) {
        if (courseid > 0) {
            var schoolstage = $('#SchoolStage').ysComboBox('getValue');
            ys.ajax({
                url: '@Url.Content("~/ExperimentTeachManage/ExperimentBooking/GetFunRoomListJson")' + '?schoolstage=' + schoolstage + '&courseid=' + courseid,
                type: 'get',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        bindFunRoomData(obj.Data);
                    } else {
                        bindFunRoomData([]);
                    }
                }
            });
        } else {
              bindFunRoomData([]);
        }
    }
    function bindFunRoomData(data) {
        data.unshift({ Id: 1, Name: '普通教室' });
        $('#FunRoomId').ysComboBox({
            data: data,
            class: 'form-control',
            key: 'Id',
            value: 'Name'
        });
        var funroomid = $("#hidFunRoomId").val();
        if (parseInt(funroomid) > 0 && data != undefined && data.length > 0) {
            $('#FunRoomId').ysComboBox('setValue', funroomid);
        }
    }
    function loadArrangerId() {
        var schoolstage = $('#SchoolStage').ysComboBox('getValue');
        var courseid = $('#CourseId').ysComboBox('getValue');
        if (parseInt(schoolstage) > 0 && parseInt(courseid) > 0) {
            ys.ajax({
                url: '@Url.Content("~/ExperimentTeachManage/ExperimentBooking/GetArrangerListJson")' + '?schoolstage=' + schoolstage + '&courseid=' + courseid,
                type: 'get',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        loadArrangerData(obj.Data);
                    } else {
                        loadArrangerData([]);
                    }
                }
            });
        } else {
            loadArrangerData([]);
        }
    }
    function loadArrangerData(data) {
        $('#ArrangerId').ysComboBox({
            data: data,
            class: 'form-control',
            key: 'UserId',
            value: 'RealName'
        });
        var arrangerid = $("#hidArrangerId").val();
        if (parseInt(arrangerid) > 0 && data != undefined && data.length > 0) {
            $('#ArrangerId').ysComboBox('setValue', arrangerid);
        }
    }
    function getForm() {
        ys.ajax({
            url: '@Url.Content("~/ExperimentTeachManage/ExperimentBooking/GetFormJson")' + '?id=' + id,
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1) {
                    if (id > 0) {
                        var firstData = obj.Data;
                        if (firstData.SourceType >= 2) {
                            firstData.SourceType = 2;
                        }
                        $('#form').setWebControls(firstData);
                        if (isNaN(firstData.ClassTime) && firstData.ClassTime.length >= 10) {
                            $("#ClassTime").val(firstData.ClassTime.substring(0, 10));
                        }
                        $("#divUseUserName").text(firstData.RealName);
                        $("#hidClassId").val(firstData.SchoolGradeClassIdz);
                        $("#hidCourseId").val(firstData.CourseId);
                        $("#hidFunRoomId").val(firstData.FunRoomId);
                        $("#hidSectionId").val(firstData.SectionId);

                        loadChilderExperiment(firstData.Id);

                        if (firstData.FunRoomId == 1) {
                            $("#divArrangerId").show();
                            $("#hidArrangerId").val(firstData.ArrangerId);
                        }
                    } else {
                        $("#divUseUserName").text(obj.Data.RealName);
                    }
                    IsLoadEnd = true;
                }
            }
        });
    }
    function loadTaskInfo() {
        ys.ajax({
            url: '@Url.Content("~/ExperimentTeachManage/ExperimentBooking/GetTaskFormJson")' + '?taskid=' + taskid + '&gradeclassid=' + taskgradeclassid,
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1) {
                    var firstData = obj.Data;
                    if (firstData.SourceType >= 2) {
                        firstData.SourceType = 2;
                    }
                    $('#form').setWebControls(firstData);
                    $("#ClassTime").val('');
                    $("#divUseUserName").text(firstData.RealName);
                    $("#hidCourseId").val(firstData.CourseId);
                    $("#hidClassId").val(firstData.SchoolGradeClassIdz);
                    //$("#hidFunRoomId").val(firstData.FunRoomId);
                    //$("#hidSectionId").val(firstData.SectionId);
                    //if (isNaN(firstData.ClassTime) && firstData.ClassTime.length >= 10) {
                    //    $("#ClassTime").val(firstData.ClassTime.substring(0, 10));
                    //}
                    var tempid = firstData.PlanDetailId;
                    if (firstData.SourcePath == 2 && firstData.SourceType == 2) {
                        tempid = firstData.SchoolExperimentId;
                    } else if (firstData.SourcePath == 1 && firstData.SourceType == 2) {
                        tempid = firstData.TextbookVersionDetailId;
                    }
                    setExperimentData(tempid, firstData.ExperimentName, 0, firstData.ExperimentType, firstData.Groupz, firstData.EquipmentNeed, firstData.MaterialNeed, firstData.SourcePath, firstData.SourceType);
                    if (firstData.FunRoomId == 1) {
                        $("#divArrangerId").show();
                        $("#hidArrangerId").val(firstData.ArrangerId);
                    }
                    $("#divUseUserName").text(obj.Data.RealName);
                    IsLoadEnd = true;
                }
            }
        });
    }
    function loadChilderExperiment(pid) {
        ys.ajax({
            url: '@Url.Content("~/ExperimentTeachManage/ExperimentBooking/GetListJson")' + '?pid=' + pid,
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1) {
                    if (obj.Data.length > 0) {
                        for (var i = 0; i < obj.Data.length; i++) {
                            var experimentInfo = obj.Data[i];
                            var experimentid = experimentInfo.TextbookVersionDetailId;
                            if (experimentInfo.SourceType == 1) {
                                //实验计划
                                experimentid = experimentInfo.PlanDetailId;
                            } else {
                                if (experimentInfo.SourcePath == 2) {
                                    experimentid = experimentInfo.SchoolExperimentId;
                                }
                            }
                            setExperimentData(
                                experimentid,
                                experimentInfo.ExperimentName,
                                experimentInfo.Id,
                                experimentInfo.ExperimentType,
                                experimentInfo.Groupz,
                                experimentInfo.EquipmentNeed,
                                experimentInfo.MaterialNeed,
                                experimentInfo.SourcePath,
                                experimentInfo.SourceType);
                        }
                    }
                }
            }
        });
    }
    function showGroupz(etype) {
        if ( @ExperimentTypeEnum.Group.ParseToInt()== etype) {
            $("#ExperimentType").text('@ExperimentTypeEnum.Group.GetDescription()');
            $("#divGroupz").show();
        } else {
            $("#ExperimentType").text('@ExperimentTypeEnum.Demo.GetDescription()');
            $("#divGroupz").hide();
        }
    }
    function saveForm(index) {
        var postData = $('#form').getWebControls({ Id: id });
        var errorMsg = '';
        if (!(parseInt(postData.SchoolStage) > 0)) {
            errorMsg += '请选择学段。<br/>';
        }
        if (!(postData.SchoolGradeClassIdz != undefined && postData.SchoolGradeClassIdz.length > 0)) {
            errorMsg += '请选择上课班级。<br/>';
        }
        if (!(parseInt(postData.CourseId) > 0)) {
            errorMsg += '请选择学科。<br/>';
        }
        if (!(postData.ClassTime != undefined && postData.ClassTime.length > 0)) {
            errorMsg += '请选择上课时间。<br/>';
        }
        if (!(parseInt(postData.SectionId) > 0)) {
            errorMsg += '请选择上课节次。<br/>';
        }
        if (!(parseInt(postData.FunRoomId) > 0)) {
            errorMsg += '请选择上课地点。<br/>';
        } else if (parseInt(postData.FunRoomId) == 1){
            if (!(parseInt(postData.ArrangerId) > 0)) {
                errorMsg += '请选择安排人。<br/>';
            }
        }

        var expertimentArr = [];
        //只有提交保存，和定时保存的时候才会保存所有
        $(".div-experit").map(function (index, item) {
            var id = $(this).attr("tagid");
            var bookingid = $(this).attr("bookingid");
            var txtSourceType = $("#SourceType_" + id).val();
            var txtSourcePath = $("#SourcePath_" + id).val();
            var txtExperimentName = $("#ExperimentName_" + id).val();
            var txtExperimentType = $("#ExperimentType_" + id).val();
            var txtGroupz = $("#Groupz_" + id).val();
            var txtEquipmentNeed = $("#EquipmentNeed_" + id).val();
            var txtMaterialNeed = $("#MaterialNeed_" + id).val();
            if (txtExperimentType == '@ExperimentTypeEnum.Group.ParseToInt()') {
                if (!(txtGroupz != undefined && parseInt(txtGroupz) > 0)) {
                    errorMsg += ('分组实验，请填写分组数。<br/>');
                }
            }
            if (SaveValiDate != undefined && SaveValiDate.length > 0) {
                SaveValiDate.map(function (item, i) {
                    if (item.typecode == "EquipmentNeed") {
                        if (!(txtEquipmentNeed != undefined && txtEquipmentNeed.length > 0)) {
                            errorMsg += (item.verifyhint + '。<br/>');
                        }
                    } else if (item.typecode == "MaterialNeed") {
                        if (!(txtMaterialNeed != undefined && txtMaterialNeed.length > 0)) {
                            errorMsg += (item.verifyhint + '。<br/>');
                        }
                    }
                });
            }
            if (!(txtSourcePath != undefined && parseInt(txtSourcePath) > 0)) {
                txtSourcePath = 0;
            }
            if (!(txtSourceType != undefined && parseInt(txtSourceType) > 0)) {
                txtSourceType = 0;
            }
            expertimentArr.push({
                Id: id,
                BookingId: bookingid,
                ExperimentName: txtExperimentName,
                ExperimentType: txtExperimentType,
                Groupz: txtGroupz,
                EquipmentNeed: txtEquipmentNeed,
                MaterialNeed: txtMaterialNeed,
                SourcePath: txtSourcePath,
                SourceType:txtSourceType
            });
        });
        if (expertimentArr.length==0) {
            errorMsg += ('请选择需要预约的实验。<br/>');
        }
        //验证预约说明
        if (SaveValiDate != undefined && SaveValiDate.length > 0) {
            SaveValiDate.map(function (item, i) {
                if (item.typecode == "Remark") {
                    if (!(postData.Remark != undefined && postData.Remark.length > 0)) {
                        errorMsg += (item.verifyhint + '。<br/>');
                    }
                }
            });
        }
        if (errorMsg != '') {
            ys.msgError('验证失败，请填写完成再提交！<br/>' + errorMsg);
            return;
        }
        postData.ExperimentList = expertimentArr;
       ys.confirm('您确认提交预约吗？', function () {
            ys.ajax({
                url: '@Url.Content("~/ExperimentTeachManage/ExperimentBooking/SaveFormJson")',
                type: 'post',
                data: postData,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        var url = '@Url.Content("~/ExperimentTeachManage/ExperimentBooking/Listed")';
                        createMenuAndCloseCurrent(url, "已预约实验");
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        });
    }

    /*获取数据库配置的填报验证*/
    function loadPageSetValid() {
        ys.ajax({
            url: '@Url.Content("~/SystemManage/ConfigSet/GetListByCode")',
            type: 'Post',
            data: { typecode: '3SYJX-1SYYY-1YYTB' },
            async: false,
            success: function (obj) {
                if (obj.Tag == 1) {
                    if (obj.Data != undefined && obj.Data.length > 0) {
                        for (var i = 0; i < obj.Data.length; i++) {
                            if (obj.Data[i].ConfigValue == "1") {
                                var typecode = obj.Data[i].TypeCode.replaceAll("3SYJX-1SYYY-1YYTB-", "");

                                var verifyhint = "";
                                if (obj.Data[i].VerifyHint != undefined && obj.Data[i].VerifyHint.length > 0) {
                                    verifyhint = obj.Data[i].VerifyHint;
                                }
                                $("#lab" + typecode).append('<font class="red"> *</font>');

                                if (typecode == "EquipmentNeed") {
                                    IsRequiredEquipmentNeed = 1;
                                    SaveValiDate.push({ typecode: typecode, verifyhint: verifyhint });
                                } else if (typecode == "MaterialNeed") {
                                    IsRequiredMaterialNeed = 1;
                                    SaveValiDate.push({ typecode: typecode, verifyhint: verifyhint });
                                } else if (typecode == "Remark") {
                                    SaveValiDate.push({ typecode: typecode, verifyhint: verifyhint });
                                }
                            }
                        }
                    }
                }
            }
        });
    }
    //#region 配置辅助连接和验证
    function updateClassInfo() {
        var url = '@Url.Content("~/PersonManage/UserClassInfo/UserClassInfoIndex")';
        createMenuItem(url, "个人任课信息");
    }
    function updateCourseSection() {
        var url = '@Url.Content("~/OrganizationManage/CourseSection/CourseSectionIndex")';
        createMenuItem(url, "课程节次表");
    }
    function updateGradeClass() {
        var url = '@Url.Content("~/OrganizationManage/SchoolGradeClass/SchoolGradeClassIndex")';
        createMenuItem(url, "年级班级信息");
    }
    function loadIsAdminInfo() {
        ys.ajax({
            url: '@Url.Content("~/OrganizationManage/User/GetIsAdminJson")',
            async: false,
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1) {
                    IsAdminInfo = obj.Data;
                } else {
                    ys.msgError(obj.Message);
                    return undefined;
                }
            }
        });

    }
    //#endregion
    //删除附件
    function delFile(obj, value) {
        $(obj).parent().remove();
        imgages.showextalink();
    }
     /*事件：学科、班级、时间、节次、地点、实验
     1：同一个班级，一个实验只能预约登记一次
     2：同一个班级，同一时间（包含节次）只能有一个预约登记
     3：同一个实验室地址，同一时间只能（包含节次）只能有一个预约登记
     4：同一个实验，同一时间只能（包含节次）只能有一个预约登记（这个验证应该不存在）
     */
    function verifyForm() {
        var postData = $('#form').getWebControls({ Id: id });
        if (!(parseInt(postData.SchoolStage) > 0)) {
            postData.SchoolStage = 0;
        }
        if (!(postData.SchoolGradeClassIdz != undefined && postData.SchoolGradeClassIdz.length > 0)) {
            postData.SchoolGradeClassIdz = '';
        }
        if (!(parseInt(postData.CourseId) > 0)) {
            postData.CourseId = 0;
        }
        if (!(postData.ClassTime != undefined && postData.ClassTime.length > 0)) {
            postData.ClassTime = null;
        }
        if (!(parseInt(postData.SectionId) > 0)) {
            postData.SectionId = 0;
        }
        if (!(parseInt(postData.FunRoomId) > 0)) {
            postData.FunRoomId = 0;
        }
        if (!(parseInt(postData.SourceType) > 0)) {
            postData.SourceType = 0;
        }
        if (!(parseInt(postData.PlanDetailId) > 0)) {
            postData.PlanDetailId = 0;
        }
        if (!(parseInt(postData.TextbookVersionDetailId) > 0)) {
            postData.TextbookVersionDetailId = 0;
        }
        if (!(parseInt(postData.RunStatuz) > 0)) {
            postData.RunStatuz = 0;
        }
        var isverify = false;
        //几种情况
        if (postData.SchoolGradeClassIdz != undefined && postData.SchoolGradeClassIdz.length > 0 && (parseInt(postData.PlanDetailId) > 0 || parseInt(postData.TextbookVersionDetailId) > 0)) {
            isverify = true;
        } else if (postData.SchoolGradeClassIdz != undefined && postData.SchoolGradeClassIdz.length > 0 && postData.ClassTime != null && parseInt(postData.SectionId) > 0) {
            isverify = true;
        } else if (postData.FunRoomId > 0 && postData.ClassTime != null && parseInt(postData.SectionId) > 0) {
            isverify = true;
        }
        if (IsLoadEnd && isverify) {
            postData.RecordMode = '@ExperimentRecordModeEnum.BookIng.ParseToInt()';
            ys.ajax({
                url: '@Url.Content("~/ExperimentTeachManage/ExperimentBooking/VerifyRegisterFormJson")',
                type: 'post',
                data: postData,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        VerifyResult = true;
                        //ys.msgSuccess(obj.Message);
                    }
                    else {
                        VerifyResult = false;
                        VerifyMsg = obj.Message;
                        ys.msgError(obj.Message);
                    }
                }
            });
        }
    }

    //#region 添加、删除实验
    function setExperimentData(id, name, bookingid, etype,groupzval ,equipmentneed, materialneed,sourcepath,sourcetype) {
        var obj = $("#experit_" + id);
        var objVal = '';
        if (obj!=undefined) {
            objVal = obj.attr("tagid");
        }
        if (objVal != undefined && parseFloat(objVal) > 0) {
            //更新
        } else {
            //添加
            var html = '<div class="div-experit" id="experit_' + id + '" tagid="' + id + '" bookingid="' + bookingid + '">';
            html += '<div class="form-group row">';
            html += '<label class="col-sm-2 control-label ">实验名称<font class="red"> *</font></label>';
            html += '<div class="col-sm-6">';
            html += '  <input id="SourcePath_' + id + '" type="hidden" value="' + sourcepath + '"/>';
            html += '  <input id="SourceType_' + id + '" type="hidden" value="' + sourcetype + '"/>';
            //html += '  <input id="TextbookVersionDetailId_' + id + '" type="hidden" value="' + detailid +'"/>';
            //html += '  <input id="PlanDetailId_' + id +'" type="hidden" />';
            html += '  <input id="ExperimentName_' + id + '" type="text" class="form-control" value="' + name +'" readonly />';
            html += ' </div>';
            html += ' <div class="col-sm-3"><a class="btn btn-danger" onclick="removeExperiment(\''+id+'\')"><i class="fa fa-remove"></i> 删除</a></div>';
            html += ' </div>';
            html += getSourceTypeHtml(sourcetype, sourcepath);
            if ( @ExperimentTypeEnum.Group.ParseToInt() == etype) {
                html += getExperimentTypeHtml(id, '@ExperimentTypeEnum.Group.ParseToInt()', '@ExperimentTypeEnum.Group.GetDescription()');
                html += getGroupHtml(id, groupzval);
            } else {
                html += getExperimentTypeHtml(id, '@ExperimentTypeEnum.Demo.ParseToInt()', '@ExperimentTypeEnum.Demo.GetDescription()');
            }
            html += getEquipmentNeedHtml(id, equipmentneed);
            html += getMaterialNeedHtml(id, materialneed);
            html += '</div>';
            $("#divExperimentBody").append(html);
        }
    }

    function getSourceTypeHtml(sourcetype, sourcepath) {
        var name = "实验计划";
        if (sourcetype == 2 && sourcepath == 1) {
            name = "实验目录";
        } else if (sourcetype == 2 && sourcepath == 2) {
            name = "校本实验";
        }
        var html = '<div class="form-group row">';
        html += '<label class="col-sm-2 control-label " >实验来源</label>';
        html += '<div class="col-sm-6">'
        html += '    <div style="border: 0px solid #e5e6e7;" class="form-control">' + name + '</div>';
        html += ' </div>';
        html += ' </div>';
        return html;
    }

    function getExperimentTypeHtml(id, typeval, name) {
        var html = '<div class="form-group row">';
        html += '<label class="col-sm-2 control-label " for="ExperimentType_' + id + '">实验类型</label>';
        html += '<div class="col-sm-6">';
        html += '   <input id="ExperimentType_' + id + '" type="hidden" value="' + typeval + '"/>';
        html += '    <div style="border: 0px solid #e5e6e7;" class="form-control">' + name + '</div>';
        html += ' </div>';
        html += ' </div>';
        return html;
    }
    function getGroupHtml(id,groupzval) {
        var html = '<div class="form-group row">';
        html += '<label class="col-sm-2 control-label " for="Groupz_' + id + '">分组数<font class="red"> *</font></label>';
        html += '<div class="col-sm-6">';
        html += '  <input id="Groupz_' + id + '" name="Groupz" type="text" class="form-control" value="' + groupzval +'" />';
        html += ' </div>';
        html += ' <div class="col-sm-3 tag_msg_color">可修改 </div>';
        html += ' </div>';
        return html;
    }
    function getEquipmentNeedHtml(id, textval) {
        var requiredHtml = '';
        if (IsRequiredEquipmentNeed == 1) {
            requiredHtml = '<font class="red">*</font>';
        }
        var html = '<div class="form-group row">';
        html += '<label class="col-sm-2 control-label " for="EquipmentNeed_' + id + '">所需仪器' + requiredHtml + '</label>';
        html += '<div class="col-sm-6">';
        html += '  <textarea id="EquipmentNeed_' + id + '" name="EquipmentNeed" class="form-control">' + textval + '</textarea>';
        html += ' </div>';
        html += ' </div>';
        return html;
    }
    function getMaterialNeedHtml(id,textval) {
        var requiredHtml = '';
        if (IsRequiredMaterialNeed == 1) {
            requiredHtml = '<font class="red">*</font>';
        }
        var html = '<div class="form-group row">';
        html += '<label class="col-sm-2 control-label " for="MaterialNeed_' + id + '">所需耗材' + requiredHtml + '</label>';
        html += '<div class="col-sm-6">';
        html += '  <textarea id="MaterialNeed_' + id + '" name="MaterialNeed" class="form-control">' + textval +'</textarea>';
        html += ' </div>';
        html += ' <div class="col-sm-3 tag_msg_color">可修改 </div>';
        html += ' </div>';
        return html;
    }
    function removeExperiment(id) {
        ys.confirm('您确认要删除该实验吗？', function () {
            $("#experit_" + id).remove();
        });
    }
    //#endregion
</script>

