﻿@{
    Layout = "~/Views/Shared/_FormWhite.cshtml";
}
<style type="text/css">
    .iradio-box {
        position: initial;
        display: inline-block;
        font-size: 14px;
    }

    .iradio-blue {
        position: inherit;
        display: inline-block;
        /*  top: 6px;*/
    }
    .select2-search {
        width: 100%;
    }

        .select2-search .select2-search__field {
            width: 100% !important;
        }

  
    .check-box {
        width: 150px;
        word-break: break-all;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        overflow: hidden;
        font-size: 12px;
    }
</style>
<div class="wrapper animated fadeInRight">
    <form id="form" class="form-horizontal m">
        <div class="form-group row">
            <label class="col-sm-3 control-label ">实验计划名称</label>
            <div class="col-sm-8">
                <input id="PlanName" col="PlanName" class="form-control" readonly />
            </div>
            <div class="col-sm-1"></div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">学年</label>
            <div class="col-sm-8">
                <div id="SchoolYearStart" col="SchoolYearStart"></div>
            </div>
            <div class="col-sm-1"></div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">学期</label>
            <div class="col-sm-8">
                <div id="SchoolTerm" col="SchoolTerm"></div>
            </div>
            <div class="col-sm-1"></div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">课程</label>
            <div class="col-sm-8">
                <div id="CourseId" col="CourseId"></div>
            </div>
            <div class="col-sm-1" style="padding-left:0px;">
                <div class="col-sm-2 tag_msg_color" style="padding-left:0px;">
                    <span id="spanAddress" class="glyphicon glyphicon-question-sign titleprompt inputprompt" aria-hidden="true" data-container="body" data-toggle="tooltip" data-placement="right" data-content="你没有编制计划的课程权限;<br />需教务主任至【人员管理】【备课组长设置】设置你管理的课程年级。<br />设置后请刷新再操作。"></span>
                </div>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">年级</label>
            <div class="col-sm-8">
                <div id="GradeId" col="GradeId"></div>
            </div>
            <div class="col-sm-1"></div>
        </div>
        <div class="form-group divVersion">
            <label class="col-sm-3 control-label ">教材版本</label>
            <div class="col-sm-8">
                <div id="VersionCurrentId" col="VersionCurrentId"></div>
            </div>
            <div class="col-sm-1"></div>
        </div>
        <div class="form-group divClass">
            <label class="col-sm-3 control-label ">
                高中班级类型
            </label>
            <div class="col-sm-8" style="padding-top:8px;">
                <div style="display:flex;flex-wrap:wrap;"><div id="selectItem">选择性学科</div><span id="selectItemSpan" style="color:#999;">（参加高考科目）</span></div>
                <div id="selectClassIdz" style="display:flex;flex-wrap:wrap;"></div>
                
            </div>
            <div class="col-sm-1" style="padding-top:8px;">
                <span style="display:none;" class="glyphicon glyphicon-question-sign titleprompt inputprompt" aria-hidden="true" data-container="body" data-toggle="tooltip" data-content="1、“只能选择使用以上“教材版本”的班级；<br />2、你所需的班级如不在上表中，请联系管理员添加。"></span>
            </div>
        </div>
        <div class="form-group divClass">
            <label class="col-sm-3 control-label ">   </label>
            <div class="col-sm-8">
                <div style="display:flex;flex-wrap:wrap;"><div id="nonSelectItem">非选择性学科</div><span id="nonSelectItemSpan" style="color:#999;">（非参加高考科目）</span></div>
                <div id="nonSelectClassIdz" style="display:flex;flex-wrap:wrap;"></div>
            </div>
            <div class="col-sm-1" style="padding-top:8px;">
                <span style="display:none;" class="glyphicon glyphicon-question-sign titleprompt inputprompt" aria-hidden="true" data-container="body" data-toggle="tooltip" data-content="1、“只能选择使用以上“教材版本”的班级；<br />2、你所需的班级如不在上表中，请联系管理员添加。"></span>
            </div>
        </div>
    </form>
</div>
<script type="text/javascript">
    var id = ys.request("id");
    var Com_IsCopy = ys.request("iscopy");
    var Com_SchoolTermYear = 0;
    var Com_SchoolTerm = 1;
    var EntityJsonData = undefined;

    var CurrentSelectGradeId = 0;
    var CurrentSelectSchoolTerm = 0;
    var CurrentSelectCourseId = 0;
    var CurrentSelectVersioneId = 0;
    var CourseListData = [];
    $(function () {
        $(".inputprompt").popover({
            trigger: 'hover',
            placement: 'left',
            html: true
        });
        if (Com_IsCopy == undefined || parseInt(Com_IsCopy) < 1) {
            Com_IsCopy = 0;
        }
        initComboBox();
        getForm();
    });

    function initComboBox() {

          $("#SchoolTerm").ysComboBox({
             data: ys.getJson(@Html.Raw(typeof(SchoolTermEnum).EnumToDictionaryString())),
             class: 'form-control',
             onChange: function () {
                 var schoolTerm=$('#SchoolTerm').ysComboBox('getValue');
                 if (CurrentSelectSchoolTerm !=schoolTerm ){
                     //加载版本 
                     var gradeId = $('#GradeId').ysComboBox('getValue');
                     var courseid = $('#CourseId').ysComboBox('getValue');
                    if (parseInt(gradeId) < '@GradeEnum.GaoYi.ParseToInt()') {
                        loadVersionBase(gradeId, schoolTerm, courseid);
                    }
                 }
                 loadPlanName();
             }
         });

        // body...
        $('#SchoolYearStart').ysComboBox({
            class: 'form-control',
            key: 'id',
            value: 'name',
            onChange: function (argument) {
                var schootermArr = [];
                var schoolstartyear = $('#SchoolYearStart').ysComboBox('getValue');
                if (schoolstartyear > 0) {
                    if (schoolstartyear == Com_SchoolTermYear) {
                        var tempArr = ys.getJson(@Html.Raw(typeof(SchoolTermEnum).EnumToDictionaryString()));
                        if (tempArr != null && tempArr.length > 0) {
                            $.each(tempArr, function (index, item) {
                                if (item.Key >= Com_SchoolTerm) {
                                    schootermArr.push(item);
                                }
                            })
                        }
                    } else {
                        schootermArr = ys.getJson(@Html.Raw(typeof(SchoolTermEnum).EnumToDictionaryString()));
                    }
                    $("#SchoolTerm").ysComboBox({
                        data: schootermArr,
                        class: 'form-control'
                    });
                    if (schoolstartyear == Com_SchoolTermYear) {
                        $('#SchoolTerm').ysComboBox('setValue', Com_SchoolTerm);
                    } else if (EntityJsonData != undefined && EntityJsonData.SchoolTerm != undefined) {
                        $('#SchoolTerm').ysComboBox('setValue', EntityJsonData.SchoolTerm);
                    }
                }
            }
        });

        if (Com_IsCopy == 1) {
            $('#SchoolTerm select').attr("disabled", "disabled");
        }
        $('#CourseId').ysComboBox({
            class: 'form-control',
            key: 'DictionaryId',
            value: 'DicName',
            onChange: function () {
                var courseid = $('#CourseId').ysComboBox('getValue');
                if (CurrentSelectCourseId != courseid) {
                    CurrentSelectCourseId = courseid;
                    bindGradeData({});
                    bindVersionBaseData({});  
                    CurrentSelectGradeId = 0;
                    CurrentSelectVersioneId = 0;
                    loadGrade(courseid);
                    loadPlanName();
                }
            }
        });
        $('#GradeId').ysComboBox({
            class: 'form-control',
            key: 'DictionaryId',
            value: 'DicName',
            onChange: function () {
                var gradeid = $('#GradeId').ysComboBox('getValue');
                if (CurrentSelectGradeId != gradeid) {
                    CurrentSelectGradeId = gradeid;
                    CurrentSelectVersioneId = 0;
                    setShowGaozhong(gradeid);
                    if (parseInt(gradeid) >= '@GradeEnum.GaoYi.ParseToInt()') {
                        loadClass(gradeid);
                    } else {
                        var schoolterm = $('#SchoolTerm').ysComboBox('getValue');
                        var courseid = $('#CourseId').ysComboBox('getValue');
                        loadVersionBase(gradeid, schoolterm, courseid);
                    }
                    loadPlanName();
                } 
            }
        });
        $("#VersionCurrentId").ysComboBox({
            class: 'form-control',
            onChange: function () {
                loadPlanName();
            }
        });
    }

    function setShowGaozhong(gradeid) {
        if (parseInt(gradeid) >= @GradeEnum.GaoYi.ParseToInt()) {
            $(".divClass").show();
            $(".divVersion").hide();
        }else if(parseInt(gradeid)==0){
            $(".divVersion").hide();
            $(".divClass").hide();
        } else if (parseInt(gradeid) < @GradeEnum.GaoYi.ParseToInt()) {
            $(".divVersion").show();
            $(".divClass").hide();
        }
    }

    function loadCourse(defaultValue) {
        ys.ajax({
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetCourseListJson")',
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1) {                
                    bindCourseData(obj.Data, defaultValue);
                } else {
                    bindCourseData({});
                }
            }
        });
    }

    function bindCourseData(data, defaultValue) {
        CourseListData = data;
        $('#CourseId').ysComboBox({
            class: 'form-control',
            data: data,
            key: 'DictionaryId',
            value: 'DicName'
        });
        if (defaultValue > 0) {
            $('#CourseId').ysComboBox("setValue", defaultValue);
            if (Com_IsCopy == 1) {
                $('#CourseId select').attr("disabled", "disabled");
            }
        }
    }

    function loadGrade(courseid, defaultValue) {
        if (courseid > 0) {
            ys.ajax({
                url: '@Url.Content("~/ExperimentTeachManage/PlanInfo/GetGradeJson")' + "?OptType=8&TypeCode=1003&Pid=" + courseid,
                type: 'get',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        bindGradeData(obj.Data, defaultValue);
                    } else {
                        bindGradeData({});
                    }
                }
            });
        } else {
            bindGradeData({});
        }
    }

    function bindGradeData(data, defaultValue) {
        $('#GradeId').ysComboBox({
            data: data,
            class: 'form-control col-sm-8',
            key: 'DictionaryId',
            value: 'DicName'
        });
        if (defaultValue > 0) {
            $('#GradeId').ysComboBox("setValue", defaultValue);
            if (Com_IsCopy == 1) {
                $('#GradeId select').attr("disabled", "disabled");
            }
        }
    }

    function loadClass(gradeid, defaultValue) {
        if (gradeid > 0) {
            ys.ajax({
                url: '@Url.Content("~/OrganizationManage/SchoolGradeClass/GetListJson")' + "?GradeId=" + gradeid + '&IsGraduate=0',
                type: 'get',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        bindClassData(obj.Data, defaultValue);
                    } else {
                        bindClassData({});
                    }
                }
            });
        } else {
            bindClassData({});
        }
    }

    function bindClassData(data, defaultValue) {
        var courseid = $('#CourseId').ysComboBox('getValue');
        var courseName = ""; 
        if (courseid > 0 && CourseListData && CourseListData.length > 0){
            $.each(CourseListData, function (i, item) {
                if (item.DictionaryId == courseid) {
                    courseName = item.DicName;
                }
            });
        }
        
        $("#selectItem").html(getSelectRadioHtml(courseName,0)); 
        $("#nonSelectItem").html(getNonSelectRadioHtml(courseName,0));
        $('input[name="inputType_checkbox"]').change(function () {
            if ($(this).prop("checked")) {
                var val1 = $(this).val();
                sourcetypeChange(val1);
            }
        });
        $("#selectClassIdz").html('');
        $("#nonSelectClassIdz").html('');
        var htmlSelect = '';
        var htmlNonSelect = '';
        for (var i = 0; i < data.length; i++) {
            var html = '';
            html += $.Format('<span class="titleprompt checkboxprompt" aria-hidden="true" data-container="body" data-toggle="tooltip" data-content="{0}">', data[i].ClassName);
            html += $.Format('<label class="check-box">{0}</label>', data[i].ClassName);
            html += '</span>';
            if (data[i].SelectSubject && data[i].SelectSubject.indexOf(courseid) > -1) {
                htmlSelect += html;
            }else{
                htmlNonSelect += html;
            }
        }
        $("#selectClassIdz").html(htmlSelect);
        $("#nonSelectClassIdz").html(htmlNonSelect);

        $("#selectItemSpan").text('（高考需考' + courseName + '班）');
        $("#nonSelectItemSpan").text('（高考不考' + courseName + '班）');
        if (htmlSelect == '') {
            $("#selectItem").html(getSelectRadioHtml(courseName, 1));
        }
        if (htmlNonSelect == '') {
            $("#nonSelectItem").html(getNonSelectRadioHtml(courseName, 1));
        }

        $(".checkboxprompt").popover({
            trigger: 'hover',
            placement: 'top',
            html: true
        });
        //选中
        if (defaultValue == 4) {
            $('#selectRadio').prop('checked', true);
            $(".single").addClass("checked");
            $(".batch").removeClass("checked");
        } else if (defaultValue == 3) {
            $('#nonSelectRadio').prop('checked', true);
            $(".batch").addClass("checked");
            $(".single").removeClass("checked");
        }
    }

    function sourcetypeChange(selectid) {
        if (selectid == 1) {
            $(".single").addClass("checked");
            $(".batch").removeClass("checked");
            /*     $('#divRemark').hide();*/
        } else {
            $(".batch").addClass("checked");
            $(".single").removeClass("checked");
            /*   $('#divRemark').show();*/
        }
    }
    function getSelectRadioHtml(coursename,isdisabled) {
        var html = '';
        if (isdisabled == 1) {
            html += $.Format('<div style="vertical-align:bottom;color:#999;"> <input name="inputread_checkbox" type="radio" value="-1" disabled="disabled" readOnly>选修{0}班</div>', coursename);
        } else {
            html += '<label class="iradio-box"><div class="iradio-blue single" style="vertical-align:bottom;"><input id="selectRadio" name="inputType_checkbox" type="radio" value="1" style="position: absolute; opacity: 0;"></div>';
            html += $.Format('选修{0}班</label>', coursename);
        }
        return html;
    }

    function getNonSelectRadioHtml(coursename, isdisabled) {
        var html = '';
        if (isdisabled == 1) {
            html += $.Format('<div style="vertical-align:bottom;color:#999;"> <input name="inputread_checkbox" type="radio" value="-1" disabled="disabled" readOnly>非选修{0}班</div>', coursename);
        } else {
            html += '<label class="iradio-box"><div class="iradio-blue batch" style="vertical-align:bottom;"><input id="nonSelectRadio" name="inputType_checkbox" type="radio" value="2" style="position: absolute; opacity: 0;"></div>';
            html += $.Format('非选修{0}班</label>', coursename);
        }
        return html;
    }
    function getSelectReadOnlyHtml(coursename) {
        var html = ''; 
        return html;
    }
    function sourcetypeChange(selectid) {
        if (selectid == 1) {
            $(".single").addClass("checked");
            $(".batch").removeClass("checked");
       /*     $('#divRemark').hide();*/
        } else {
            $(".batch").addClass("checked");
            $(".single").removeClass("checked");
         /*   $('#divRemark').show();*/
        }
    }

    function loadSchoolTermYear() {
        ys.ajax({
            url: '@Url.Content("~/ExperimentTeachManage/ExperimentBooking/GetSchoolTermInfo")',
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1 && obj.Data != undefined && obj.Data.SchoolTermStartYear > 0) {
                    var schoolYear = [];
                    Com_SchoolTermYear = obj.Data.SchoolTermStartYear;
                    Com_SchoolTerm = obj.Data.SchoolTerm;
                    var name = (obj.Data.SchoolTermStartYear + '').substr(2) + '~' + ((obj.Data.SchoolTermStartYear + 1) + '').substr(2);
                    schoolYear.push({ id: obj.Data.SchoolTermStartYear, name: name });

                    var nextYear = obj.Data.SchoolTermStartYear + 1;
                    var nextEndyear = nextYear + 1;
                    name = (nextYear + '').substr(2) + '~' + (nextEndyear + '').substr(2);
                    schoolYear.push({ id: nextYear, name: name });
                    $('#SchoolYearStart').ysComboBox({
                        data: schoolYear,
                        class: 'form-control',
                        key: 'id',
                        value: 'name'
                    });
                    if (EntityJsonData != undefined && EntityJsonData.SchoolYearStart != undefined && parseInt(EntityJsonData.SchoolYearStart) > 0) {
                        $("#SchoolYearStart").ysComboBox('setValue', EntityJsonData.SchoolYearStart);
                    } else {
                        $("#SchoolYearStart").ysComboBox('setValue', Com_SchoolTermYear);
                    }
                } else {
                    var schoolYear = [];
                    var SchoolYearStart = new Date().getFullYear();
                    //根据8月25号，判断默认是上学期还是下学期。
                    Com_SchoolTerm = 1;
                    if (new Date() < new Date(SchoolYearStart + '-08-25 00:00:00')) {
                        Com_SchoolTerm = 2;
                        SchoolYearStart = SchoolYearStart - 1;
                    }
                    Com_SchoolTermYear = SchoolYearStart;
                    SchoolYearEnd = SchoolYearStart + 1;
                    var name = (SchoolYearStart + '').substr(2) + '~' + (SchoolYearEnd + '').substr(2);
                    schoolYear.push({ id: SchoolYearStart, name: name });

                    var nextYear = SchoolYearStart + 1;
                    var nextEndyear = SchoolYearEnd + 1;
                    name = (nextYear + '').substr(2) + '~' + (nextEndyear + '').substr(2);
                    schoolYear.push({ id: nextYear, name: name });
                    $('#SchoolYearStart').ysComboBox({
                        data: schoolYear,
                        class: 'form-control',
                        key: 'id',
                        value: 'name'
                    });
                    if (EntityJsonData != undefined && EntityJsonData.SchoolYearStart != undefined && parseInt(EntityJsonData.SchoolYearStart) > 0) {
                        $("#SchoolYearStart").ysComboBox('setValue', EntityJsonData.SchoolYearStart);
                    } else {
                        $("#SchoolYearStart").ysComboBox('setValue', Com_SchoolTermYear);
                    }
                }
                if (Com_IsCopy == 1) {
                    $("#SchoolYearStart").ysComboBox('setValue', Com_SchoolTermYear);
                }
            }
        });
    }

    function loadVersionBase(gradeid, schoolterm, courseid, defaultValue) {
        if (gradeid > 0 && schoolterm > 0 && courseid > 0) {
            ys.ajax({
                url: '@Url.Content("~/ExperimentTeachManage/PlanDetail/GetVersionListJson")' + "?GradeId=" + gradeid + '&SchoolTerm=' + schoolterm + '&CourseId=' + courseid,
                type: 'get',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        bindVersionBaseData(obj.Data, defaultValue);
                    } else {
                        bindVersionBaseData({});
                    }
                }
            });
       }

    }

    function bindVersionBaseData(data, defaultValue) {
        $('#VersionCurrentId').ysComboBox({
            data: data,
            key: 'Id',
            value: 'VersionName',
            class: 'form-control'
        });
        if (defaultValue > 0) $('#VersionCurrentId').ysComboBox('setValue', defaultValue);
        if (Com_IsCopy == 1) {
            $('#VersionCurrentId select').attr("disabled", "disabled");
        }
        if (Com_IsCopy == 1) {
            $('#VersionCurrentId select').attr("disabled", "disabled");
        }
        loadPlanName();
    }

    function getForm() {
        if (id > 0) {
            ys.ajax({
                url: '@Url.Content("~/ExperimentTeachManage/PlanInfo/GetFormJson")' + '?id=' + id,
                type: 'get',
                async: false,
                success: function (obj) {
                    if (obj.Tag == 1) {
                         
                        loadSchoolTermYear();
                        CurrentSelectCourseId = obj.Data.CourseId;
                        loadCourse(obj.Data.CourseId);
                        CurrentSelectGradeId = obj.Data.GradeId;
                        CurrentSelectSchoolTerm=obj.Data.SchoolTerm;
                        loadGrade(obj.Data.CourseId, obj.Data.GradeId);
                        setShowGaozhong(obj.Data.GradeId);
                         if (obj.Data.GradeId >= @GradeEnum.GaoYi.ParseToInt()) {
                            loadClass(obj.Data.GradeId, obj.Data.CompulsoryType);
                         }else{
                            loadVersionBase(obj.Data.GradeId, obj.Data.SchoolTerm, obj.Data.CourseId, obj.Data.VersionCurrentId);
                         }
                        EntityJsonData = obj.Data;
                        $("#PlanName").val(obj.Data.PlanName);
                    }
                }
            });
        } else {
            var defaultData = {};
            setShowGaozhong(0);
            $('#form').setWebControls(defaultData);
            loadSchoolTermYear();
            loadCourse();
        }
    }

    function saveForm(index) {
        if ($('#form').validate().form()) {
            var postData = $('#form').getWebControls({ Id: id });
            if (Com_IsCopy == 1) {
                postData.CopyId = id;
            }
            var errorMsg = '';
            if (!(parseInt(postData.SchoolYearStart) > 0)) {
                errorMsg += '请选择学年。<br/>';
            }
            if (!(parseInt(postData.SchoolTerm) > 0)) {
                errorMsg += '请选择学期。<br/>';
            }
            if (!(parseInt(postData.CourseId) > 0)) {
                errorMsg += '请选择课程。<br/>';
            }
            if (!(parseInt(postData.GradeId) > 0)) {
                errorMsg += '请选择年级。<br/>';
            }
           
            if (postData.GradeId >= @GradeEnum.GaoYi.ParseToInt()) {
                if ($('#selectRadio').prop('checked') || $('#nonSelectRadio').prop('checked')) {
                    postData.CompulsoryType = @ClassCompulsoryTypeEnum.NonSelect.ParseToInt();
                    if ($('#selectRadio').prop('checked')) {
                        postData.CompulsoryType = @ClassCompulsoryTypeEnum.Select.ParseToInt();
                    }
                } else {
                    errorMsg += '请选择教材类型。<br/>';
                }
                postData.VersionCurrentId = 0;
            } else {
                if (!(parseInt(postData.VersionCurrentId) > 0)) {
                    errorMsg += '请选择教材版本。<br/>';
                }
                postData.CompulsoryType = 0;
            }
            if (errorMsg != '') {
                ys.msgError('验证失败，请填写完成再提交！<br/>' + errorMsg);
                return;
            }
            ys.ajax({
                url: '@Url.Content("~/ExperimentTeachManage/PlanInfo/SaveFormJson")',
                type: 'post',
                data: postData,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        parent.$('#gridTable').ysTable('refresh');
                        parent.resetToolbarStatus();
                        parent.layer.close(index);
                    } else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        }
    }

    function loadPlanName() {
        var coursename = "";
        var gradename = "";
        var termname = "";
        var tempval = $("#CourseId").ysComboBox("getValue");
        if (tempval != undefined && parseInt(tempval) > 0) {
            tempval = $("#CourseId select option[value='" + tempval + "']").text();
            if (tempval != null && tempval.length > 0) {
                coursename = tempval;
            }
        }
        var gradeid = $("#GradeId").ysComboBox("getValue");
        if (gradeid != undefined && parseInt(gradeid) > 0) {
            tempval = $("#GradeId select option[value='" + tempval + "']").text();
            if (tempval != null && tempval.length > 0) {
                gradename = tempval;
            }
        }
      
        tempval = $("#SchoolTerm").ysComboBox("getValue");
        if (tempval != undefined && parseInt(tempval) > 0) {
            tempval = $("#SchoolTerm select option[value='" + tempval + "']").text();
            if (tempval != null && tempval.length > 0) {
                termname = tempval;
            }
        }
        $("#PlanName").val($.Format("{0}{1}{2}计划", coursename, gradename, termname));
    }
</script>