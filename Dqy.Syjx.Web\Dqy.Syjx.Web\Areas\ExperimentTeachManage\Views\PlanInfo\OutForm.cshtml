﻿@{
    Layout = "~/Views/Shared/_FormWhite.cshtml";
}
<style type="text/css">
    .iradio-box {
        position: initial;
        display: inline-block;
        font-size: 14px;
    }

    .iradio-blue {
        position: inherit;
        display: inline-block;
        /*  top: 6px;*/
    }

    .select2-search {
        width: 100%;
    }

        .select2-search .select2-search__field {
            width: 100% !important;
        }


    .check-box {
        width: 150px;
        word-break: break-all;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        overflow: hidden;
        font-size: 12px;
    }
</style>
<div class="wrapper animated fadeInRight">
    <form id="form" class="form-horizontal m"> 
        <div class="form-group row">
            <label class="col-sm-3 control-label ">学年</label>
            <div class="col-sm-8">
                <div id="SchoolYearStart" col="SchoolYearStart"></div>
            </div>
            <div class="col-sm-1"></div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">学期</label>
            <div class="col-sm-8">
                <div id="SchoolTerm" col="SchoolTerm"></div>
            </div>
            <div class="col-sm-1"></div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">课程</label>
            <div class="col-sm-8">
                <div id="CourseId" col="CourseId"></div>
            </div>
            <div class="col-sm-1"></div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">实验计划名称</label>
            <div class="col-sm-8">
                <input id="PlanName" col="PlanName" class="form-control" readonly />
            </div>
            <div class="col-sm-1"></div>
        </div>
    </form>
</div>
<script type="text/javascript">
    var id = ys.request("id");
    var Com_SchoolTermYear = 0;
    var Com_SchoolTerm = 1;
    var EntityJsonData = undefined;

    var CurrentSelectSchoolTerm = 0;
    var CurrentSelectCourseId = 0;
    var CourseListData = [];
    $(function () {
        $(".inputprompt").popover({
            trigger: 'hover',
            placement: 'left',
            html: true
        }); 
        initComboBox();
        getForm();
    });

    function initComboBox() {
        // body...
        $('#SchoolYearStart').ysComboBox({
            class: 'form-control',
            key: 'id',
            value: 'name',
            onChange: function (argument) {
                var schootermArr = [];
                var schoolstartyear = $('#SchoolYearStart').ysComboBox('getValue');
                if (schoolstartyear > 0) {
                    if (schoolstartyear == Com_SchoolTermYear) {
                        var tempArr = ys.getJson(@Html.Raw(typeof(SchoolTermEnum).EnumToDictionaryString()));
                        if (tempArr != null && tempArr.length > 0) {
                            $.each(tempArr, function (index, item) {
                                if (item.Key >= Com_SchoolTerm) {
                                    schootermArr.push(item);
                                }
                            })
                        }
                    } else {
                        schootermArr = ys.getJson(@Html.Raw(typeof(SchoolTermEnum).EnumToDictionaryString()));
                    }
                    $("#SchoolTerm").ysComboBox({
                        data: schootermArr,
                        class: 'form-control'
                    });
                    if (schoolstartyear == Com_SchoolTermYear) {
                        $('#SchoolTerm').ysComboBox('setValue', Com_SchoolTerm);
                    } else if (EntityJsonData != undefined && EntityJsonData.SchoolTerm != undefined) {
                        $('#SchoolTerm').ysComboBox('setValue', EntityJsonData.SchoolTerm);
                    }
                }
            }
        });
        $("#SchoolTerm").ysComboBox({
            data: ys.getJson(@Html.Raw(typeof(SchoolTermEnum).EnumToDictionaryString())),
            class: 'form-control',
            onChange: function () {
                loadPlanName();
            }
        });

        $('#CourseId').ysComboBox({
            class: 'form-control',
            key: 'DictionaryId',
            value: 'DicName',
            onChange: function () {
                var courseid = $('#CourseId').ysComboBox('getValue');
                if (CurrentSelectCourseId != courseid) {
                    CurrentSelectCourseId = courseid;
                    loadPlanName();
                }
            }
        }); 
    } 

    function loadCourse(defaultValue) {
        ys.ajax({
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetSchoolUserCourseListJson")',
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1) {
                    bindCourseData(obj.Data, defaultValue);
                } else {
                    bindCourseData({});
                }
            }
        });
    }

    function bindCourseData(data, defaultValue) {
        CourseListData = data;
        $('#CourseId').ysComboBox({
            class: 'form-control',
            data: data,
            key: 'DictionaryId',
            value: 'DicName'
        });
        if (defaultValue > 0) {
            $('#CourseId').ysComboBox("setValue", defaultValue);
            if (Com_IsCopy == 1) {
                $('#CourseId select').attr("disabled", "disabled");
            }
        }
    }   

    function loadSchoolTermYear() {
        ys.ajax({
            url: '@Url.Content("~/ExperimentTeachManage/ExperimentBooking/GetSchoolTermInfo")',
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1 && obj.Data != undefined && obj.Data.SchoolTermStartYear > 0) {
                    var schoolYear = [];
                    Com_SchoolTermYear = obj.Data.SchoolTermStartYear;
                    Com_SchoolTerm = obj.Data.SchoolTerm;
                    var name = (obj.Data.SchoolTermStartYear + '').substr(2) + '~' + ((obj.Data.SchoolTermStartYear + 1) + '').substr(2);
                    schoolYear.push({ id: obj.Data.SchoolTermStartYear, name: name });

                    var nextYear = obj.Data.SchoolTermStartYear + 1;
                    var nextEndyear = nextYear + 1;
                    name = (nextYear + '').substr(2) + '~' + (nextEndyear + '').substr(2);
                    schoolYear.push({ id: nextYear, name: name });
                    $('#SchoolYearStart').ysComboBox({
                        data: schoolYear,
                        class: 'form-control',
                        key: 'id',
                        value: 'name'
                    });
                    if (EntityJsonData != undefined && EntityJsonData.SchoolYearStart != undefined && parseInt(EntityJsonData.SchoolYearStart) > 0) {
                        $("#SchoolYearStart").ysComboBox('setValue', EntityJsonData.SchoolYearStart);
                    } else {
                        $("#SchoolYearStart").ysComboBox('setValue', Com_SchoolTermYear);
                    }
                } else {
                    var schoolYear = [];
                    var SchoolYearStart = new Date().getFullYear();
                    //根据8月25号，判断默认是上学期还是下学期。
                    Com_SchoolTerm = 1;
                    if (new Date() < new Date(SchoolYearStart + '-08-25 00:00:00')) {
                        Com_SchoolTerm = 2;
                        SchoolYearStart = SchoolYearStart - 1;
                    }
                    Com_SchoolTermYear = SchoolYearStart;
                    SchoolYearEnd = SchoolYearStart + 1;
                    var name = (SchoolYearStart + '').substr(2) + '~' + (SchoolYearEnd + '').substr(2);
                    schoolYear.push({ id: SchoolYearStart, name: name });

                    var nextYear = SchoolYearStart + 1;
                    var nextEndyear = SchoolYearEnd + 1;
                    name = (nextYear + '').substr(2) + '~' + (nextEndyear + '').substr(2);
                    schoolYear.push({ id: nextYear, name: name });
                    $('#SchoolYearStart').ysComboBox({
                        data: schoolYear,
                        class: 'form-control',
                        key: 'id',
                        value: 'name'
                    });
                    if (EntityJsonData != undefined && EntityJsonData.SchoolYearStart != undefined && parseInt(EntityJsonData.SchoolYearStart) > 0) {
                        $("#SchoolYearStart").ysComboBox('setValue', EntityJsonData.SchoolYearStart);
                    } else {
                        $("#SchoolYearStart").ysComboBox('setValue', Com_SchoolTermYear);
                    }
                }
            }
        });
    }

    function getForm() {
        if (id > 0) {
            ys.ajax({
                url: '@Url.Content("~/ExperimentTeachManage/PlanInfo/GetFormJson")' + '?id=' + id,
                type: 'get',
                async: false,
                success: function (obj) {
                    if (obj.Tag == 1) {

                        loadSchoolTermYear();
                        CurrentSelectCourseId = obj.Data.CourseId;
                        loadCourse(obj.Data.CourseId); 
                     
                        EntityJsonData = obj.Data;
                        $("#PlanName").val(obj.Data.PlanName);
                    }
                }
            });
        } else {
            var defaultData = {};
            $('#form').setWebControls(defaultData);
            loadSchoolTermYear();
            loadCourse();
        }
    }

    function saveForm(index) {
        if ($('#form').validate().form()) {
            var postData = $('#form').getWebControls({ Id: id });
            var errorMsg = '';
            if (!(parseInt(postData.SchoolYearStart) > 0)) {
                errorMsg += '请选择学年。<br/>';
            }
            if (!(parseInt(postData.SchoolTerm) > 0)) {
                errorMsg += '请选择学期。<br/>';
            }
            if (!(parseInt(postData.CourseId) > 0)) {
                errorMsg += '请选择课程。<br/>';
            }
            if (errorMsg != '') {
                ys.msgError('验证失败，请填写完成再提交！<br/>' + errorMsg);
                return;
            }
            ys.ajax({
                url: '@Url.Content("~/ExperimentTeachManage/PlanInfo/SaveOutFormJson")',
                type: 'post',
                data: postData,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        parent.$('#gridTable').ysTable('refresh');
                        parent.resetToolbarStatus();
                        parent.layer.close(index);
                    } else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        }
    }

    function loadPlanName() {
        var coursename = ""; 
        var schoolyear = "";
        var termname = "";
        var tempval = $("#CourseId").ysComboBox("getValue");
        if (tempval != undefined && parseInt(tempval) > 0) {
            tempval = $("#CourseId select option[value='" + tempval + "']").text();
            if (tempval != null && tempval.length > 0) {
                coursename = tempval;
            }
        } 
        var schoolYearStart = $("#SchoolYearStart").ysComboBox("getValue");
        if (schoolYearStart && parseInt(schoolYearStart) > 0) {
            var yearend = parseInt(schoolYearStart) + 1;
            schoolyear = (schoolYearStart.substr(2, 3) + "~" + (yearend+'').substr(2, 3));
        }
        tempval = $("#SchoolTerm").ysComboBox("getValue");
        if (tempval != undefined && parseInt(tempval) > 0) {
            tempval = $("#SchoolTerm select option[value='" + tempval + "']").text();
            if (tempval != null && tempval.length > 0) {
                termname = tempval;
            }
        }
        $("#PlanName").val($.Format("{0}{1}{2}课外实验计划", coursename, schoolyear, termname));
    }
</script>