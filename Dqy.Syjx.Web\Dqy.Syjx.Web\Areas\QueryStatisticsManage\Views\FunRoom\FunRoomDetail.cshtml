﻿@{
    Layout = "~/Views/Shared/_FormWhite.cshtml";
}
@inject Microsoft.AspNetCore.Hosting.IWebHostEnvironment HostingEnvironment
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/uploadifive/File.min.css"))
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/uploadifive/File.min.js"))
<style type="text/css">
    .layui-laydate {
        top: 10px !important;
    }

    .tag_msg_color {
        color: #999;
    }
</style>
<div class="wrapper animated fadeInRight">
    <form id="form" class="form-horizontal m">
        <input type="hidden" id="Id" col="Id" value="0" />
        <div class="form-group row">
            <label class="col-sm-3 control-label ">一级分类</label>
            <div class="col-sm-6">
                <span id="DictionaryId1006A"  class="form-control"></span>
            </div>
            <div class="col-sm-2">

            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">二级分类</label>
            <div class="col-sm-6">
                <span id="DictionaryId1006B"  class="form-control"></span>
            </div>
            <div class="col-sm-2">

            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">适用学科</label>
            <div class="col-sm-6">
                <span id="DictionaryId1005" class="form-control"></span>
            </div>
            <div class="col-sm-2">

            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">适用学段</label>
            <div class="col-sm-6">
                <span id="DictionaryId1002" class="form-control"></span>
            </div>
            <div class="col-sm-2 tag_msg_color">
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">实验（专用）室名称</label>
            <div class="col-sm-6">
                <span id="Name" class="form-control"></span>
            </div>
            <div class="col-sm-2 tag_msg_color">
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">使用面积（㎡）</label>
            <div class="col-sm-6">
                <span id="UseArea" class="form-control"></span>
            </div>
            <div class="col-sm-2">

            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">属性</label>
            <div class="col-sm-6">
                <span id="NatureName" class="form-control"></span>
            </div>
            <div class="col-sm-2 tag_msg_color">

            </div>
        </div>
        <div class="form-group row naturetype">
            <label class="col-sm-3 control-label ">座位数（个）</label>
            <div class="col-sm-6">
                <span id="SeatNum" class="form-control"></span>
            </div>
            <div class="col-sm-2 tag_msg_color">
            </div>
        </div>
        <div class="form-group row naturetype">
            <label class="col-sm-3 control-label ">是否数字化</label>
            <div class="col-sm-6">
                <span id="IsDigitalize"  class="form-control"></span>
            </div>
            <div class="col-sm-2 tag_msg_color">
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">管理部门</label>
            <div class="col-sm-6">
                <span id="SysDepartmentId"  class="form-control"></span>
            </div>
            <div class="col-sm-2 tag_msg_color">
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">管理人</label>
            <div class="col-sm-6">
                <span id="SysUserId"  class="form-control"></span>
            </div>
            <div class="col-sm-2 tag_msg_color">
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">起初建设时间</label>
            <div class="col-sm-6">
                <span id="BuildTime"  class="form-control"></span>
            </div>
            <div class="col-sm-2 tag_msg_color">

            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">最新改造时间</label>
            <div class="col-sm-6">
                <span id="ReformTime"  class="form-control"></span>
            </div>
            <div class="col-sm-2 tag_msg_color">

            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">地点</label>
            <div class="col-sm-6">
                <span id="Address" class="form-control"></span>
            </div>
            <div class="col-sm-2 tag_msg_color">
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">现场照片</label>
            <div class="col-sm-6">
                <div id="spanFile"></div>
            </div>
            <div class="col-sm-2 tag_msg_color">
            </div>
        </div>
    </form>
</div>
<script type="text/javascript">
    var id = ys.request("id");
    $(function () {
        parent.removeConfirm();
        getForm(); 
    }); 
    function getForm() {
        if (id > 0) {
            ys.ajax({
                url: '@Url.Content("~/QueryStatisticsManage/FunRoom/GetFunRoomDetailJson")' + '?id=' + id,
                type: 'get',
                success: function (obj) {
                    if (obj.Tag == 1) {
                       /* $('#form').setWebControls(obj.Data);*/
                        $("#DictionaryId1006A").text(obj.Data.ClassNameA);
                        $("#DictionaryId1006B").text(obj.Data.ClassNameB);
                        $("#DictionaryId1005").text(obj.Data.SubjectName);
                        $("#DictionaryId1002").text(obj.Data.SchoolStageName);
                        $("#Name").text(obj.Data.Name);
                        $("#UseArea").text(obj.Data.UseArea);
                        $("#NatureName").text(obj.Data.NatureName);
                        $("#SeatNum").text(obj.Data.SeatNum);
                        $("#Address").text(obj.Data.AddressFull);
                        if (obj.Data.IsDigitalize==1) {
                            $("#IsDigitalize").text("是");
                        } else {
                            $("#IsDigitalize").text("否");
                        }
                        $("#SysDepartmentId").text(obj.Data.DepartmentName);
                        $("#SysUserId").text(obj.Data.RealName);
                        if (isNaN(obj.Data.BuildTime) && obj.Data.BuildTime.length > 10) {
                            $("#BuildTime").text(obj.Data.BuildTime.substring(0,10));
                        }
                        if (isNaN(obj.Data.ReformTime) && obj.Data.ReformTime.length > 10) {
                            $("#ReformTime").text(obj.Data.ReformTime.substring(0, 10));
                        }
                        //调用显示附件信息
                        var objDiv = $("#spanFile");
                        objDiv.html("--");
                        if (isNaN(obj.Data.AttachmentList) && obj.Data.AttachmentList != undefined) {
                            var fileList = obj.Data.AttachmentList;
                            if (fileList.length > 0) {
                                //先清空
                                objDiv.html("");
                                for (var i = 0; i < fileList.length; i++) {
                                    var id = fileList[i].Id;
                                    var filePath = fileList[i].Path;
                                    var fileExt = fileList[i].Ext.toUpperCase();
                                    var fileTitle = fileList[i].Title;
                                    if (fileExt == ".JPG" || fileExt == ".BMP" || fileExt == ".JPEG" || fileExt == ".PNG" || fileExt == ".GIF") {
                                        objDiv.append('<span class="keywords" idValue="' + id + '"><a  modal="zoomImg"  href="javascript:void(0);" src="' + filePath + '" >' + fileTitle + '</a></span>');
                                    } else {
                                        objDiv.append('<span class="keywords" idValue="' + id + '"><a target="_blank" href="' + filePath + '" >' + fileTitle + '</a></span>');
                                    }
                                }
                                imgages.showextalink();
                            }
                        }
                    }
                }
            });
        }
        else {
            var defaultData = {Id:0};
            $('#form').setWebControls(defaultData);
        }
    } 
</script>

