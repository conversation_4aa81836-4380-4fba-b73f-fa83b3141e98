﻿@{
    Layout = "~/Views/Shared/_Index.cshtml";
}
<div class="container-div">

    <div class="row" style="height:auto;">
        <div class="col-sm-12">
            <div class="ibox float-e-margins">
                <div class="ibox-title">
                    <h5>填报信息</h5>
                    <div class="ibox-tools">
                        <a class="collapse-link">
                            <i class="fa fa-chevron-up"></i>
                        </a>
                        @*<a class="close-link">
                                <i class="fa fa-times"></i>
                            </a>*@
                    </div>
                </div>
                <div class="ibox-content">
                    <div class="row">
                        <div class="col-sm-12" style="font-size:14px">
                            <div class="card-body">
                                <form id="baseForm" class="form-horizontal m">
                                    <input type="hidden" id="statuz" col="Statuz" />
                                    <div class="form-group row">
                                            <label class="col-sm-2 control-label ">采购年度</label>
                                            <div class="col-sm-4">
                                                <input id="purchaseYear" col="PurchaseYear" type="text" readonly="readonly" class="form-control" />
                                            </div>
                                        <label class="col-sm-2 control-label ">仪器名称</label>
                                        <div class="col-sm-4">
                                            <input id="name" col="Name" type="text" readonly="readonly" class="form-control" />
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-sm-2 control-label ">规格属性</label>
                                        <div class="col-sm-10">
                                           @* <input id="model" col="Model" type="text" readonly="readonly" class="form-control" />*@
                                            <textarea id="Model" col="Model" type="text" class="form-control" style="max-width:100%" readonly></textarea>
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-sm-2 control-label ">适用学段</label>
                                        <div class="col-sm-4">
                                            <input id="stage" col="Stage" type="text" readonly="readonly" class="form-control" />
                                        </div>
                                        <label class="col-sm-2 control-label ">适用学科</label>
                                        <div class="col-sm-4">
                                            <input id="course" col="Course" type="text" readonly="readonly" class="form-control" />
                                        </div>
                                    </div>
                                    
                                    <div class="form-group row">
                                        <label class="col-sm-2 control-label ">数量</label>
                                        <div class="col-sm-4">
                                            <input id="num" col="Num" type="text" readonly="readonly" class="form-control" />
                                        </div>
                                        <label class="col-sm-2 control-label ">单位</label>
                                        <div class="col-sm-4">
                                            <input id="unitName" col="UnitName" type="text" readonly="readonly" class="form-control" />
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-sm-2 control-label ">单价</label>
                                        <div class="col-sm-4">
                                            <input id="price" col="Price" type="text" readonly="readonly" class="form-control" />
                                        </div>
                                        <label class="col-sm-2 control-label ">金额</label>
                                        <div class="col-sm-4">
                                            <input id="amountSum" col="AmountSum" type="text" readonly="readonly" class="form-control" />
                                        </div>
                                    </div>
                                   
                                    <div class="form-group row">
                                        <label class="col-sm-2 control-label ">采购理由</label>
                                        <div class="col-sm-10">
                                            <input id="reason" col="Reason" type="text" readonly="readonly" class="form-control" />
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-sm-2 control-label ">填报日期</label>
                                        <div class="col-sm-4">
                                            <input id="BaseCreateTime" col="BaseCreateTime" type="text" readonly="readonly" class="form-control" />
                                        </div>
                                       @* <label class="col-sm-2 control-label ">计划状态</label>
                                        <div class="col-sm-4">
                                            <input id="StatuzDesc" col="StatuzDesc" type="text" readonly="readonly" class="form-control" />
                                        </div>*@
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div id="divAudit">
    </div>

    <!--<div class="row" style="height:auto;">
        <div class="col-sm-12">
            <div class="ibox float-e-margins">
                <div class="ibox-title">
                    <h5>审批日志</h5>
                    <div class="ibox-tools">
                        <a class="collapse-link">
                            <i class="fa fa-chevron-up"></i>
                        </a>-->
                        @*<a class="close-link">
                                <i class="fa fa-times"></i>
                            </a>*@
                    <!--</div>
                </div>
                <div class="ibox-content">
                    <div class="row">
                        <div class="col-sm-12" style="font-size:14px">
                            <div class="card-body">
                                <div class="form-group row">
                                    <div class="col-sm-8">
                                        2021-10-14 17:28 学校申报 Gavin 状态：通过
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <div class="col-sm-8">
                                        2021-10-14 17:28 学校申报 Gavin 状态：通过
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <div class="col-sm-8">
                                        2021-10-14 17:28 学校申报 Gavin 状态：通过
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>-->

</div>

<script type="text/javascript">
    var id = ys.request("id");
    $(function () {

        getForm();

        getAuditLog();
    });

    function getForm() {
        if (id > 0) {
            ys.ajax({
                url: '@Url.Content("~/InstrumentManage/PurchaseDeclaration/GetFormJson")' + '?id=' + id,
                type: 'get',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        obj.Data.Price = ComBox.ToLocaleString(obj.Data.Price);
                        obj.Data.AmountSum = ComBox.ToLocaleString(obj.Data.AmountSum);
                        $('#baseForm').setWebControls(obj.Data);

                        //console.log("obj:"+JSON.stringify(obj));
                    }
                }
            });
        }
    }

    function getAuditLog() {
        ys.ajax({
            url: '@Url.Content("~/InstrumentManage/PurchaseAudit/GetAuditLog")' + '?purchaseDeclarationId=' + id,
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1) {
                    $.each(obj.Data, function (i, v) {
                        if (v.IsShow == @IsEnum.Yes.ParseToInt()) {
                            var html = '';
                            html += '<div class="row" style="height:auto;"><div class="col-sm-12"><div class="ibox float-e-margins">';
                            html += $.Format('<div class="ibox-title"><h5>{0}</h5>', v.ProcessNumberDesc);
                            html += '</div>';
                            html += '<div class="ibox-content"><div class="row"><div class="col-sm-12" style="font-size:14px"><div class="card-body">';
                            html += '<form id="fileConfigForm" class="form-horizontal m"><div class="form-group row">';
                            html += '<label class="col-sm-2 control-label ">审批结果</label><div class="col-sm-10">';
                            html += $.Format('<input type="text" value="{0}" readonly="readonly" class="form-control" /></div></div>', v.ApprovalStatuz % 2 == 1 ? '不通过' : '通过');
                            html += '<div class="form-group row"><label class="col-sm-2 control-label ">审批意见</label>';
                            html += $.Format('<div class="col-sm-10"><textarea readonly="readonly" style="max-width:100%" class="form-control">{0}</textarea></div></div>', v.ApprovalRemark);
                            html += '<div class="form-group row"><label class="col-sm-2 control-label ">审批人</label>';
                            html += $.Format('<div class="col-sm-4"><input type="text" value="{0}" readonly="readonly" class="form-control" /></div>',v.AuditUserName);
                            html += '<label class="col-sm-2 control-label ">审批日期</label>';
                            html += $.Format('<div class="col-sm-4"><input value="{0}" type="text" readonly="readonly" class="form-control" /></div></div>', v.BaseCreateTime);
                            html += '</form></div></div></div></div></div></div></div>';
                            $('#divAudit').append(html);
                        }
                    });
                }
            }
        });
    }

</script>

