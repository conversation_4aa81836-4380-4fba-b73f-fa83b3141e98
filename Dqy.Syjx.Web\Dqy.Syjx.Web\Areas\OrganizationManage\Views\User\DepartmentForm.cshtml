﻿
@{
    Layout = "~/Views/Shared/_FormWhite.cshtml";
}

<div class="wrapper animated fadeInRight">
    <form id="form" class="form-horizontal m">

        <div class="form-group row">
            <label class="col-sm-2 control-label ">
                <span class="glyphicon glyphicon-question-sign titleprompt inputprompt" aria-hidden="true" data-container="body" data-toggle="tooltip" data-placement="right"
                      data-content="一个用户可属于多个部门"></span>
                所属部门
            </label>
            <div class="col-sm-10">
                <div id="departmentId" class="ztree"></div>
            </div>
        </div>

    </form>
</div>


<script type="text/javascript">
    var userIds = ys.request("ids");
    $(function () {
        
        $(".inputprompt").popover({
            trigger: 'hover',
            html: true
        });

        $('#departmentId').ysTree({
            async: false,
            url: '@Url.Content("~/OrganizationManage/Department/GetDepartmentTreeListJson")',
            maxHeight: '300px',
            check: { enable: true, chkboxType: { "Y": "", "N": "" } },
            expandLevel: 2,
        })

    });

    function saveForm(index) {
        if ($("#form").validate().form()) {

            var departmentIds = $('#departmentId').ysTree("getCheckedNodes");
            if (departmentIds==""){
                ys.msgError("请选择所属部门");
                return;
            }
            var listUserId = userIds.split(',');
            var listDepartmentId = departmentIds.split(',');
            var postData = { listUserId: listUserId, listNew: listDepartmentId };
            ys.ajax({
                url: '@Url.Content("~/OrganizationManage/DepeartmentRelation/SaveBatchDepartmentForm")',
                type: "post",
                data: postData,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        parent.$('#gridTable').ysTable('refresh'); parent.resetToolbarStatus();
                        parent.layer.close(index);
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        }
    }


</script>
