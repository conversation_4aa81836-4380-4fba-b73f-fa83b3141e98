﻿@{
    Layout = "~/Views/Shared/_FormWhite.cshtml";
}

<div class="wrapper animated fadeInRight">
    <form id="form" class="form-horizontal m">
        <div class="form-group row">
            <label class="col-sm-3 control-label ">单位<font class="red"> *</font></label>
            <div class="col-sm-8" id="unitId" col="UnitId"></div>
            <input type="hidden" id="schoolName" col="SchoolName" value="" />
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">摄像头名称<font class="red"> *</font></label>
            <div class="col-sm-8" id="cameraId" col="CameraId"></div>
            <input type="hidden" id="srcName" col="SrcName" value="" />
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">摄像头所在功能室<font class="red"> *</font></label>
            <div class="col-sm-8" id="funRoomId" col="FunRoomId"></div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">备注</label>
            <div class="col-sm-8">
                <input id="remark" col="Remark" type="text" class="form-control" />
            </div>
        </div>
    </form>
</div>

<script type="text/javascript">
    var id = ys.request("id");
    $(function () {
        loadFunRoom(0);

        $('#unitId').ysComboBox(
             {
                url: '@Url.Content("~/OrganizationManage/Unit/GetListJson")' + "?PageSize=10000",
                class: 'form-control col-sm-8',
                key: 'Id',
                value: 'Name',
                onChange: function () {
                    var unitId = $('#unitId').ysComboBox('getValue');
                    console.log('unitId', unitId);
                    if (unitId == "") {
                        unitId = 0;
                    } else {
                        loadFunRoom(unitId);
                    }
                }
            });
       
        $('#cameraId').ysComboBox(
             {
                url: '@Url.Content("~/CameraManage/HaiKangCamera/GetListJson")' + "?PageSize=10000",
                class: 'form-control col-sm-8',
                key: 'Id',
                value: 'CameraName',
                onChange: function () {
                    var cameraId = $('#cameraId').ysComboBox('getValue');
                    if (cameraId == "") {
                        cameraId = 0;
                    }
                }
             });


        getForm();

    });

    function loadFunRoom(unitid) {
        console.log('loadFunRoom-unitId', unitid);
        if (unitid > 0) {
            ys.ajax({
                url: '@Url.Content("~/QueryStatisticsManage/FunRoom/GetFunRoomPageListJson")' + '?pageSize=1000&pageIndex=1&UnitId=' + unitid,
                type: 'get',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        $('#funRoomId').ysComboBox({
                            data: obj.Data,
                            class: 'form-control',
                            key: 'Id',
                            value: 'Name',
                            defaultName: '请选择功能室',
                            onChange: function () {
                                var funroomid = $('#funRoomId').ysComboBox('getValue');
                                console.log('功能室ID', funroomid);
                            }
                        });
                        if (obj.Data.length == 1) {
                            $("#funRoomId").ysComboBox('setValue', obj.Data[0].Id);

                        }
                    } else {
                        $('#funRoomId').ysComboBox({ data: [], key: 'Id', value: 'Name', class: 'form-control' });
                        ys.msgError(obj.Message);
                    }
                }
            });
        } else {
            $('#funRoomId').ysComboBox({ data: [], key: 'Id', value: 'Name', class: 'form-control' });
        }
        //$(".select2-container").width("100%");
    }

    function getForm() {
        if (id > 0) {
            ys.ajax({
                url: '@Url.Content("~/CameraManage/SchoolCamera/GetFormJson")' + '?id=' + id,
                type: 'get',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        $('#form').setWebControls(obj.Data);
                    }
                }
            });
        }
        else {
            var defaultData = {};
            $('#form').setWebControls(defaultData);
        }
    }

    function saveForm(index) {
        if ($('#form').validate().form()) {
            var postData = $('#form').getWebControls({ Id: id });
    
            if (postData.UnitId == "") {
                ys.msgError("请选择单位");
                return;
            }
            if (postData.CameraId == "") {
                ys.msgError("请选择摄像头");
                return;
            }
            if (postData.FunRoomId == "") {
                ys.msgError("请选择功能室");
                return;
            }
            console.log('postData', JSON.stringify(postData));
            @*ys.ajax({
                url: '@Url.Content("~/CameraManage/HaiKangCamera/SaveFormJson")',
                type: 'post',
                data: postData,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        parent.$('#gridTable').ysTable('refresh');
                        parent.resetToolbarStatus();
                        parent.layer.close(index);
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });*@
        }
    }
</script>

