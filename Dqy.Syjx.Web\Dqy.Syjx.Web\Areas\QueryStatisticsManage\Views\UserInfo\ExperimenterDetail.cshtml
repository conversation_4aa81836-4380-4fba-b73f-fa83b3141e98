﻿@{
    Layout = "~/Views/Shared/_FormWhite.cshtml";
}

@inject Microsoft.AspNetCore.Hosting.IWebHostEnvironment HostingEnvironment
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/uploadifive/File.min.css"))
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/uploadifive/File.min.js"))
<div class="wrapper animated fadeInRight">
    <form id="form" class="form-horizontal m">
        <div class="form-group row">
            <label class="col-sm-2 control-label ">姓名：<font class="red"> *</font></label>
            <div class="col-sm-8">
                <input id="RealName" col="RealName" type="text" class="form-control" readonly/>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-2 control-label ">手机号码：<font class="red"> *</font></label>
            <div class="col-sm-8">
                <input id="Mobile" col="Mobile" type="text" class="form-control" readonly/>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-2 control-label ">邮箱：</label>
            <div class="col-sm-8">
                <input id="Email" col="Email" type="text" class="form-control" readonly/>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-2 control-label ">性别：</label>
            <div class="col-sm-8">
                <input id="GenderName" type="text" class="form-control" readonly />
            </div>
        </div>

        <div class="form-group row">
            <label class="col-sm-2 control-label ">出生日期：</label>
            <div class="col-sm-8">
                <input id="Birthday" col="Birthday" type="text" class="form-control" readonly/>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-2 control-label ">学历：</label>
            <div class="col-sm-8">
                <input id="EducationLevelName" type="text" class="form-control" readonly />
               
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-2 control-label ">职称：</label>
            <div class="col-sm-8">
                <input id="JobTitleName" type="text" class="form-control" readonly />
               
            </div>
        </div>
        <div class="form-group divExperimenter" style="display:none;">
            <label class="col-sm-2 control-label ">实验员性质：</label>
            <div class="col-sm-8">
                <div id="ExperimenterNature" col='ExperimenterNature' style="padding-top: 8px;"></div>
            </div>
        </div>
        <div class="form-group divExperimenter" style="display:none;">
            <label class="col-sm-2 control-label ">实验员上岗证：</label>
            <div class="col-sm-8">
                <div id="spanFile" style="padding: 10px 0px;"></div>
                <div style="height: 55px;display:none;" id="fileQueue"></div>
            </div>
        </div>
    </form>
   
</div>

<script type="text/javascript">
    var id = ys.request("id");
    var userName = decodeURI(ys.request("userName"));
    $(function () {
        laydate.render({ elem: '#Birthday', format: 'yyyy-MM-dd', trigger: 'click', position: 'fixed' });
        getForm();

    });

    function getForm() {
        $("#spanFile").html("--");
        if (id > 0) {
            ys.ajax({
                url: '@Url.Content("~/PersonManage/UserInfo/GetFormExtJson")' + '?id=' + id,
                type: 'get',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        $('#form').setWebControls(obj.Data);
                        var graderArr = ys.getJson(@Html.Raw(typeof(GenderTypeEnum).EnumToDictionaryString()));
                        graderArr.map(function (item) {
                            if (item.Key == obj.Data.Gender) {
                                $("#GenderName").val(item.Value);
                                return false;
                            }
                        });
                        var jobtiitleArr = ys.getJson(@Html.Raw(typeof(JobTiitleEnum).EnumToDictionaryString()));
                        jobtiitleArr.map(function (item) {
                            if (item.Key == obj.Data.JobTitle) {
                                $("#JobTitleName").val(item.Value);
                                return false;
                            }
                        });
                        var educationlevelArr = ys.getJson(@Html.Raw(typeof(EducationLevelEnum).EnumToDictionaryString()));
                        educationlevelArr.map(function (item) {
                            if (item.Key == obj.Data.EducationLevel) {
                                $("#EducationLevelName").val(item.Value);
                                return false;
                            }
                        });

                        //处理学校，单位属性 单位性质
                        $("input[name='IsExperimenter']").val(obj.Data.IsExperimenter);
                        if (obj.Data.IsExperimenter == 1) {
                            $("#spanIsExperimenter").text('是');
                            //判断文件类型，如果未PDF则在新页面打开。
                            $('#experimenterCard').attr("href", obj.Data.Path);
                            /*$('#experimenterCard').text(obj.Data.CardName);*/
                            $('.divExperimenter').show();
                            //调用显示附件信息
                            if (isNaN(obj.Data.AttachmentList) && obj.Data.AttachmentList != undefined) {
                                var fileList = obj.Data.AttachmentList;
                                //先清空
                                var objDiv = $("#spanFile");
                                objDiv.html("");
                                if (fileList.length > 0) {
                                    for (var i = 0; i < fileList.length; i++) {
                                        var attachmentid = fileList[i].Id;
                                        var filePath = fileList[i].Path;
                                        var fileExt = fileList[i].Ext.toUpperCase();
                                        var fileTitle = fileList[i].Title;
                                        if (fileExt == ".JPG" || fileExt == ".BMP" || fileExt == ".JPEG" || fileExt == ".PNG" || fileExt == ".GIF") {
                                            objDiv.append('<span class="keywords" idValue="' + attachmentid + '"> <a  modal="zoomImg"  href="javascript:void(0);" src="' + filePath + '" >' + fileTitle + '</a></span>');
                                        } else {
                                            objDiv.append('<span class="keywords" idValue="' + attachmentid + '"><a target="_blank" href="' + filePath + '" >' + fileTitle + '</a></span>');
                                        }
                                    }
                                    imgages.showextalink();
                                }
                            }
                        } else {
                            $("#spanIsExperimenter").text('否');
                        }
                    } else {
                        layer.msg(obj.Message);
                    }
                }
            });
        }
        else {
            var defaultData = {};
            $('#form').setWebControls(defaultData);
        }
    }
</script>

