﻿@{
    Layout = "~/Views/Shared/_FormWhite.cshtml";
}
@inject Microsoft.AspNetCore.Hosting.IWebHostEnvironment HostingEnvironment
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/uploadifive/File.min.css"))
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/uploadifive/File.min.js"))
<div class="wrapper animated fadeInRight">
    <form id="form" class="form-horizontal m">
        <input type="hidden" id="Id" col="Id" value="0" />
        <div class="form-group row">
            <label class="col-sm-3 control-label ">选择分类<font class="red"> *</font></label>
            <div class="col-sm-7">
                <input type="hidden" id="DictionaryId1006A" col="DictionaryId1006A" value="0" />
                <input type="hidden" id="DictionaryId1006B" col="DictionaryId1006B" value="0" />
                <div id="ClassTree" col="ClassTree"></div>
            </div>
            <div class="col-sm-2">

            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">适用学科<font class="red"> *</font></label>
            <div class="col-sm-7">
                <div id="DictionaryId1005" col="DictionaryId1005"></div>
            </div>
            <div class="col-sm-2">

            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">适用学段<font class="red"> *</font></label>
            <div class="col-sm-7">
                <div id="DictionaryId1002" col="DictionaryId1002"></div>
            </div>
            <div class="col-sm-2 tag_msg_color">
                <span class="glyphicon glyphicon-question-sign titleprompt inputprompt" aria-hidden="true" data-container="body" data-toggle="tooltip" data-placement="right" data-content="九年制、完中、十二年制学校如存在多学段共用一室，需多选"></span>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">实验（专用）室名称<font class="red"> *</font></label>
            <div class="col-sm-7">
                <input id="Name" col="Name" type="text" class="form-control" autocomplete="off" placeholder="根据个性名称自定义" />
            </div>
            <div class="col-sm-2 tag_msg_color">
                <span class="glyphicon glyphicon-question-sign titleprompt inputprompt" aria-hidden="true" data-container="body" data-toggle="tooltip" data-placement="right" data-content="1：根据个性名称自定义<br/>2：是指实验室或专用室名称"></span>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label " id="labUseArea">使用面积（㎡）<font class="red"> *</font></label>
            <div class="col-sm-7">
                <input id="UseArea" col="UseArea" type="text" class="form-control" autocomplete="off" />
            </div>
            <div class="col-sm-2">

            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">属性</label>
            <div class="col-sm-7">
                <input id="RoomAttributeName" col="RoomAttributeName" type="text" class="form-control" readonly />
                <input type="hidden" id="RoomAttribute" col="RoomAttribute" name="name" value="" />
                <input type="hidden" id="RoomAttributeType" col="RoomAttributeType" name="name" value="" />
            </div>
            <div class="col-sm-2 tag_msg_color">

            </div>
        </div>
        <div class="form-group row naturetype">
            <label class="col-sm-3 control-label " id="labSeatNum">座位数（个）</label>
            <div class="col-sm-7">
                <input id="SeatNum" col="SeatNum" type="text" class="form-control" autocomplete="off" />
            </div>
            <div class="col-sm-2 tag_msg_color">
                <span class="glyphicon glyphicon-question-sign titleprompt inputprompt" aria-hidden="true" data-container="body" data-toggle="tooltip" data-placement="right" data-content="是指学生座位数"></span>
            </div>
        </div>
        <div class="form-group row naturetype">
            <label class="col-sm-3 control-label " id="labIsDigitalize">数字化</label>
            <div class="col-sm-7">
                <div id="IsDigitalize" col="IsDigitalize"></div>
            </div>
            <div class="col-sm-2 tag_msg_color">
                <span class="glyphicon glyphicon-question-sign titleprompt inputprompt" aria-hidden="true" data-container="body" data-toggle="tooltip" data-placement="right" data-content="是指教学设备为数字化"></span>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label " id="labDepartmentId">管理部门</label>
            <div class="col-sm-7">
                <div id="SysDepartmentId" col="SysDepartmentId"></div>
            </div>
            <div class="col-sm-2 tag_msg_color">
                <span id="spanDepartmentId" class="glyphicon glyphicon-question-sign titleprompt inputprompt" aria-hidden="true" data-container="body" data-toggle="tooltip" data-placement="right" data-content="你所需的部门如不在下拉框中，请联系管理员进行添加！"></span>
            </div>
        </div>
        @*<div class="form-group row">
            <label class="col-sm-3 control-label ">管理人</label>
            <div class="col-sm-7">
                <div id="SysUserId" col="SysUserId"></div>
            </div>
            <div class="col-sm-2 tag_msg_color">
                <span id="spanSysUserId" class="glyphicon glyphicon-question-sign titleprompt inputprompt" aria-hidden="true" data-container="body" data-toggle="tooltip" data-placement="right" data-content=""></span>
            </div>
        </div>*@
        <div class="form-group row">
            <label class="col-sm-3 control-label " id="labBuildTime">起初建设时间</label>
            <div class="col-sm-7">
                <input id="BuildTime" col="BuildTime" type="text" class="form-control" readonly autocomplete="off" />
            </div>
            <div class="col-sm-2 tag_msg_color">

            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label " id="labReformTime">最新改造时间</label>
            <div class="col-sm-7">
                <input id="ReformTime" col="ReformTime" type="text" class="form-control" readonly autocomplete="off" />
            </div>
            <div class="col-sm-2 tag_msg_color">

            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label " id="labInvestmentAmount">总投入（元）</label>
            <div class="col-sm-7">
                <input id="InvestmentAmount" col="InvestmentAmount" type="text" class="form-control" autocomplete="off" onkeyup="num(this)" onpaste="num(this)"/>
            </div>
            <div class="col-sm-2 tag_msg_color">
                <span class="glyphicon glyphicon-question-sign titleprompt inputprompt" aria-hidden="true" data-container="body" data-toggle="tooltip" data-placement="right" data-content="含改造费用，不含实验仪器费用。"></span>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label " id="labAddress">地点</label>
            <div class="col-sm-7">
                <div id="Address" col="Address"></div>
            </div>
            <div class="col-sm-2 tag_msg_color">
                <span id="spanAddress" class="glyphicon glyphicon-question-sign titleprompt inputprompt" aria-hidden="true" data-container="body" data-toggle="tooltip" data-placement="right" data-content="如：实验楼303室"></span>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label " id="labImage">现场照片</label>
            <div class="col-sm-7" style="height:60px;">
                <a class="btn btn-primary btn-xs" onclick="showUploadFileForm()"><i class="fa fa-plus"></i> 上传</a>
                <div style="display:none;">
                    <input type="file" name="uploadify" id="uploadify" />
                    <div style="height: 55px;display:none;" id="fileQueue"></div>
                 </div>
               &nbsp;&nbsp;
            <div id="spanFile" style="display:inline-block;margin-top:3px;"></div>
            </div>
            <div class="col-sm-2 tag_msg_color">
                <span class="glyphicon glyphicon-question-sign titleprompt inputprompt" aria-hidden="true" data-container="body" data-toggle="tooltip" data-placement="right" data-content="照片少于4张；小于5M，支持pdf和图片文件"></span>
            </div>
        </div>
        <input type="hidden" id="SchoolStagez" col="SchoolStagez" value="" />
    </form>
</div>
<input type="hidden" id="hidUserId" value="0" />
<script type="text/javascript">
    var id = ys.request("id");
    var IsAdminInfo;
    var SaveValiDate = [];
    var AttributeType = 0;
    $(function () {
        $(".inputprompt").popover({
            trigger: 'hover',
            placement:'left',
            html: true
        });
        /**获取管理员信息*/
        loadIsAdminInfo();
        $("#IsDigitalize").ysRadioBox({ data: ys.getJson(@Html.Raw(typeof(IsStatusEnum).EnumToDictionaryString())), class: "form-control" });
        $('#Address').ysComboBoxTree({ url: '@Url.Content("~/OrganizationManage/Address/GetZtreeListJson")', class: 'form-control' });

        loadSysDepartment();
    /*    loadSysUser(0);*/
        loadSubject(0);
        laydate.render({ elem: '#BuildTime', format: 'yyyy-MM-dd', trigger: 'click', position: 'fixed'  });
        laydate.render({ elem: '#ReformTime', format: 'yyyy-MM-dd', trigger: 'click', position: 'fixed'});
        loadDictionaryId1006A();
        loadSchoolStage(0);
        loadUploadify();
        getForm();

        loadFormVali();
    });
    function showUploadFileForm() {
        var obj = undefined;
        $("#uploadifive-uploadify input").map(function (index, item) {
            obj = $(this);
            var styleStr = obj.attr("style");
            if (styleStr.indexOf('display') > -1) {
                obj = undefined;
            }
        });
        if (obj != undefined) {
            obj.click();
        }
            //$("#uploadifive-uploadify input:eq(1)").click();
    }
    function loadIsAdminInfo() {
        ys.ajax({
            url: '@Url.Content("~/OrganizationManage/User/GetIsAdminJson")',
            async: false,
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1) {
                    IsAdminInfo = obj.Data;
                    if (obj.Data.IsAdmin == true) {
                        $("#spanDepartmentId").attr("data-content", "你所需的部门如不在下拉框中，请在【系统设置】-【组织架构】中进行添加！");
                       // $("#spanSysUserId").attr("data-content", "你所需的人如不在下拉框中，请在【系统设置】-【用户信息】中进行设置！");
                        $("#spanAddress").attr("data-content", "你所需的地点如不在下拉框中，请在【系统设置】-【地点管理】中进行设置！");
                    } else {
                        var adminName = "";
                        if (obj.Data.AdminName != undefined && obj.Data.AdminName.length > 0) {
                            adminName = ("【" + obj.Data.AdminName + "】");
                        }
                        $("#spanDepartmentId").attr("data-content", "你所需的部门如不在下拉框中，请联系" + adminName + "管理员进行添加！");
                       // $("#spanSysUserId").attr("data-content", "你所需的人如不在下拉框中，请联系" + adminName + "管理员进行添加！");
                        $("#spanAddress").attr("data-content", "你所需的地点如不在下拉框中，请联系" + adminName + "管理员进行添加！");
                    }
                } else {
                    ys.msgError(obj.Message);
                    return undefined;
                }
            }
        });

    }
    function loadSysDepartment() {
        $('#SysDepartmentId').ysComboBoxTree({
            url: '@Url.Content("~/OrganizationManage/Department/GetDepartmentTreeListJson")',
            class: 'form-control',
            callback: {
                customOnClick: function (event, treeId, treeNode) {
                    if (typeof (treeNode) == "string") {
                        treeNode = JSON.parse(treeNode);
                    }
                    //if (isNaN(treeNode.pId) || treeNode.pId == 0 || treeNode.pId == undefined) {
                    //    $('#SysDepartmentId > input').val('');
                    //    ys.msgError("请选择二级根目录。");
                    //} else {
                    //    loadSysUser(treeNode.id);
                    //}
                   /* loadSysUser(treeNode.id);*/
                }
            }
        });
        $(".select2-container").width("100%");
    }
    @*function loadSysUser(departmentid) {
        /*根据部门获取部门下的人员。
         *
         */
        if (departmentid > 0) {
            ys.ajax({
                url: '@Url.Content("~/OrganizationManage/User/GetUserByDepartmentList")' + '?DepartmentId=' + departmentid,
                type: 'get',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        $('#SysUserId').ysComboBox({
                            data: obj.Data,
                            class: 'form-control',
                            key: 'Id',
                            value: 'RealName'
                        });
                        if (obj.Data.length == 1) {
                            $("#SysUserId").ysComboBox('setValue', obj.Data[0].DictionaryId);
                        }
                        if (parseInt($("#hidUserId").val())){
                            $("#SysUserId").ysComboBox('setValue', $("#hidUserId").val());
                        }
                    } else {
                        $('#SysUserId').ysComboBox({ data: [], key: 'Id', value: 'RealName', class: 'form-control' });
                        ys.msgError(obj.Message);
                    }
                }
            });
        } else {
            $('#SysUserId').ysComboBox({ data: [], key: 'Id', value: 'RealName', class: 'form-control' });
        }
    }*@
    function loadDictionaryId1006A() {
        $('#ClassTree').ysComboBoxTree({
            url: '@Url.Content("~/BusinessManage/FunRoom/GetClassTreeJson")' + "?TypeCode=1006",
            class: 'form-control',
            callback: {
                customOnClick: function (event, treeId, treeNode) {
                    if (typeof (treeNode) == "string") {
                        treeNode = JSON.parse(treeNode);
                    }
                    if (isNaN(treeNode.pId) || treeNode.pId == 0 || treeNode.pId == undefined) {
                        $('#ClassTree > input').val('');
                        ys.msgError("请选择二级分类。");
                    } else {
                        loadSubject(treeNode.id)
                        $("#RoomAttributeName").val(treeNode.Attribute.name);
                        $("#RoomAttribute").val(treeNode.Attribute.id);
                        $("#RoomAttributeType").val(treeNode.Attribute.type);
                        if (treeNode.Attribute.type == 3) {
                            AttributeType = 3;
                            $('.naturetype').hide();
                        } else {
                            AttributeType = 1;
                            $('.naturetype').show();
                        }
                    }
                },
                customOnSuccess: function (param, treeId, data) {
                    if (data.Data == undefined || data.Data.length == 0) {
                            ComBox.LoadPageMessage('分类科目管理', '/BusinessManage/UserSchoolStageSubject/StageSubjectInput', '实验员授权', @RoleEnum.BusinessManager.ParseToInt());
                    }
                }
            }
        });
    }
    function loadSubject(classid) {
        /* 1：如果是分类未选择加载空。根据分类加载
         * 2：根据分类获取学科，如果为一条默认选中。
         * 3：学段根据学科加载
         */
        if (classid > 0) {
            ys.ajax({
                url:  '@Url.Content("~/BusinessManage/FunRoom/GetSubjectIdzJson")' + '?classid=' + classid,
                type: 'get',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        $('#DictionaryId1005').ysComboBox({
                            data: obj.Data,
                            class: 'form-control',
                            key: 'DictionaryId',
                            value: 'DicName',
                            onChange: function () {
                                var selectid = $('#DictionaryId1005').ysComboBox('getValue');
                                loadSchoolStage(selectid);
                            }
                        });
                        if (obj.Data.length == 1) {
                            $("#DictionaryId1005").ysComboBox('setValue', obj.Data[0].DictionaryId);
                            loadSchoolStage(obj.Data[0].DictionaryId);
                        }
                    } else {
                        $('#DictionaryId1005').ysComboBox({ data: [], key: 'DictionaryId', value: 'DicName', class: 'form-control' });
                        ys.msgError(obj.Message);
                    }
                }
            });
        } else {
            $('#DictionaryId1005').ysComboBox({ data: [], key: 'DictionaryId', value: 'DicName', class: 'form-control' });
        }
    }
    function loadSchoolStage(subjectid) {
        /* 1：学段根据学科加载
       * 2：默认加载当前管理员配置的学段。
       * 3：选择分类，重新加载、选择学段在重新加载。
       * 4：如果为一条默认选中， 如果以选中，则给选中状态。
       */
        $("#DictionaryId1002").html('');
        ys.ajax({
            url: '@Url.Content("~/BusinessManage/FunRoom/GetSchoolStageIdzList")' + '?subjectid=' + subjectid,
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1) {
                    var html = '';
                    var schoolstagez = $("#SchoolStagez").val();
                    if (obj.Data.length == 1) {
                        $("#SchoolStagez").val(obj.Data[0].DictionaryId);
                        schoolstagez = obj.Data[0].DictionaryId + '';
                    }
                    for (var i = 0; i < obj.Data.length; i++) {
                        html += '<label class="check-box">';
                        if (schoolstagez.indexOf(obj.Data[i].DictionaryId) > -1) {
                            html += $.Format('<div class="icheckbox-blue checked"><input name="Schoolstage_checkbox" type="checkbox" value="{0}" style="position: absolute; opacity: 0;" checked="checked"></div>', obj.Data[i].DictionaryId);
                        } else {
                            html += $.Format('<div class="icheckbox-blue"><input name="Schoolstage_checkbox" type="checkbox" value="{0}" style="position: absolute; opacity: 0;"></div>', obj.Data[i].DictionaryId);
                        }
                        html += $.Format('{0}</label>', obj.Data[i].DicName);
                    }
                    $("#DictionaryId1002").html(html);
                    setSchoolstagez();
                    $('input[name="Schoolstage_checkbox"]').change(function () {
                        if ($(this).prop("checked")) {
                            $(this).parent(".icheckbox-blue").addClass("checked");
                        } else {
                            $(this).parent(".icheckbox-blue").removeClass("checked");
                        }
                        setSchoolstagez();
                    });
                }
                else {
                    $("#DictionaryId1002").html("");
                    ys.msgError(obj.Message);
                }
            }
        });
    }
    function setSchoolstagez() {
        var schoolstages = $('input[name="Schoolstage_checkbox"]:checked').map(function () { return this.value }).get().join(',');
        $("#SchoolStagez").val(schoolstages);
    }
    function loadUploadify() {
        $("#uploadify").uploadifive({
            'uploadScript': '/File/UploadFile?fc=1020',
            'cancelImg': '~/lib/uploadifive/uploadifive-cancel.png',
            'buttonText': '上 传',
            //'formData': { "token": "", "UserId": "0", "folder": "/UploadFile/Attachment" },
            'queueID': 'fileQueue',
            'auto': true,
            'multi': false,
            'fileDesc': '支持格式：*.jpg;*.gif;*.png;*.pdf;',
            'fileExt': '*.jpg;*.gif;*.png;*.pdf;',
            'onAddQueueItem': function (file) {

            },
            'onUploadComplete': function (fileObj, response) {
                if (typeof (response) == "string" && response != "") {
                    response = JSON.parse(response);
                    if (response.Tag == 1) {
                        ys.msgSuccess("上传成功！");
                        var id = response.Description;
                        var filePath = response.Data;
                        var fileTitle = response.Message;
                        var fileExt = filePath.substring(filePath.lastIndexOf('.')).toUpperCase();
                        if (fileExt == ".JPG" || fileExt == ".BMP" || fileExt == ".JPEG" || fileExt == ".PNG" || fileExt == ".GIF") {
                            $("#spanFile").append('<span class="keywords" idValue="' + id + '"> <a type="del" onclick="delFile(this,\'' + id + '\')"></a><a  modal="zoomImg"  href="javascript:void(0);" src="' + filePath + '" >' + fileTitle + '</a></span>');
                        } else {
                            $("#spanFile").append('<span class="keywords" idValue="' + id + '"> <a type="del" onclick="delFile(this,\'' + id + '\')"></a><a target="_blank" href="' + filePath + '" >' + fileTitle + '</a></span>');
                        }
                        imgages.showextalink();
                    } else {
                        ys.msgError(response.Message);
                    }
                }
            },
            'onFallback': function (event) {
                ys.msgError(event);
            }
        });
    }
    function getForm() {
        if (id > 0) {
            ys.ajax({
                url: '@Url.Content("~/BusinessManage/FunRoom/GetFormJson")' + '?id=' + id,
                type: 'get',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        $('#form').setWebControls(obj.Data);
                        if (isNaN(obj.Data.BuildTime) && obj.Data.BuildTime.length > 10) {
                            $("#BuildTime").val(ys.formatDate(obj.Data.BuildTime, "yyyy-MM-dd"));
                        }
                        if (isNaN(obj.Data.ReformTime) && obj.Data.ReformTime.length > 10) {
                            $("#ReformTime").val(ys.formatDate(obj.Data.ReformTime, "yyyy-MM-dd"));
                        }
                        $('#ClassTree').ysComboBoxTree("setValue", obj.Data.DictionaryId1006B);
                        $("#hidUserId").val(obj.Data.SysUserId);
                        //调用显示附件信息
                        if (isNaN(obj.Data.AttachmentList) && obj.Data.AttachmentList != undefined) {
                            var fileList = obj.Data.AttachmentList;
                            //先清空
                            var objDiv = $("#spanFile");
                            objDiv.html("");
                            if (fileList.length > 0) {
                                for (var i = 0; i < fileList.length; i++) {
                                    var attachmentid = fileList[i].Id;
                                    var filePath = fileList[i].Path;
                                    var fileExt = fileList[i].Ext.toUpperCase();
                                    var fileTitle = fileList[i].Title;
                                    if (fileExt == ".JPG" || fileExt == ".BMP" || fileExt == ".JPEG" || fileExt == ".PNG" || fileExt == ".GIF") {
                                        objDiv.append('<span class="keywords" idValue="' + attachmentid + '"> <a type="del" onclick="delFile(this,\'' + attachmentid + '\')"></a><a  modal="zoomImg"  href="javascript:void(0);" src="' + filePath + '" >' + fileTitle + '</a></span>');
                                    } else {
                                        objDiv.append('<span class="keywords" idValue="' + attachmentid + '"> <a type="del" onclick="delFile(this,\'' + attachmentid + '\')"></a><a target="_blank" href="' + filePath + '" >' + fileTitle + '</a></span>');
                                    }
                                }
                                imgages.showextalink();
                            }
                        }
                    }
                }
            });
        }
        else {
            var defaultData = {Id:0};
            $('#form').setWebControls(defaultData);
        }
    }
    function saveForm(index) {
        if ($('#form').validate().form()) {
            var postData = $('#form').getWebControls({ Id: id });
            if (!ys.isNullOrEmpty(postData.ClassTree)) {
                var arr = postData.ClassTree.toString().split(',');
                postData.DictionaryId1006A = arr[0];
                postData.DictionaryId1006B = arr[arr.length - 1];
            }
            postData.Address = ys.getLastValue(postData.Address);
            postData.SysDepartmentId = ys.getLastValue(postData.SysDepartmentId);
            var imageArr = [];
            $(".keywords").each(function () {
                var idValue = $(this).attr("idValue");
                imageArr.push(idValue);
            });
            if (imageArr.length > 0) {
                postData.Imagez = imageArr.join(',');
            }
            var errorMsg = '';
            if (!(parseInt(postData.DictionaryId1006B) > 0)) {
                errorMsg += '请选择分类。<br/>';
            }
            if (!(parseInt(postData.DictionaryId1005) > 0)) {
                errorMsg += '请选择适用学科。<br/>';
            }
            if (!(parseInt(postData.DictionaryId1002) > 0)) {
                errorMsg += '请选择适用学段。<br/>';
            }
            if (!(parseFloat(postData.UseArea) >= 0)) {
                errorMsg += '请填写使用面积，必须不小0。<br/>';
            }
            if (SaveValiDate != undefined && SaveValiDate.length > 0) {
                SaveValiDate.map(function (item, i) {
                    if (item.typecode == "Image") {
                        if (!(imageArr != undefined && imageArr.length >0)) {
                            errorMsg += (item.verifyhint + '<br/>');
                        }
                    } else if (item.typecode == "DepartmentId") {
                        if (!(parseInt(postData.SysDepartmentId) > 0)) {
                            errorMsg += (item.verifyhint + '<br/>');
                        }
                    } else if (item.typecode == "SeatNum" && AttributeType != 3) {
                        if (!(parseInt(postData.SeatNum) > 0)) {
                            errorMsg += (item.verifyhint + '<br/>');
                        }
                    } else if (item.typecode == "IsDigitalize" && AttributeType != 3) {
                        //数字化
                        if (postData.IsDigitalize != @IsStatusEnum.Yes.ParseToInt() && postData.IsDigitalize!=@IsStatusEnum.No.ParseToInt()) {
                            errorMsg += (item.verifyhint + '<br/>');
                        }
                    } else if (item.typecode == "Address") {
                        if (!(postData.Address != undefined && postData.Address.length > 0)) {
                            errorMsg += (item.verifyhint + '<br/>');
                        }
                    } else if (item.typecode == "InvestmentAmount") {
                        if (!(parseFloat(postData.InvestmentAmount)>0)) {
                            errorMsg += (item.verifyhint + '<br/>');
                        }
                    }
                });
            }

            if (errorMsg != '') {
                ys.msgError('验证失败，请填写完成再提交！<br/>' + errorMsg);
                return;
            }
            ys.ajax({
                url: '@Url.Content("~/BusinessManage/FunRoom/SaveFormJson")',
                type: 'post',
                data: postData,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        parent.$('#gridTable').ysTable('refresh');parent.resetToolbarStatus();
                        parent.layer.close(index);
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        } else {
            ys.msgError('验证失败，请填写完成再提交！');
        }
    }

     //删除附件
    function delFile(obj, value) {
        $(obj).parent().remove();
        imgages.showextalink();
    }
    /**打开心授权页面，
  * Form表单页面调用，因为表单页面不能直接打开标签页面
  */
    function openSubjectSet() {
        parent.OpenSubjectSet();
    }
   /**
     *
     * 限制文本框只能输入数字且小数点后两位
     */
    function num(obj) {
        obj.value = obj.value.replace(/[^\d.]/g, ""); //清除"数字"和"."以外的字符
        obj.value = obj.value.replace(/^\./g, ""); //验证第一个字符是数字
        obj.value = obj.value.replace(/\.{2,}/g, "."); //只保留第一个, 清除多余的
        obj.value = obj.value.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
        obj.value = obj.value.replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3'); //只能输入两个小数
    }
    
    //#region
    //加载配置验证信息
    function loadFormVali() {
        ys.ajax({
            url: '@Url.Content("~/SystemManage/ConfigSet/GetListByCode")',
            type: 'Post',
            data: { typecode:'2SYSG-1CJWH-1SYLB'},
            success: function (obj) {
                if (obj.Tag == 1) {
                    var validateRule = {};
                    var validateMessage = {};
                    validateRule.Name = { required: true };
                    validateMessage.Name = { required: '请填写名称' };
                    validateRule.UseArea = { required: true, number: true };
                    validateMessage.UseArea = { required: "请填写使用面积(㎡)。", number: '请正确填写使用面积(㎡)。' };
                    validateRule.SeatNum = { number: true };
                    validateMessage.SeatNum = { number: '请正确填写座位数（个）' };
                    validateRule.InvestmentAmount = { number: true };
                    validateMessage.InvestmentAmount = { number: '请正确填写总投入（元）' };
                    if (obj.Data != undefined && obj.Data.length > 0) {
                        for (var i = 0; i < obj.Data.length; i++) {
                            if (obj.Data[i].ConfigValue == "1") {
                                var typecode = obj.Data[i].TypeCode.replaceAll("2SYSG-1CJWH-1SYLB-", "");
                                var verifyhint = "";
                                if (obj.Data[i].VerifyHint != undefined && obj.Data[i].VerifyHint.length > 0) {
                                    verifyhint = obj.Data[i].VerifyHint;
                                }
                                $("#lab" + typecode).append('<font class="red"> *</font>');
                                if (typecode == "SeatNum") {
                                    validateRule.SeatNum = { required: true };
                                    validateMessage.SeatNum = { required: verifyhint };
                                    SaveValiDate.push({ typecode: typecode, verifyhint: verifyhint });
                                } else if (typecode == "BuildTime") {
                                    validateRule.BuildTime = { required: true };
                                    validateMessage.BuildTime = { required: verifyhint };
                                } else if (typecode == "ReformTime") {
                                    validateRule.ReformTime = { required: true };
                                    validateMessage.ReformTime = { required: verifyhint };
                                } else if (typecode == "InvestmentAmount") {
                                    validateRule.InvestmentAmount = { required: true };
                                    validateMessage.SeatNum = { required: verifyhint };
                                    SaveValiDate.push({ typecode: typecode, verifyhint: verifyhint });
                                } else if (typecode == "Address" || typecode == "Image" || typecode == "DepartmentId" || typecode == "IsDigitalize") {
                                    SaveValiDate.push({ typecode: typecode, verifyhint: verifyhint });
                                }
                            }
                        }
                    }
                    $('#form').validate({
                        rules: validateRule,
                        messages: validateMessage
                    });
                }
            }
        });
    }

    //#endregion
</script>

