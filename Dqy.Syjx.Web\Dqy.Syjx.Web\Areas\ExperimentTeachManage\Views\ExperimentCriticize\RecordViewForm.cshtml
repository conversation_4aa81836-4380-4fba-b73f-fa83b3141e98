﻿@{
    Layout = "~/Views/Shared/_Index.cshtml";
}
@inject Microsoft.AspNetCore.Hosting.IWebHostEnvironment HostingEnvironment
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/fileinput/5.0.3/css/fileinput.min.css"))
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/fileinput/5.0.3/js/fileinput.min.js"))
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/uploadifive/File.min.css"))
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/uploadifive/File.min.js"))

<style type="text/css">
    .check-box {
        width: 100px;
    }

    .iradio-box {
        width: 100px;
    }

    .iradio-box {
        position: initial;
        display: inline-block;
    }

    .iradio-blue {
        position: inherit;
        display: inline-block;
        /*  top: 6px;*/
    }

    .div-experit {
        border: 1px dashed #999;
        margin: 15px 0px 15px 0px;
        padding-top: 15px;
    }
</style>
<div class="wrapper animated fadeInRight">
    <form id="form" class="form-horizontal m">
        <div class="form-group row">
            <label class="col-sm-2 control-label " id="labExperimentSum">实验总结</label>
            <div class="col-sm-8">
                <textarea id="experimentSummary" col="ExperimentSummary" class="form-control" placeholder="如实验内容、效果、改进意见等（限1000字）"></textarea>
            </div>
        </div>
        <div class="form-group row" id="divImageFile">
            <label class="col-sm-2 control-label " id="labImage">
                现场照片
            </label>
            <div class="col-sm-5">
                <div id="spanFile" style="padding: 10px 0px;"></div>
            </div>
        </div>
        <div class="form-group run-info">
            <label class="col-sm-2 control-label ">设备运行</label>
            <div class="col-sm-8">
                <div id="runStatuz"></div>
            </div>
        </div>
        <div class="form-group run-info" id="divProblem" style="display:none;">
            <label class="col-sm-2 control-label ">问题描述</label>
            <div class="col-sm-8">
                <textarea id="problemDesc" col="ProblemDesc" class="form-control"></textarea>
            </div>
        </div>
    </form>
</div>


<script type="text/javascript">
    var id = ys.request("id");
    var Com_BookingId = ys.request("bookingid");
    $(function () {
        $(".inputprompt").popover({
            trigger: 'hover',
            html: true
        });
        parent.removeConfirm();
        getForm();
    });

    function getForm() {
        if (id > 0) {
            ys.ajax({
                url: '@Url.Content("~/ExperimentTeachManage/ExperimentCriticize/GetRecordJson")' + '?id=' + id + '&bookingid=' + Com_BookingId,
                type: 'get',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        obj.Data.ClassTime = ys.formatDate(obj.Data.ClassTime, "yyyy-MM-dd");
                        $('#baseForm').setWebControls(obj.Data);

                        var schoolyearterm = (obj.Data.SchoolYearStart + '').substr(2) + '~' + (obj.Data.SchoolYearEnd + '').substr(2);
                        if (@SchoolTermEnum.NextSemester.ParseToInt()== obj.Data.SchoolTerm) {
                            schoolyearterm += '@SchoolTermEnum.NextSemester.GetDescription()';
                        } else {
                            schoolyearterm += '@SchoolTermEnum.LastSemester.GetDescription()';
                        }
                        $("#SchoolYearStartTerm").val(schoolyearterm);
                        loadStatuz();
                        if (obj.ExtendData){
                            if (obj.ExtendData.ExperimentSummary && obj.ExtendData.ExperimentSummary.length > 0) {
                                $("#experimentSummary").val(obj.ExtendData.ExperimentSummary);
                            }

                            statuzChange(obj.ExtendData.RunStatuz);
                            if (obj.ExtendData.RunStatuz && obj.ExtendData.RunStatuz == 2) {
                                $("#divProblem").show();
                                console.log(obj.ExtendData.ProblemDesc);
                                if (obj.ExtendData.ProblemDesc && obj.ExtendData.ProblemDesc.length > 0) {
                                    console.log(obj.ExtendData.ProblemDesc);
                                    $("#problemDesc").val(obj.ExtendData.ProblemDesc);
                                }
                            }
                            if (obj.ExtendData.AttachmentList && obj.ExtendData.AttachmentList.length > 0) {
                                for (var i = 0; i < obj.ExtendData.AttachmentList.length; i++) { 

                                }
                            }

                            //调用显示附件信息
                            if (isNaN(obj.ExtendData.AttachmentList) && obj.ExtendData.AttachmentList != undefined && obj.ExtendData.AttachmentList.length > 0) {
                                var fileList = obj.ExtendData.AttachmentList;
                                //先清空
                                var objDiv = $("#spanFile");
                                objDiv.html("");
                                if (fileList.length > 0) {
                                    for (var i = 0; i < fileList.length; i++) {
                                        var attachmentid = fileList[i].Id;
                                        var filePath = fileList[i].Path;
                                        var fileExt = fileList[i].Ext.toUpperCase();
                                        var fileTitle = fileList[i].Title;
                                        if (fileExt == ".JPG" || fileExt == ".BMP" || fileExt == ".JPEG" || fileExt == ".PNG" || fileExt == ".GIF") {
                                            objDiv.append('<span class="keywords" idValue="' + attachmentid + '"><a  modal="zoomImg"  href="javascript:void(0);" src="' + filePath + '" >' + fileTitle + '</a></span>');
                                        } else {
                                            objDiv.append('<span class="keywords" idValue="' + attachmentid + '"></a><a target="_blank" href="' + filePath + '" >' + fileTitle + '</a></span>');
                                        }
                                    }
                                    imgages.showextalink();
                                }
                            }
                        }



                    }
                }
            });
        }
    }

    //#region 运行状态
    function loadStatuz() {
        var html = '';
        html += '<label class="iradio-box"><div class="iradio-blue single" style="vertical-align:bottom;"><input name="inputType_checkbox" type="radio" value="1" style="position: absolute; opacity: 0;"></div> 正常</label>';
        html += '<label class="iradio-box"><div class="iradio-blue batch" style="vertical-align:bottom;"><input name="inputType_checkbox" type="radio" value="2" style="position: absolute; opacity: 0;"></div> 不正常</label>';
        $("#runStatuz").html(html);
        $(".single").addClass("checked");
        $('#divRemark').hide();
        $('input[name="inputType_checkbox"]').change(function () {
            if ($(this).prop("checked")) {
                var val1 = $(this).val();
                statuzChange(val1);
            }
        });
    }
    function statuzChange(selectid) {
        if (selectid == 1) {
            $(".single").addClass("checked");
            $(".batch").removeClass("checked");
            $('#divRemark').hide();
        } else {
            $(".batch").addClass("checked");
            $(".single").removeClass("checked");
            $('#divRemark').show();
        }
    }
    //#endregion

</script>

