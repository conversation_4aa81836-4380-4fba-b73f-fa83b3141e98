﻿@{
    Layout = "~/Views/Shared/_FormWhite.cshtml";
}
@inject Microsoft.AspNetCore.Hosting.IWebHostEnvironment HostingEnvironment
@section header {
    <link href="@Url.Content("~/lib/summernote/0.8.15/summernote.min.css")" rel="stylesheet" type="text/css">
    <script src='@Url.Content("~/lib/summernote/0.8.15/summernote.js")' type="text/javascript"></script>
    <script src='@Url.Content("~/lib/summernote/0.8.15/lang/summernote-zh-CN.min.js")' type="text/javascript"></script>

    @BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/imageupload/1.0/css/imgup.min.css"))
    @BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/imageupload/1.0/js/imgup.min.js"))
    @BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/bootstrap.tagsinput/0.8.0/bootstrap-tagsinput.min.css"))
    @BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/bootstrap.tagsinput/0.8.0/bootstrap-tagsinput.min.js"))
    @BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/uploadifive/File.min.css"))
    @BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/uploadifive/File.min.js"))
}
<style>
    #divDetailList .row {
        margin-top: 10px;
    }
</style>
<div class="container-div">
    <div class="btn-group-sm hidden-xs" id="toolbar">
    </div>
    <div class="col-sm-12 select-table table-striped">

        <div class="ibox-title">
            <h5 id="hTitle">发布资讯</h5>
        </div>

        <div class="card-body">
            <form id="form" class="form-horizontal m">
                <div class="form-group row">
                    <label class="col-sm-3 control-label ">分类<font class="red"> *</font></label>
                    <div class="col-sm-9">
                        <div id="Cid" col="Cid"></div>
                    </div>
                </div>
                <div class="form-group row">
                    <label class="col-sm-3 control-label ">标题<font class="red"> *</font></label>
                    <div class="col-sm-9">
                        <input id="Title" col="Title" type="text" class="form-control" />
                    </div>
                </div>
                <div class="form-group row">
                    <label class="col-sm-3 control-label ">资讯短标题<font class="red"> *</font></label>
                    <div class="col-sm-9">
                        <input id="ShortTitle" col="ShortTitle" type="text" class="form-control" />
                    </div>
                </div>

                <div class="form-group row">
                    <label class="col-sm-3 control-label ">封面图片</label>
                    <div class="col-sm-7">
                        <input id="ImageUrl" col="ImageUrl" type="text" class="form-control" readonly />
                    </div>
                    <div class="col-sm-2">
                        <input type="file" name="uploadifyFileImageUrl" id="uploadifyFileImageUrl" />
                        <div style="height: 55px;display:none;" id="fileImgUrlFile"></div>
                    </div>
                </div>

                <div class="form-group row">
                    <label class="col-sm-3 control-label ">附件</label>
                    <div class="col-sm-7">
                        <input id="Attachment" col="Attachment" type="text" class="form-control" readonly />
                    </div>
                    <div class="col-sm-2">
                        <input type="file" name="uploadifyFileAttachment" id="uploadifyFileAttachment" />
                        <div style="height: 55px;display:none;" id="fileAttachmentFile"></div>
                    </div>
                </div>

                <div class="form-group row">
                    <label class="col-sm-3 control-label ">排序值<font class="red"> *</font></label>
                    <div class="col-sm-9">
                        <input id="Sort" col="Sort" type="text" class="form-control" />
                    </div>
                </div>

                <div class="form-group row">
                    <label class="col-sm-3 control-label ">详情</label>
                    <div class="col-sm-9">
                        <input class="btn btn-secondary" type="button" value=" 添 加 " onclick="saveContentForm();" />
                        <div id="divDetailList" style="margin-top:10px;">
                        </div>
                    </div>
                </div>
                <div class="form-group row">
                    <label class="col-sm-3 control-label ">是否登录时弹窗展示</label>
                    <div class="col-sm-8" id="IsLoginShow" col="IsLoginShow">
                    </div>
                </div>
            </form>


        </div>
        <div class="btn-group-sm hidden-xs" id="toolbar" style="text-align: center;">
            <a id="btnAdd" class="btn btn-info" onclick="saveForm(@ArticleStatuzEnum.Save.ParseToInt());"><i class="fa fa-edit"></i> 保存</a>

            <a id="btnAdd" class="btn btn-danger" onclick="saveForm(@ArticleStatuzEnum.Publish.ParseToInt());"><i class="fa fa-edit"></i> 发布</a>
        </div>
    </div>
</div>


<script type="text/javascript">
    var id = ys.request("id");
    $(function () {

        if (id == null) {
            id = 0;
            $("#hTitle").html("发布资讯");
        }
        var isStatuz=[{Value:"是",Key:true},{Value:"否",Key:false}];
        $("#IsLoginShow").ysRadioBox({ data: isStatuz, defaultValue: false });
        getForm();

        $('#form').validate({
            rules: {
                Cid: { required: true },
                Title: { required: true },
                ShortTitle: { required: true },
                Sort: { required: true, digits: true, max: 9999 }
            }
        });

        $("#uploadifyFileImageUrl").uploadifive({
            'uploadScript': '/File/UploadFile?fc=8001',
            'cancelImg': '~/lib/uploadifive/uploadifive-cancel.png',
            'buttonText': '上 传',
            'queueID': 'fileImgUrlFile',
            'auto': true,
            'multi': true,
            'fileDesc': '支持格式：*.jpg;*.gif;*.png;',
            'fileExt': '*.jpg;*.gif;*.png;',
            'onAddQueueItem': function (file) {

            },
            'onUploadComplete': function (fileObj, response) {
                if (typeof (response) == "string" && response != "") {
                    response = JSON.parse(response);
                    if (response.Tag == 1) {
                        ys.msgSuccess("上传成功！");
                        var filePath = response.Data;
                        $("#ImageUrl").val(filePath);
                    } else {
                        ys.msgError(response.Message);
                    }
                }
            },
            'onFallback': function (event) {
                ys.msgError(event);
            }
        });

        $("#uploadifyFileAttachment").uploadifive({
            'uploadScript': '/File/UploadFile?fc=8002',
            'cancelImg': '~/lib/uploadifive/uploadifive-cancel.png',
            'buttonText': '上 传',
            'queueID': 'fileAttachmentFile',
            'auto': true,
            'multi': true,
            'fileDesc': '支持格式：*.jpg;*.gif;*.png;',
            'fileExt': '*.jpg;*.gif;*.png;',
            'onAddQueueItem': function (file) {

            },
            'onUploadComplete': function (fileObj, response) {
                if (typeof (response) == "string" && response != "") {
                    response = JSON.parse(response);
                    if (response.Tag == 1) {
                        ys.msgSuccess("上传成功！");
                        var filePath = response.Data;
                        $("#Attachment").val(filePath);
                    } else {
                        ys.msgError(response.Message);
                    }
                }
            },
            'onFallback': function (event) {
                ys.msgError(event);
            }
        });
    });

    function loadCategory(pid) {
         $('#Cid').ysComboBox({ class: 'form-control'});
        ys.ajax({
            url: '@Url.Content("~/ArticleManager/ArticleCategory/GetListJson?Pid=0")',
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1) {
                    $('#Cid').ysComboBox({ data: obj.Data, class: 'form-control', key: 'Id', value: 'Name' });
                    if (pid > 0) {
                        $('#Cid').ysComboBox('setValue', pid);
                    } else {
                        $('#Cid').ysComboBox('setValue', "");
                    }
                } else {
                    $('#Cid').ysComboBox({ data: [], class: 'form-control', key: 'Id', value: 'Name' });
                }
            }
        });
    }

    function getForm() {
        if (id > 0) {
            $("#hTitle").html("修改资讯");
            ys.ajax({
                url: '@Url.Content("~/ArticleManager/Article/GetFormJson")' + '?id=' + id,
                type: 'get',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        loadCategory(obj.Data.Cid);
                        console.log("-----aa------:",obj.Data);
                        $('#form').setWebControls(obj.Data);
                        var listContent = obj.Data.listContent;
                        if (listContent != "") {
                            $.each(listContent, function (index, element) {
                                if (element.ImageSrc != "") {
                                    html = $.Format('<div class="row" id="div_{0}"><div class="col-sm-4"><a  modal="zoomImg" href="javascript:void(0);" src="{1}" ><img src="{1}" class="img-rounded" style="width:300px; height:200px;"/></a></div><div class="col-sm-6">{2}</div><div class="col-sm-2"><input class="btn btn-secondary" type="button" id="{0}" onclick="del(this)" value="删除" />&nbsp;&nbsp;<input class="btn btn-danger" type="button" id="{0}" onclick="edit(this)" value="修改" /></div></div >', element.Id
                                        , element.ImageSrc, element.Remark);
                                } else {
                                    html = $.Format('<div class="row" id="div_{0}"><div class="col-sm-4"></div><div class="col-sm-6">{1}</div><div class="col-sm-6"><input class="btn btn-secondary" id="{0}" onclick="del(this)" type="button" value="删除" />&nbsp;&nbsp;<input class="btn btn-danger" id="{0}" onclick="edit(this)" type="button" value="修改" /></div></div >', element.Id
                                        , element.Remark);
                                }
                                $("#divDetailList").append(html);
                            });

                            imgages.showextalink();
                        }

                    }
                }
            });
        }
        else {
            var defaultData = {};
            $('#form').setWebControls(defaultData);
            $('#IsLoginShow').ysRadioBox('setValue', false);
            loadCategory(0);
        }
    }

    function saveContentForm() {
        if ($('#form').validate().form()) {
            var postData = $('#form').getWebControls({ Id: id });
            if (postData.Cid == "") {
                ys.msgError("请选择分类");
                return;
            }
            postData.Statuz = @ArticleStatuzEnum.Save.ParseToInt();
             ys.ajax({
                url: '@Url.Content("~/ArticleManager/Article/SaveFormJson")',
                type: 'post',
                data: postData,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        id = obj.Data;
                         ys.openDialog({
                            title: "添加",
                             content: '@Url.Content("~/ArticleManager/Article/ArticleContextForm")' + '?id=0&articleId=' + id,
                            width: '768px',
                            height: '550px',
                            callback: function (index, layero) {
                                var iframeWin = window[layero.find('iframe')[0]['name']];
                                iframeWin.saveForm(index);
                            }
                         });
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });

        }
    }

    function saveForm(statuz) {

        if ($('#form').validate().form()) {
            var postData = $('#form').getWebControls({ Id: id });
            if (postData.Cid == "") {
                ys.msgError("请选择分类");
                return;
            }
            postData.Statuz = statuz;
            ys.ajax({
                url: '@Url.Content("~/ArticleManager/Article/SaveFormJson")',
                type: 'post',
                data: postData,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);

                        var menuUrl = "@Url.Content("~/ArticleManager/Article/ArticleIndex")";
                        createMenuAndCloseCurrent(menuUrl, "资讯列表");
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        }


    }


    function del(obj) {

        ys.confirm('您确定要删除吗？', function () {
            var delId = $(obj).attr("id");
            var postData = { ids: delId };
            ys.ajax({
                    url: '@Url.Content("~/ArticleManager/ArticleContext/DeleteFormJson")',
                    type: 'post',
                    data: postData,
                    success: function (obj) {
                        if (obj.Tag == 1) {
                            ys.msgSuccess(obj.Message);
                            $("#div_" + delId).remove();
                        }
                        else {
                            ys.msgError(obj.Message);
                        }
                    }
             });
        });

    }

    function edit(obj) {
        var editId = $(obj).attr("id");
        ys.openDialog({
            title: "修改",
            content: '@Url.Content("~/ArticleManager/Article/ArticleContextForm")' + '?id=' + editId+'&articleId=' + id,
            width: '768px',
            height: '550px',
            callback: function (index, layero) {
                var iframeWin = window[layero.find('iframe')[0]['name']];
                iframeWin.saveForm(index);
            }
        });
    }


</script>

