﻿@{
    Layout = "~/Views/Shared/_FormWhite.cshtml";
}
@inject Microsoft.AspNetCore.Hosting.IWebHostEnvironment HostingEnvironment
@section header{
    <link href="@Url.Content("~/lib/summernote/0.8.15/summernote.min.css")" rel="stylesheet" type="text/css">
    <script src='@Url.Content("~/lib/summernote/0.8.15/summernote.js")' type="text/javascript"></script>
    <script src='@Url.Content("~/lib/summernote/0.8.15/lang/summernote-zh-CN.min.js")' type="text/javascript"></script>

    @BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/imageupload/1.0/css/imgup.min.css"))
    @BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/imageupload/1.0/js/imgup.min.js"))

    @BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/bootstrap.tagsinput/0.8.0/bootstrap-tagsinput.min.css"))
    @BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/bootstrap.tagsinput/0.8.0/bootstrap-tagsinput.min.js"))
}
<link href="~/lib/uploadifive/uploadifive.css" rel="stylesheet" />
<script src="~/lib/uploadifive/jquery.uploadifive.js"></script>

<div class="wrapper animated fadeInRight">
    <form id="form" class="form-horizontal m">
        <div class="form-group row">
            <label class="col-sm-3 control-label ">浏览文件</label>
            <div class="col-sm-8">
                <input type="hidden" id="AttachmentId" value="0" />
                <input id="hFileType" name="FileType" type="hidden" />
                <input type="file" name="uploadify" id="uploadify" />
                <input id="hImageSrc" name="Path" type="hidden" />
                <input id="hFileName" name="FileName" type="hidden" />
                <span id="spanLink"></span>
            </div>
        </div>

        <div class="form-group row">
            <label class="col-sm-3 control-label "> </label>
            <div class="col-sm-8">
                <div style="width: 300px; height: 55px;" id="fileQueue"></div>
            </div>
        </div>
    </form>

</div>

<script type="text/javascript">
    var id = ys.request("id");
    console.log("上传附件页面Id:" + id);
    $(function () {

        loadUploadify();
    });

    function loadUploadify() {
        $("#uploadify").uploadifive({
            'uploadScript': '/File/UploadFile?fc=1100',
            'cancelImg': '~/lib/uploadifive/uploadifive-cancel.png',
            'buttonText': '上 传',
            //'formData': { "token": "", "UserId": "0", "folder": "/UploadFile/Attachment" },
            'queueID': 'fileQueue',
            'auto': true,
            'multi': false,
            'fileDesc': '支持格式：*.jpg;*.gif;*.png;',
            'fileExt': '*.jpg;*.gif;*.png;',
            'onAddQueueItem': function (file) {

            },
            'onUploadComplete': function (fileObj, response) {
                if (typeof (response) == "string" && response != "") {
                    response = JSON.parse(response);
                    console.log("response:" + JSON.stringify(response));
                    if (response.Tag == 1) {
                        $('#spanLink').text('上传成功，请单击保存按钮，保存文件。');
                        $('#AttachmentId').val(response.Description);
                        $('#Path').val(response.Data);
                        //$('#imgAward').attr("src", response.Data);
                        ys.msgSuccess("上传成功！");
                    } else {
                        $('#spanLink').text(response.Message);
                        ys.msgError(response.Message);
                    }
                }
            },
            'onFallback': function (event) {
                ys.msgError(event);
            }
        });
    }


     function saveForm(index) {
         if (id > 0) {
             var attachmentId = $("#AttachmentId").val();
             if (attachmentId == 0) {
                 ys.msgError("请先上传附件后再保存");
                 return;
             }
             var postData = { UserTrainInfoId: id, AttachmentId: attachmentId};
             ys.ajax({
                url: '@Url.Content("~/PersonManage/UserTrainInfo/EditTrainFile")',
                type: 'post',
                data: postData,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        parent.document.getElementById("id").value
                        parent.getForm();
                        parent.layer.close(index);
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });


         } else {
             ys.msgError("请保存信息后再上传附件");
         }
    }
</script>