﻿@{
    Layout = "~/Views/Shared/_FormWhite.cshtml";
}
@inject Microsoft.AspNetCore.Hosting.IWebHostEnvironment HostingEnvironment
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/uploadifive/File.min.css"))
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/uploadifive/File.min.js"))
<style type="text/css">
    .tag_msg_color {
        color: #999;
    }
</style>
<div class="wrapper animated fadeInRight">
    <form id="form" class="form-horizontal m">
        <input type="hidden" id="Id" col="Id" value="0" />
        <div class="form-group row">
            <label class="col-sm-3 control-label ">类别<font class="red"> *</font></label>
            <div class="col-sm-7">
                <div id="AwardCategory" col="AwardCategory"></div>
            </div>
            <div class="col-sm-2">
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">等级<font class="red"> *</font></label>
            <div class="col-sm-7">
                <div id="AwardLevel" col="AwardLevel"></div>
            </div>
            <div class="col-sm-2">
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">获奖日期<font class="red"> *</font></label>
            <div class="col-sm-7">
                <input id="AwardDate" col="AwardDate" type="text" class="form-control" autocomplete="off" />
            </div>
            <div class="col-sm-2">
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">备注</label>
            <div class="col-sm-7">
                <textarea id="Remark" col="Remark" class="form-control" style="max-width:100%" autocomplete="off"></textarea>
            </div>
            <div class="col-sm-2 tag_msg_color">
                <span class="glyphicon glyphicon-question-sign titleprompt inputprompt" aria-hidden="true" data-container="body" data-toggle="tooltip" data-content="可填写获奖等次，如：一等奖"></span>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">获奖证书<font class="red"> *</font></label>
            <div class="col-sm-7">
                <input type="file" name="uploadify" id="uploadify" />
                <div style="height: 55px;display:none;" id="fileQueue"></div>
                <input id="AttachmentId" col="AttachmentId" type="hidden" />
                <input id="Path" col="Path" type="hidden" />
                <div id="spanFile" style="padding: 10px 0px;"></div>
                <div style="height: 55px;display:none;" id="fileQueue"></div>
                <a modal="zoomImg" href="javascript:void(0);" id="awardSrc">
                    <img modal="zoomImg" href="javascript:void(0);" src="" id="imgAward" class="col-sm-6" style="padding: 10px 0px;" />
                </a>
            </div>
            <div class="col-sm-2 tag_msg_color">
                <span class="glyphicon glyphicon-question-sign titleprompt inputprompt" aria-hidden="true" data-container="body" data-toggle="tooltip" data-content="扫描件，支持PDF和图片格式"></span>
            </div>
        </div>
    </form>
</div>

<script type="text/javascript">
    var id = ys.request("id");
    $(function () {
        $(".inputprompt").popover({
            trigger: 'hover',
            placement: 'left',
            html: true
        });
        $('#AwardCategory').ysComboBox({ url: '@Url.Content("~/SystemManage/StaticDictionary/GetListJson")' + "?TypeCode=1007", class:'form-control', key: 'DictionaryId', value: 'DicName' });
        $('#AwardLevel').ysComboBox({ url: '@Url.Content("~/SystemManage/StaticDictionary/GetListJson")' + "?TypeCode=1008", class: 'form-control', key: 'DictionaryId', value: 'DicName' });
        laydate.render({ elem: '#AwardDate', format: 'yyyy-MM-dd', trigger: 'click', position: 'fixed'});
        getForm();

        $('#form').validate({
            rules: {
                AwardDate: { required: true }
            }
        });
        loadUploadify();
    });
    function loadUploadify() {
        $("#uploadify").uploadifive({
            'uploadScript': '/File/UploadFile?fc=1001',
            'cancelImg': '~/lib/uploadifive/uploadifive-cancel.png',
            'buttonText': '上 传',
            //'formData': { "token": "", "UserId": "0", "folder": "/UploadFile/Attachment" },
            'queueID': 'fileQueue',
            'auto': true,
            'multi': false,
            'fileDesc': '支持格式：*.pdf;*.jpg;*.gif;*.png;',
            'fileExt': '*.pdf;*.jpg;*.gif;*.png;',
            'onAddQueueItem': function (file) {

            },
            'onUploadComplete': function (fileObj, response) {
                if (typeof (response) == "string" && response != "") {
                    response = JSON.parse(response); 
                    if (response.Tag == 1) {
                        ys.msgSuccess("上传成功！");
                        $('#AttachmentId').val(response.Description);
                        $('#Path').val(response.Data);
                        var id = response.Description;
                        var filePath = response.Data;
                        var fileTitle = response.Message;
                        var fileExt = filePath.substring(filePath.lastIndexOf('.')).toUpperCase();
                        //清空上次的值
                        $('#imgAward').attr("src", "");
                        $("#awardSrc").attr("src", "");
                        $("#spanFile").html("");
                        if (fileExt == ".JPG" || fileExt == ".BMP" || fileExt == ".JPEG" || fileExt == ".PNG" || fileExt == ".GIF") {
                            $('#imgAward').attr("src", response.Data);
                            $("#awardSrc").attr("src", response.Data);
                        } else {
                            $("#spanFile").append('<span class="keywords" idValue="' + id + '"><a target="_blank" href="' + filePath + '" >' + fileTitle + '</a></span>');
                        }
                        imgages.showextalink();
                    } else {
                        ys.msgError(response.Message);
                    }
                }
            },
            'onFallback': function (event) {
                ys.msgError(event);
            }
        });
    }
    function getForm() {
        if (id > 0) {
            ys.ajax({
                url: '@Url.Content("~/PersonManage/UserAwardInfo/GetFormJson")' + '?id=' + id,
                type: 'get',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        $('#form').setWebControls(obj.Data);
                        $("#AwardDate").val('');
                        if (isNaN(obj.Data.AwardDate) && obj.Data.AwardDate.length > 10) {
                            $("#AwardDate").val(obj.Data.AwardDate.substring(0, 10));
                        }
                        var attachmentid = obj.Data.AttachmentId;
                        var filePath = obj.Data.Path;
                        var fileExt = obj.Data.Ext.toUpperCase();
                        var fileTitle = obj.Data.Title;
                        if (fileExt == ".JPG" || fileExt == ".BMP" || fileExt == ".JPEG" || fileExt == ".PNG" || fileExt == ".GIF") {
                            $("#imgAward").attr("src", obj.Data.Path);
                            $("#awardSrc").attr("src", obj.Data.Path);
                            imgages.showextalink();
                        } else {
                            $("#spanFile").append('<span class="keywords" idValue="' + attachmentid + '"><a target="_blank" href="' + filePath + '" >' + fileTitle + '</a></span>');
                        }
                    }
                }
            });
        }
        else {
            var defaultData = { Id: 0 };
            $("#imgAward").attr("src", "");
            $('#form').setWebControls(defaultData);
        }
    }

    function saveForm(index) {
        if ($('#form').validate().form()) {
            var postData = $('#form').getWebControls({ Id: id });
            var msg = '';
            if (!(postData.AwardCategory != undefined && parseInt(postData.AwardCategory) > 0)) {
                msg += '请选择获奖类别！<br/>';
            }
            if (!(postData.AwardLevel != undefined && parseInt(postData.AwardLevel) > 0)) {
                msg += '请选择获奖等级！<br/>';
            }
            if (!(postData.AttachmentId != undefined && parseInt(postData.AttachmentId) > 0)) {
                msg += '请上传获奖证书！<br/>';
            }
            if (msg != '') {
                ys.msgError("验证失败<br/>"+msg);
                return;
            }
            ys.ajax({
                url: '@Url.Content("~/PersonManage/UserAwardInfo/SaveFormJson")',
                type: 'post',
                data: postData,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        parent.$('#gridTable').ysTable('refresh');parent.resetToolbarStatus();
                        parent.layer.close(index);
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        }
    }
</script>

