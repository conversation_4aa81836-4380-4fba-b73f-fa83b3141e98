﻿@{
    Layout = "~/Views/Shared/_FormWhite.cshtml";
}

<div class="wrapper animated fadeInRight">
    <form id="form" class="form-horizontal m">
        <div class="form-group row">
            <label class="col-sm-3 control-label ">状态</label>
            <div class="col-sm-6">
                <div id="Statuz" col="Statuz"></div>
            </div>
            <div class="col-sm-3">
            </div>
        </div> 
    </form>
</div>

<script type="text/javascript">
    $(function () {
        $("#Statuz").ysRadioBox({ data: ys.getJson(@Html.Raw(typeof(StatusEnum).EnumToDictionaryString()))});
    });
    function saveForm(index) {
        if ($('#form').validate().form()) {
            var postData = $('#form').getWebControls();
            if (postData.Statuz != 1 && postData.Statuz != 2) {
                ys.msgError("请选择状态，再提交。");
                return;
            }
            postData = { ids: parent.getSelectIds(), standardid: parent.id, statuz: postData.Statuz };
            ys.ajax({
                url: '@Url.Content("~/EvaluateManage/InstrumentEvaluateList/SetStatuzFormJson")',
                type: 'post',
                data: postData,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        parent.$('#gridTable').ysTable('refresh');parent.resetToolbarStatus();
                        parent.layer.close(index);
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        }
    }
</script>

