﻿@{
    Layout = "~/Views/Shared/_FormWhite.cshtml";
}
@inject Microsoft.AspNetCore.Hosting.IWebHostEnvironment HostingEnvironment
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/uploadifive/File.min.css"))
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/uploadifive/File.min.js"))
@*<style>
    #select2-checkResult_select-container{
        color:lightgray;
    }
</style>*@
<style type="text/css">
    .select2-container {
        width: 100% !important;
    }

    .select-list li input {
        width: 100% !important;
    }
</style>
<div class="wrapper animated fadeInRight">
    <form id="form" class="form-horizontal m">
        <div class="form-group row" style="display:none;">
            <label class="col-sm-3 control-label ">名称<font class="red"> *</font></label>
            <div class="col-sm-8">
                <div id="FunRoomId" col="FunRoomId"></div>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">排查人<font class="red"> *</font></label>
            <div class="col-sm-8">
                <div id="checkUserId" col="CheckUserId"></div>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">排查时间<font class="red"> *</font></label>
            <div class="col-sm-8">
                <input id="checkTime" col="CheckTime" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">排查结果<font class="red"> *</font></label>
            <div class="col-sm-8">
                <div id="checkResult" col="CheckResult"></div>
            </div>
        </div>
        <div id="divInfo" style="display:none;">
            <div class="form-group row">
                <label class="col-sm-3 control-label ">
                    <span class="glyphicon glyphicon-question-sign titleprompt inputprompt" aria-hidden="true" data-container="body" data-toggle="tooltip" data-placement="right"
                          data-content="请根据线下排查清单进行填写"></span>问题隐患
                </label>
                <div class="col-sm-8">
                    <textarea id="problemDanger" col="ProblemDanger" class="form-control" style="height:60px"></textarea>
                </div>
            </div>
            <div class="form-group row">
                <label class="col-sm-3 control-label ">是否整改</label>
                <div class="col-sm-8">
                    <div id="isRectification" col="IsRectification"></div>
                </div>
            </div>
            <div class="form-group row">
                <label class="col-sm-3 control-label ">整改时间</label>
                <div class="col-sm-8">
                    <input id="rectificationTime" col="RectificationTime" type="text" class="form-control" />
                </div>
            </div>
        </div>

        <div class="form-group row">
            <label class="col-sm-3 control-label ">
                <span class="glyphicon glyphicon-question-sign titleprompt inputprompt" aria-hidden="true" data-container="body" data-toggle="tooltip" data-placement="right"
                      data-content="文件小于5M，支持pdf"></span>
                排查清单扫描件<font class="red"> *</font>
            </label>
            <div class="col-sm-9">
                <div style="float:left;">
                    <input type="file" name="uploadifyContent" id="uploadifyContent" />
                    <div style="height: 55px;display:none;" id="fileQueueContent"></div>
                </div>
                &nbsp;&nbsp;
                <div id="spanFile_1701" style="float: left; padding-left: 10px;">
                </div>
            </div>
        </div>


        <div class="form-group row">
            <label class="col-sm-3 control-label "id="labImage">
                <span class="glyphicon glyphicon-question-sign titleprompt inputprompt" aria-hidden="true" data-container="body" data-toggle="tooltip" data-placement="right"
                      data-content="文件小于5M，支持pdf和图片文件"></span>
                现场照片
            </label>
            <div class="col-sm-9">
                <div style="float:left;">
                    <input type="file" name="uploadifyScene" id="uploadifyScene" />
                    <div style="height: 55px;display:none;" id="fileQueueScene"></div>
                </div>
                &nbsp;&nbsp;
                <div id="spanFile_1702" style="float: left; padding-left: 10px;">
                </div>
            </div>
        </div>
    </form>
</div>

<script type="text/javascript">
    var id = ys.request("id");
    var SaveValiDate = [];
    $(function () {
        $(".inputprompt").popover({
            trigger: 'hover',
            placement: 'right',
            html: true
        });
        $("#checkResult").ysComboBox({
            data: ys.getJson(@Html.Raw(typeof(CheckResultEnum).EnumToDictionaryString())),
            class: 'form-control',
            onChange: function () {
                var result = $('#checkResult').ysComboBox('getValue');
                if (result == 2) {
                    $("#divInfo").show();
                } else {
                    $("#divInfo").hide();
                }
            }
        });

        $("#isRectification").ysComboBox({
            data: ys.getJson(@Html.Raw(typeof(IsEnum).EnumToDictionaryString())),
            class: 'form-control'
        });

        laydate.render({ elem: '#checkTime', format: 'yyyy-MM-dd', trigger: 'click', position: 'fixed'});
        laydate.render({ elem: '#rectificationTime', format: 'yyyy-MM-dd', trigger: 'click', position: 'fixed' });

        $("#uploadifyContent").uploadifive({
            'uploadScript': '/File/UploadFile?fc=1701',
            'cancelImg': '~/lib/uploadifive/uploadifive-cancel.png',
            'buttonText': '上 传',
            'queueID': 'fileQueueContent',
            'auto': true,
            'multi': true,
            'fileDesc': '支持格式：*.jpg;*.gif;*.png;',
            'fileExt': '*.jpg;*.gif;*.png;',
            'onAddQueueItem': function (file) {

            },
            'onUploadComplete': function (fileObj, response) {
                if (typeof (response) == "string" && response != "") {
                    response = JSON.parse(response);
                    if (response.Tag == 1) {

                        $('#spanLinkManager').text('上传成功，请单击保存按钮，保存文件。');
                        ys.msgSuccess("上传成功！");

                        var id = response.Description;
                        var filePath = response.Data;
                        var fileTitle = response.Message;
                        var fileExt = filePath.substring(filePath.lastIndexOf('.')).toUpperCase();
                        if (fileExt == ".JPG" || fileExt == ".BMP" || fileExt == ".JPEG" || fileExt == ".PNG" || fileExt == ".GIF") {
                            $("#spanFile_1701").append('<span class="keywords" idValue="' + id+'"> <a type="del" onclick="delFile(this,\'' + id + '\')"></a><a  modal="zoomImg"  href="javascript:void(0);" src="' + filePath + '" >' + fileTitle + '</a></span>');
                        } else {
                            $("#spanFile_1701").append('<span class="keywords" idValue="' + id +'"> <a type="del" onclick="delFile(this,\'' + id + '\')"></a><a target="_blank" href="' + filePath + '" >' + fileTitle + '</a></span>');
                        }
                        imgages.showextalink();
                    } else {
                        $('#spanLinkManager').text(response.Message);
                        ys.msgError(response.Message);
                    }
                }
            },
            'onFallback': function (event) {
                ys.msgError(event);
            }
        });

        $("#uploadifyScene").uploadifive({
            'uploadScript': '/File/UploadFile?fc=1702',
            'cancelImg': '~/lib/uploadifive/uploadifive-cancel.png',
            'buttonText': '上 传',
            'queueID': 'fileQueueScene',
            'auto': true,
            'multi': true,
            'fileDesc': '支持格式：*.jpg;*.gif;*.png;',
            'fileExt': '*.jpg;*.gif;*.png;',
            'onAddQueueItem': function (file) {

            },
            'onUploadComplete': function (fileObj, response) {
                if (typeof (response) == "string" && response != "") {
                    response = JSON.parse(response);
                    if (response.Tag == 1) {

                        $('#spanLinkExamine').text('上传成功，请单击保存按钮，保存文件。');
                        ys.msgSuccess("上传成功！");

                        var id = response.Description;
                        var filePath = response.Data;
                        var fileTitle = response.Message;
                        var fileExt = filePath.substring(filePath.lastIndexOf('.')).toUpperCase();
                        if (fileExt == ".JPG" || fileExt == ".BMP" || fileExt == ".JPEG" || fileExt == ".PNG" || fileExt == ".GIF") {
                            $("#spanFile_1702").append('<span class="keywords" idValue="' + id+'"> <a type="del" onclick="delFile(this,\'' + id + '\')"></a><a  modal="zoomImg"  href="javascript:void(0);" src="' + filePath + '" >' + fileTitle + '</a></span>');
                        } else {
                            $("#spanFile_1702").append('<span class="keywords" idValue="' + id +'"> <a type="del" onclick="delFile(this,\'' + id + '\')"></a><a target="_blank" href="' + filePath + '" >' + fileTitle + '</a></span>');
                        }
                        imgages.showextalink();
                    } else {
                        $('#spanLinkExamine').text(response.Message);
                        ys.msgError(response.Message);
                    }
                }
            },
            'onFallback': function (event) {
                ys.msgError(event);
            }
        });



        getForm();
        loadFormVali();
    });

    function loadFunRoom(roomValue) {
        //console.log("roomValue:" + roomValue);
        $('#FunRoomId').ysComboBox({ class: 'form-control'});
        ys.ajax({
            url: '@Url.Content("~/BusinessManage/FunRoom/GetListByUserId?OptType=1")',
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1) {
                    $('#FunRoomId').ysComboBox({ data: obj.Data, class: 'form-control', key: 'Id', value: 'RoomNatureName' });
                    if (roomValue > 0) {
                        $('#FunRoomId').ysComboBox('setValue', roomValue);
                    }
                } else {
                    $('#FunRoomId').ysComboBox({ data: [], class: 'form-control', key: 'Id', value: 'RoomNatureName' });
                    ys.msgError(obj.Message);
                }
            }
        });
    }

    function loadUserList(userId) {
        $('#checkUserId').ysComboBox({ class: 'form-control'});
        ys.ajax({
            //url: '@Url.Content("~/OrganizationManage/User/GetUserListByRoleId?RoleId=16508640061130155")',
            url: '@Url.Content("~/OrganizationManage/User/GetUserListByRoleId")',
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1) {
                    $('#checkUserId').ysComboBox({ data: obj.Data, class: 'form-control', key: 'Id', value: 'RealName' });
                    if (userId == 0) {
                        $("#checkUserId").ysComboBox('setValue', obj.Message);
                    } else {
                        $("#checkUserId").ysComboBox('setValue', userId);
                    }
                } else {
                    $('#checkUserId').ysComboBox({ data: [], class: 'form-control', key: 'Id', value: 'RealName' });
                    ys.msgError(obj.Message);
                }
            }
        });
    }

    function getForm() {
        if (id > 0) {
            ys.ajax({
                url: '@Url.Content("~/BusinessManage/CheckRectification/GetFormJson")' + '?id=' + id,
                type: 'get',
                success: function (obj) {
                    if (obj.Tag == 1) {

                        loadFunRoom(obj.Data.FunRoomId);
                        loadUserList(obj.Data.CheckUserId);
                        $('#form').setWebControls(obj.Data);

                        if (obj.Data.IsRectification == -1) {
                            $("#isRectification").ysComboBox({
                                data: ys.getJson(@Html.Raw(typeof(IsEnum).EnumToDictionaryString())),
                                defaultName: '请选择'
                            });
                        }

                        var CheckTime = ys.formatDate(obj.Data.CheckTime, "yyyy-MM-dd");
                        $("#checkTime").val(CheckTime);
                        var RectificationTime = ys.formatDate(obj.Data.RectificationTime, "yyyy-MM-dd");
                        if (RectificationTime != "1970-01-01") {
                            $("#rectificationTime").val(RectificationTime);
                        } else {
                            $("#rectificationTime").val("");
                        }

                        //调用显示附件信息
                        var fileList = obj.Data.Atts;
                        if (fileList.length > 0) {
                            for (var i = 0; i < fileList.length; i++) {
                                var id = fileList[i].Id;
                                var filePath = fileList[i].Path;
                                var fileExt = fileList[i].Ext.toUpperCase();
                                var fileTitle = fileList[i].Title;
                                var fileFileCategory = fileList[i].FileCategory;
                                var objDiv = $("#spanFile_" + fileFileCategory);
                                if (fileExt == ".JPG" || fileExt == ".BMP" || fileExt == ".JPEG" || fileExt == ".PNG" || fileExt == ".GIF") {
                                    objDiv.append('<span class="keywords" idValue="' + id + '"> <a type="del" onclick="delFile(this,\'' + id + '\')"></a><a  modal="zoomImg"  href="javascript:void(0);" src="' + filePath + '" >' + fileTitle + '</a></span>');
                                } else {
                                    objDiv.append('<span class="keywords" idValue="' + id + '"> <a type="del" onclick="delFile(this,\'' + id + '\')"></a><a target="_blank" href="' + filePath + '" >' + fileTitle + '</a></span>');
                                }
                            }
                            imgages.showextalink();
                        }
                    }
                }
            });
        }
        else {
            var defaultData = { class: 'form-control'};
            loadFunRoom(0);
            loadUserList(0);
            $('#form').setWebControls(defaultData);
        }
    }

    function saveForm(index) {
        if ($('#form').validate().form()) {
            var postData = $('#form').getWebControls({ Id: id });
            //var FunRoomId = postData.FunRoomId;
            //if (FunRoomId == "") {
            //    ys.msgError("请选择名称");
            //    return;
            //}
            var CheckUserId = postData.CheckUserId;
            if (CheckUserId == "") {
                ys.msgError("请选择排查类型");
                return;
            }
            var CheckResult = postData.CheckResult;
            if (CheckResult == "") {
                ys.msgError("请选择排查结果");
                return;
            }
            var CheckTime = postData.CheckTime;
            var RectificationTime = postData.RectificationTime;
            if (RectificationTime != "") {
                if (CheckTime > RectificationTime) {
                    ys.msgError("排查时间不能大于整改时间");
                    return;
                }
            }

            var IsRectification = postData.IsRectification;
            if (IsRectification == 1 && RectificationTime == "") {
                ys.msgError("已整改必须选择整改时间");
                return;
            }

            //判断排查清单扫描件是否上传
            var f = $("#spanFile_1701").html().trim();
            if (f == "") {
                ys.msgError("请上传排查清单扫描件");
                return;
            }

            //获取附件Id集合
            var objFileId = [];
            $(".keywords").each(function () {
                var idValue = $(this).attr("idValue");
                objFileId.push(idValue);
            });
            if (objFileId.length > 0) {
                postData.ListFileId = objFileId;
            }
            var errorMsg = '';
            var imageArr = [];
            $("#spanFile_1702 .keywords").each(function () {
                var idValue = $(this).attr("idValue");
                imageArr.push(idValue);
            });
             if (SaveValiDate != undefined && SaveValiDate.length > 0) {
                SaveValiDate.map(function (item, i) {
                    if (item.typecode == "Image") {
                        if (!(imageArr != undefined && imageArr.length >0)) {
                            errorMsg += (item.verifyhint + '<br/>');
                        }
                    }
                });
            } 
            if (errorMsg != '') {
                ys.msgError('验证失败，请填写完成再提交！<br/>' + errorMsg);
                return;
            }
            ys.ajax({
                url: '@Url.Content("~/BusinessManage/CheckRectification/SaveFormJson")',
                type: 'post',
                data: postData,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        parent.$('#gridTable').ysTable('refresh');parent.resetToolbarStatus();
                        parent.layer.close(index);
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        }
    }

    //#region
    //加载配置验证信息
    function loadFormVali() {
        ys.ajax({
            url: '@Url.Content("~/SystemManage/ConfigSet/GetListByCode")',
            type: 'Post',
            data: { typecode:'2SYSG-2AQGL-1AQZG'},
            success: function (obj) {
                if (obj.Tag == 1) {
                    var validateRule = {};
                    var validateMessage = {};
                    validateRule.checkTime = { required: true };
                    validateMessage.checkTime = { required: '请选择排查时间' };

                    if (obj.Data != undefined && obj.Data.length > 0) {
                        for (var i = 0; i < obj.Data.length; i++) {
                            if (obj.Data[i].ConfigValue == "1") {
                                var typecode = obj.Data[i].TypeCode.replaceAll("2SYSG-2AQGL-1AQZG-", "");
                                var verifyhint = "";
                                if (obj.Data[i].VerifyHint != undefined && obj.Data[i].VerifyHint.length > 0) {
                                    verifyhint = obj.Data[i].VerifyHint;
                                }
                                $("#lab" + typecode).append('<font class="red"> *</font>');
                                SaveValiDate.push({ typecode: typecode, verifyhint: verifyhint });
                            }
                        }
                    }
                    $('#form').validate({
                        rules: validateRule,
                        messages: validateMessage
                    });
                }
            }
        });
    }

    //#endregion
</script>

