﻿@{
    Layout = "~/Views/Shared/_FormWhite.cshtml";
}

<div class="wrapper animated fadeInRight">
    <form id="form" class="form-horizontal m">
        <div class="form-group row">
            <label class="col-sm-3 control-label ">班级名称<font class="red"> *</font></label>
            <div class="col-sm-8">
                <input id="ClassDesc" col="ClassDesc" type="text" class="form-control" autocomplete="off"/>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">班级学生数<font class="red"> *</font></label>
            <div class="col-sm-8">
                <input id="StudentNum" col="StudentNum" type="text" class="form-control" autocomplete="off"/>
            </div>
        </div>
    </form>
</div>

<script type="text/javascript">
    var id = ys.request("id");
    $(function () {
        $('#form').validate({
            rules: {
                ClassDesc: { required: true },
                StudentNum: { required: true, number: true }
            }
        });
        getForm();
    });
    function getForm() {
        if (id > 0) {
            ys.ajax({
                url: '@Url.Content("~/OrganizationManage/SchoolGradeClass/GetFormJson")' + '?id=' + id,
                type: 'get',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        $('#form').setWebControls(obj.Data);
                    }
                }
            });
        }
        else {
            var defaultData = {};
            $('#form').setWebControls(defaultData);
        }
    }

    function saveForm(index) {
        if ($('#form').validate().form()) {
            var postData = $('#form').getWebControls({ Id: id });
            if (parseFloat(postData.StudentNum) >= 70) {
                ys.msgError("验证失败：单个班级学生数必须小于70。");
            }else if (parseFloat(postData.StudentNum) < 20 || parseFloat(postData.StudentNum) > 50) {
                ys.confirm("班级学生数不在正常范围内20~50，确定要继续修改吗？", function () {
                    submitObj(index, postData);
                });
            } else {
                submitObj(index, postData);
            }

        }
    }
    function submitObj(index, postData) {
        ys.ajax({
            url: '@Url.Content("~/OrganizationManage/SchoolGradeClass/SaveEditFormJson")',
            type: 'post',
            data: postData,
            success: function (obj) {
                if (obj.Tag == 1) {
                    ys.msgSuccess(obj.Message);
                    parent.$('#gridTable').ysTable('refresh');parent.resetToolbarStatus();
                    parent.loadClassInfo();
                    parent.layer.close(index);
                }
                else {
                    ys.msgError(obj.Message);
                }
            }
        });
    }
</script>

