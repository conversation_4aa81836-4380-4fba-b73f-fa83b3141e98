﻿@{
    Layout = "~/Views/Shared/_Index.cshtml";
    int IsAllowEditModel = (int)ViewBag.IsAllowEditModel;
}

<div class="container-div">
    <div class="btn-group-sm hidden-xs" id="toolbar">
    </div>
    <div class="col-sm-12 select-table table-striped">

        <div class="ibox-title">
            <h5>填报采购计划</h5>
        </div>
        <div class="card-body">
            <form id="form" class="form-horizontal m">
                <div class="form-group row">
                    <label class="col-sm-2 control-label ">仪器名称<font class="red"> *</font></label>
                    <div class="col-sm-8">
                        <input id="name" col="Name" type="text" class="form-control" readonly />
                        <input id="instrumentStandardId" col="InstrumentStandardId" type="hidden" />
                    </div>
                </div>
                <div class="form-group row">
                    <label class="col-sm-2 control-label ">适用学段<font class="red"> *</font></label>
                    <div class="col-sm-8">
                        <input type="hidden" id="stageId" col="StageId" />
                        <input id="stageName" col="StageName" type="text" class="form-control" readonly />
                    </div>
                </div>
                <div class="form-group row">
                    <label class="col-sm-2 control-label ">适用学科<font class="red"> *</font></label>
                    <div class="col-sm-8">
                        <input type="hidden" id="courseId" col="CourseId" />
                        <input id="courseName" col="CourseName" type="text" class="form-control" readonly />
                    </div>
                </div>
                <div class="form-group row">
                    <label class="col-sm-2 control-label ">采购年度<font class="red"> *</font></label>
                    <div class="col-sm-8">
                        <div id="purchaseYear" col="PurchaseYear"></div>
                    </div>
                </div>

                <div class="form-group row">
                    <label class="col-sm-2 control-label ">选择规格属性<font class="red"> *</font></label>
                    <div class="col-sm-8">
                        <div id="modelStandardId"></div>
                    </div>
                </div>
                <div class="form-group row" id="divModel" style="display:none;">
                    <label class="col-sm-2 control-label ">规格属性<font class="red"> *</font></label>
                    <div class="col-sm-8">
                        <input id="model" col="Model" type="text" class="form-control" />
                    </div>
                </div>
                <div class="form-group row">
                    <label class="col-sm-2 control-label ">单位<font class="red"> *</font></label>
                    <div class="col-sm-8">
                        <input id="unitName" col="UnitName" type="text" class="form-control" readonly />
                    </div>
                </div>
                <div class="form-group row">
                    <label class="col-sm-2 control-label ">数量<font class="red"> *</font></label>
                    <div class="col-sm-8">
                        <input id="num" col="Num" type="text" class="form-control" />
                    </div>
                </div>
                <div class="form-group row">
                    <label class="col-sm-2 control-label ">单价(元)<font class="red"> *</font></label>
                    <div class="col-sm-8">
                        <input id="price" col="Price" type="text" class="form-control" />
                    </div>
                </div>
                <div class="form-group row">
                    <label class="col-sm-2 control-label ">采购理由<font class="red"> *</font></label>
                    <div class="col-sm-8">
                        <input id="reason" col="Reason" type="text" class="form-control" placeholder="如损坏等" />
                    </div>
                </div>
            </form>
        </div>
        <div class="btn-group-sm hidden-xs" id="toolbar" style="text-align: center;">
            <a id="btnAdd" class="btn btn-info" onclick="saveForm(@InstrumentAuditStatuzEnum.DeclareIng.ParseToInt());"><i class="fa fa-save"></i> 保存</a>&nbsp;&nbsp;
            @*<a id="btnAdd" class="btn btn-info" onclick="saveForm(@InstrumentAuditStatuzEnum.WaitSchoolAudit.ParseToInt());"><i class="fa fa-edit"></i> 转交下一步</a>
            <span style="color:#999;">（下一步转交学校审批）</span>*@
        </div>
    </div>
</div>
<script type="text/javascript">
    var id = ys.request("attendstaticid");

    $(function () {
        $(".inputprompt").popover({
            trigger: 'hover',
            html: true
        });

        getYear();

        getForm();

        $('#form').validate({
            rules: {
                purchaseYear: { required: true },
                name: { required: true },
                model: { required: true },
                unitName: { required: true },
                num: { required: true, number: true, thanMinValue: 0 },
                price: { required: true, number: true },
                stageId: { required: true },
                courseId: { required: true },
                reason: { required: true }
            }
        });
    });

    function getYear() {
        var d = new Date();
        var nowYear = d.getFullYear();
        var nextYear = nowYear + 1;
        $("#purchaseYear").ysComboBox({
            data: [{
                "id": nowYear,
                "text": nowYear
            }, {
                "id": nextYear,
                "text": nextYear,
            }],
            key: "id",
            value: "text",
            class: "form-control"
        });
        var defaultData = {
            PurchaseYear: new Date().getFullYear()
        };
        $('#form').setWebControls(defaultData);
    }

    function getForm() {
        ys.ajax({
            url: '@Url.Content("~/InstrumentManage/PurchaseDeclaration/GetDeclarationByAttendId")' + '?id=' + id,
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1) {
                    getInstrumentModel(obj.Data.InstrumentStandardId, obj.Data.ModelStandardId);
                    $('#divModel').show();
                    $('#form').setWebControls(obj.Data);
                }
            }
        });
    }

    function saveForm(statuz) {
        if ($('#form').validate().form()) {
            var postData = $('#form').getWebControls({
                Id: 0,
                Statuz: statuz,
                ModelStandardId: $('#modelStandardId').ysComboBox('getValue')
            });

            var checkErr = checkMessage(postData);
            if (checkErr != '') {
                ys.msgError(checkErr);
                return false;
            }

            ys.ajax({
                url: '@Url.Content("~/InstrumentManage/PurchaseDeclaration/SaveFormJson")',
                type: 'post',
                data: postData,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        var url = '@Url.Content("~/InstrumentManage/PurchaseDeclaration/InstrumentStandardIndex")';
                        createMenuAndCloseCurrent(url, "按达标填报");
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        }
    }

    function getInstrumentModel(id) {
        $('#modelStandardId').ysComboBox({
            data: [],
            key: 'Id',
            value: 'Model',
            class: "form-control",
            onChange: function () {
                if ($('#modelStandardId').ysComboBox('getValue') == '') {
                    $('#divModel').hide();
                    $('#model').val('');
                }
                else {
                    $('#divModel').show();
                    var model = $("#modelStandardId option:checked").text();
                    model = model == '手动填写' ? '' : model;
                    $('#model').val(model);
                }
            }
        });
        if (id > 0) {
            var param = { Pid: id, ClassType: 2 };
            ys.ajax({
                url: '@Url.Content("~/InstrumentManage/InstrumentStandard/GetInstrumentStandard")',
                data: { param: param },
                type: 'post',
                success: function (obj) {
                    if (obj.Tag == 1 && obj.Data) {
                        if (@IsAllowEditModel == 1) obj.Data.push({ Id: 0, Model: '手动填写' });
                        else $('#model').prop('disabled', true);
                        $('#modelStandardId').ysComboBox({
                            data: obj.Data,
                            key: 'Id',
                            value: 'Model',
                            class: "form-control"
                        });
                        if (obj.Data.length == 1) {
                            $('#modelStandardId').ysComboBox('setValue', obj.Data[0].Id);
                        }
                    }
                }
            });
        }
    }

    //保存校验
    function checkMessage(postData) {
        var checkErr = '';
        if (!postData.ModelStandardId) {
            checkErr += '请选择规格属性！<br />';
        }
        else if (postData.Model.trim() == '') {
            checkErr += '请填写规格属性！<br />';
        }
        if (!postData.StageId) {
            checkErr += '请选择适用学段！<br />';
        }
        if (!postData.CourseId) {
            checkErr += '请选择适用学科 ！<br />';
        }
        return checkErr;
    }
</script>

