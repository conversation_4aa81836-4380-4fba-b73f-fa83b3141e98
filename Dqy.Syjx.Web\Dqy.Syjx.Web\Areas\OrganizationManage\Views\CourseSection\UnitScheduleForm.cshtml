﻿@{
    Layout = "~/Views/Shared/_FormWhite.cshtml";
}

@inject Microsoft.AspNetCore.Hosting.IWebHostEnvironment HostingEnvironment
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/laydate/5.1/laydate.js"),true)

<div class="wrapper animated fadeInRight">
    <form id="form" class="form-horizontal m">
        <div class="form-group row">
            <label class="col-sm-2 control-label">
                夏季时段：
            </label>
            <div class="col-sm-4 inline">
                <input id="x_beginDate" col="XBeginDate" type="text" class="form-control" />
            </div>
            @*<label class="col-sm-2 control-label ">夏季时段：</label>
            <div class="col-sm-4">
                <div class="form-inline">
                    <input id="x_beginDate" col="XBeginDate" type="text" class="form-control form-inline" />
                    <span>至</span>
                    <input id="x_endDate" col="XEndDate" type="text" class="form-control form-inline"/>
                </div>
            </div>*@
        </div>
        <div class="form-group row">
            <label class="col-sm-2 control-label">
                冬季时段：
            </label>
            <div class="col-sm-4 inline">
                <input id="d_beginDate" col="DBeginDate" type="text" class="form-control" />
            </div>
           @* <label class="col-sm-2 control-label">
                冬季时段：
            </label>
            <div class="col-sm-4">
                <div class="form-inline">
                    <input id="d_beginDate" col="DBeginDate" type="text" class="form-control" />
                    <span>至</span>
                    <input id="d_endDate" col="DEndDate" type="text" class="form-control" />
                </div>
            </div>*@
        </div>
    </form>
</div>
<script type="text/javascript">
    //var id = ys.request("id");
    $(function () {

        //laydate.render({ elem: '#x_beginDate', format: 'MM-dd', trigger: 'click', });
        //laydate.render({ elem: '#x_endDate', format: 'MM-dd', trigger: 'click', });
        //laydate.render({ elem: '#d_beginDate', format: 'MM-dd', trigger: 'click', });
        //laydate.render({ elem: '#d_endDate', format: 'MM-dd', trigger: 'click', });
       
        laydate.render({
            elem: '#x_beginDate',
            format: 'MM-dd', 
            //trigger: 'click'

            range: '~',
        });

        laydate.render({
            elem: '#d_beginDate',
            format: 'MM-dd',
            //trigger: 'click',
            range: '~',
        });

        $("#form").validate({
            rules: {
                x_beginDate: { required: true },
                d_beginDate: { required: true },
            }
        });

        getForm();
    });



    //function getForm() {
    //    if (id > 0) {
    //        var url = '@Url.Content("~/OrganizationManage/UnitSchedule/GetListJson")' + '?GradeId=' + id;
    //        ys.ajax({
    //            url: url,
    //            type: "get",
    //            success: function (obj) {
    //                if (obj.Tag == 1 && obj.Total > 0) {
    //                    var result = obj.Data;
    //                    //console.log("result:" + JSON.stringify(result))

    //                    $("#x_beginDate").val(result[0].BeginDate + " ~ " + result[0].EndDate);
    //                    $("#d_beginDate").val(result[0].DjBeginTime + " ~ " + result[0].DjEndTime);

    //                    //$("#x_beginDate").val(result[0].BeginDate);
    //                    //$("#x_endDate").val(result[0].EndDate);
    //                    //$("#d_beginDate").val(result[1].BeginDate);
    //                    //$("#d_endDate").val(result[1].EndDate);
    //                    //$("#x_beginDate").val(ys.formatDate(result[0].BeginDate, "MM-dd"));
    //                    //$("#x_endDate").val(ys.formatDate(result[0].EndDate, "MM-dd")); 
    //                }
    //            }
    //        });
    //    }
    //}


    //function saveForm(index) {
    //    if ($("#form").validate().form()) {
    //        var formObj = $("#form").getWebControls();
    //        var beginDate = formObj.XBeginDate;
    //        var djBeginDate = formObj.DBeginDate;
    //        var arrXjBegin = beginDate.split('~');
    //        var arrDjBegin = djBeginDate.split('~');
    //        var obj = { GradeId: id, IsSet: 1, BeginDate: $.trim(arrXjBegin[0]), EndDate: $.trim(arrXjBegin[1]), DjBeginTime: $.trim(arrDjBegin[0]), DjEndTime: $.trim(arrDjBegin[1]) };
    //        var postData = { schedule: obj, gradeId: id};
    //        ys.ajax({
    //            url: '@Url.Content("~/OrganizationManage/UnitSchedule/SaveBatchFormJson")',
    //            type: "post",
    //            data: postData,
    //            success: function (obj) {
    //                if (obj.Tag == 1) {
    //                    ys.msgSuccess(obj.Message);
    //                    parent.$('#gridTable').ysTable('refresh'); parent.resetToolbarStatus();
    //                    parent.layer.close(index);
    //                }
    //                else {
    //                    //ys.msgError(obj.Message);
    //                    ys.alertError(obj.Message);
    //                }
    //            }
    //        });
    //    }
    //}


    function getForm() {
        var url = '@Url.Content("~/OrganizationManage/UnitSchedule/GetListNewJson")';
        ys.ajax({
            url: url,
            type: "get",
            success: function (obj) {
                if (obj.Tag == 1 && obj.Total > 0) {
                    var result = obj.Data;

                    $("#x_beginDate").val(result[0].BeginDate + " ~ " + result[0].EndDate);
                    $("#d_beginDate").val(result[0].DjBeginTime + " ~ " + result[0].DjEndTime);
                }
            }
        });
    }


    function saveForm(index) {
        if ($("#form").validate().form()) {
            var formObj = $("#form").getWebControls();
            var beginDate = formObj.XBeginDate;
            var djBeginDate = formObj.DBeginDate;
            var arrXjBegin = beginDate.split('~');
            var arrDjBegin = djBeginDate.split('~');
            var obj = {BeginDate: $.trim(arrXjBegin[0]), EndDate: $.trim(arrXjBegin[1]), DjBeginTime: $.trim(arrDjBegin[0]), DjEndTime: $.trim(arrDjBegin[1]) };
            var postData = { schedule: obj};
            ys.ajax({
                url: '@Url.Content("~/OrganizationManage/UnitSchedule/SaveBatchFormNewJson")',
                type: "post",
                data: postData,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        parent.$('#gridTable').ysTable('refresh'); parent.resetToolbarStatus();
                        parent.layer.close(index);
                    }
                    else {
                        //ys.msgError(obj.Message);
                        ys.alertError(obj.Message);
                    }
                }
            });
        }
    }

</script>
