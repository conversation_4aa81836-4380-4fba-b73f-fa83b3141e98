﻿@{
    Layout = "~/Views/Shared/_FormWhite.cshtml";
}
<div class="wrapper animated fadeInRight">
    <form id="form" class="form-horizontal m">
        <div class="form-group row">
            <label class="col-sm-3 control-label ">设备名称<font class="red"> *</font></label>
            <div class="col-sm-7">
                <input id="Name" col="Name" type="text" required class="form-control" autocomplete="off" />
            </div>
            <div class="col-sm-2"> </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">品牌<font class="red"> *</font></label>
            <div class="col-sm-7">
                <input id="Brand" col="Brand" type="text" required class="form-control" autocomplete="off" />
            </div>
            <div class="col-sm-2"> </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">型号<font class="red"> *</font></label>
            <div class="col-sm-7">
                <input id="Modelz" col="Modelz" type="text" required class="form-control" autocomplete="off" />
            </div>
            <div class="col-sm-2"> </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">数量<font class="red"> *</font></label>
            <div class="col-sm-7">
                <input id="Num" col="Num" type="text" required class="form-control" autocomplete="off" />
            </div>
            <div class="col-sm-2"> </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">计量单位<font class="red"> *</font></label>
            <div class="col-sm-7">
                <input id="UnitName" col="UnitName" type="text" required class="form-control" autocomplete="off" />
            </div>
            <div class="col-sm-2"> </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">备注</label>
            <div class="col-sm-7">
                <input id="Remark" col="Remark" type="text" class="form-control" autocomplete="off" />
            </div>
            <div class="col-sm-2"> </div>
        </div>
    </form>
</div>
<script type="text/javascript">
    var id = ys.request("id");
    $(function () {
         getForm();
    });
     
    function getForm() {
        if (id > 0) {
            ys.ajax({
                url: '@Url.Content("~/EquipmentStatisticsManage/Report/GetEquipmentFormJson")' + '?id=' + id,
                type: 'get',
                async: false,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        $('#form').setWebControls(obj.Data); 
                    }
                }
            });
        } else {
            var defaultData = {};
            $('#form').setWebControls(defaultData);
        }
    }

    function saveForm(index) {
        if ($('#form').validate().form()) {
            var postData = $('#form').getWebControls({ Id: 0 });
            var errorMsg = '';
            // if (!(parseInt(postData.SchoolYearStart) > 0)) {
            //     errorMsg += '请选择学年。<br/>';
            // }
            // if (!(parseInt(postData.SchoolTerm) > 0)) {
            //     errorMsg += '请选择学期。<br/>';
            // }
            // if (!(parseInt(postData.CourseId) > 0)) {
            //     errorMsg += '请选择学科。<br/>';
            // }
            // if (!(parseInt(postData.GradeId) > 0)) {
            //     errorMsg += '请选择年级。<br/>';
            // }
            // if (!(parseInt(postData.VersionCurrentId) > 0)) {
            //     errorMsg += '请选择教材版本。<br/>';
            // } 
            if (errorMsg != '') {
                ys.msgError('验证失败，请填写完成再提交！<br/>' + errorMsg);
                return;
            }
            console.log("--postData----:" + JSON.stringify(postData));
            ys.ajax({
                url: '@Url.Content("~/EquipmentStatisticsManage/Report/SaveEquipmentFormJson")',
                type: 'post',
                data: postData,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        // parent.loadEquipmentData(obj.Data);
                        // parent.layer.close(index);
                    } else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        }
    }
</script>