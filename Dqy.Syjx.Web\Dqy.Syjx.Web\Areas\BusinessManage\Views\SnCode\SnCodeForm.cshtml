﻿@{
    Layout = "~/Views/Shared/_FormWhite.cshtml";
}

<div class="wrapper animated fadeInRight">
    <form id="form" class="form-horizontal m">
        <div class="form-group row">
            <label class="col-sm-3 control-label ">分类编号<font class="red"> *</font></label>
            <div class="col-sm-8">
                <div id="categoryCode" col="CategoryCode"  ></div>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">生成编码数量</label>
            <div class="col-sm-8">
                <input id="num" col="Num" type="text" value="50000" class="form-control" />
            </div>
        </div>
    </form>
</div>

<script type="text/javascript">
    var id = ys.request("id");
    $(function () {
        $("#categoryCode").ysComboBox({
            key: 'id',
            value: 'text',
            data: [{ "id": "E", "text": "仪器" }, { "id": "R", "text": "专用室" }, { "id": "C", "text": "橱柜" }],
            class: 'form-control'
        });
    });
    //生成编码
    function saveForm(index) {
        //if ($('#form').validate().form()) {
            var postData = $('#form').getWebControls();
            ys.ajax({
                url: '@Url.Content("~/BusinessManage/SnCode/CreateCode")',
                type: 'post',
                data: postData,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        parent.searchGrid();
                        parent.layer.close(index);
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        }
    //}
</script>

