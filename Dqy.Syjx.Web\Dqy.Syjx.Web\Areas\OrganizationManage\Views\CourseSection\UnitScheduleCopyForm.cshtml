﻿
@{
    Layout = "~/Views/Shared/_FormWhite.cshtml";
}


<div class="wrapper animated fadeInRight">
    <form id="form" class="form-horizontal m">
        <div class="form-group row">
            <label class="col-sm-2 control-label ">年级：</label>
            <div class="col-sm-10" id="grade" col="GradeIds">
               
            </div>
        </div>
    </form>
</div>

<script type="text/javascript">
    var id = ys.request("id");
    var gradeName = ys.request("gradeName");

    //console.log("gradeName:" + gradeName);

    $(function () {
        getForm();
    });



    function getForm() {
        if (id > 0) {
            var url = '@Url.Content("~/SystemManage/StaticDictionary/GetUnitGradeListJson")' + '?dictionaryId='+id;
            $("#grade").ysCheckBox({
                url: url,
                key: "DictionaryId",
                value: "DicName"
            });
        }
    }


    function saveForm(index) {

        var listGrade = [];
        $("input:checkbox[name='grade_checkbox']:checked").each(function () {
            listGrade.push($(this).val());
        });

        if (listGrade.length == 0) {
            ys.msgError("请选择年级");
            return;
        }

        var postData = {
            listGradeId: listGrade, gradeId: id, gradeName: gradeName
        };

        ys.ajax({
            url: '@Url.Content("~/OrganizationManage/UnitSchedule/SaveCopyFormJson")',
            type: "post",
            data: postData,
            success: function (obj) {
                if (obj.Tag == 1) {
                    ys.msgSuccess(obj.Message);
                    parent.$('#gridTable').ysTable('refresh'); parent.resetToolbarStatus();
                    parent.layer.close(index);
                }
                else {
                    ys.msgError(obj.Message);
                }
            }
        });
    }

</script>