﻿@{
    Layout = "~/Views/Shared/_FormWhite.cshtml";
}
<div class="wrapper animated fadeInRight">
    <form id="form" class="form-horizontal m">
        <div class="form-group row">
            <label class="col-sm-3 control-label ">单位名称<font class="red">*</font></label>
            <div class="col-sm-6">
                <input id="unitName" col="UnitName" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">人员名称<font class="red">*</font></label>
            <div class="col-sm-6">
                <input id="userName" col="UserName" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">手机号码<font class="red">*</font></label>
            <div class="col-sm-6">
                <input id="mobile" col="Mobile" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">单位类型<font class="red">*</font></label>
            <div class="col-sm-6">
                <div class="col-sm-12" id="unitTypeId" col="UnitTypeId"></div>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">角色<font class="red">*</font></label>
            <div class="col-sm-6">
                <input id="roleNames" col="RoleNames" type="text" class="form-control" />
            </div>
            <div class="col-sm-3">
                角色名称，多个角色以逗号分隔填写。
            </div>
        </div>
    </form>
</div>

<script type="text/javascript">
    var id = ys.request("id");
    $(function () {

        $("#unitTypeId").ysRadioBox({ data: ys.getJson(@Html.Raw(typeof(UnitTypeNameEnum).EnumToDictionaryString())) });
        getForm();

        $('#form').validate({
            rules: {
                unitName: { required: true },
                mobile: { required: true, isPhone: true },
                userName: { required: true },
                roleNames: { required: true },
            }
        });
    });

    function getForm() {
        if (id > 0) {
            ys.ajax({
                url: '@Url.Content("~/OrganizationManage/ThirdUserPower/GetFormJson")' + '?id=' + id,
                type: 'get',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        $('#form').setWebControls(obj.Data);
                    }
                }
            });
        }
        else {
            var defaultData = {};
            $('#form').setWebControls(defaultData);
        }
    }

    function saveForm(index) {
        if ($('#form').validate().form()) {
            var postData = $('#form').getWebControls({ Id: id });
            if (postData.UnitTypeId == "") {
                ys.msgError("请选择单位类型");
                return;
            }
            ys.ajax({
                url: '@Url.Content("~/OrganizationManage/ThirdUserPower/SaveFormJson")',
                type: 'post',
                data: postData,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        parent.$('#gridTable').ysTable('refresh');parent.resetToolbarStatus();
                        parent.layer.close(index);
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        }
    }
</script>

