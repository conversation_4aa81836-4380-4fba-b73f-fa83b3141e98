﻿@using Dqy.Syjx.Entity.SystemManage
@{
    Layout = "~/Views/Shared/_FormWhite.cshtml";
}
@inject Microsoft.AspNetCore.Hosting.IWebHostEnvironment HostingEnvironment
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/uploadifive/File.min.css"))
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/uploadifive/File.min.js"))

<style>
    .delete {
        position: absolute;
        right: 0px;
        top: 0px;
        width: 14px;
        height: 14px;
        background: url(/image/times.png) no-repeat center center;
        cursor: pointer;
    }

</style>

<div class="container-div">
    <div class="btn-group-sm hidden-xs" id="toolbar">
    </div>
    <div class="fixed-table-container">

        <div class="ibox-title">
            <h5>单位管理</h5>
        </div>

        <div class="card-body">
            <form id="fileConfigForm" class="form-horizontal m">
                <input id="Id" col="Id" type="hidden" />
                <div class="form-group row">
                    <label class="col-sm-2 control-label ">
                        <span class="glyphicon glyphicon-question-sign titleprompt inputprompt" aria-hidden="true" data-container="body" data-toggle="tooltip" data-placement="right"
                              data-content="不可修改"></span> 单位代码：<font class="red"> *</font>
                    </label>
                    <div class="col-sm-10">
                        <input id="Code" col="Code" type="text" class="form-control" readonly />
                    </div>
                </div>
                <div class="form-group row">
                    <label class="col-sm-2 control-label ">单位名称：<font class="red"> *</font></label>
                    <div class="col-sm-10">
                        <input id="Name" col="Name" type="text" class="form-control" />
                    </div>
                </div>
                <div class="form-group row">
                    <label class="col-sm-2 control-label ">社会统一信用代码：</label>
                    <div class="col-sm-10">
                        <input id="OrganizationCode" col="OrganizationCode" type="text" class="form-control" />
                    </div>
                </div>
                <div class="form-group row">
                    <label class="col-sm-2 control-label ">
                        <span class="glyphicon glyphicon-question-sign titleprompt inputprompt" aria-hidden="true" data-container="body" data-toggle="tooltip" data-placement="right"
                              data-content="不可修改"></span>所在区域：
                    </label>
                    <div class="col-sm-10">
                        <div id="AreaId" col="AreaId"></div>
                    </div>
                </div>
                <div class="form-group row">
                    <label class="col-sm-2 control-label ">详细地址：</label>
                    <div class="col-sm-10">
                        <input id="Address" col="Address" type="text" class="form-control" />
                    </div>
                </div>
                <div class="form-group row">
                    <label class="col-sm-2 control-label ">单位性质：<font class="red"> *</font></label>
                    <div class="col-sm-4">
                        <label class='radio-box'><input type='radio' name='SchoolNature' col='SchoolNature' checked="checked" value='1' /><span id="spanSchoolNatureName">--</span></label>
                    </div>
                    <label class="col-sm-2 control-label ">单位属性：<font class="red">&nbsp;</font></label>
                    <div class="col-sm-4">
                        <input type="hidden" name='SchoolProp' col='SchoolProp' value="" />
                        <span id="spanSchoolPropName" class="form-control" style="border: 0px solid #e5e6e7;">--</span>
                    </div>
                </div>
                <div class="form-group row">
                    <label class="col-sm-2 control-label ">学校规模（轨）：<font class="red">&nbsp;</font></label>
                    <div class="col-sm-4">                       
                        <input id="TracksNumStatic" col="TracksNumStatic" type="text" data-suffix="BLL" class="form-control" />                       
                    </div>
                    <label class="col-sm-2 control-label ">
                        <span class="glyphicon glyphicon-question-sign titleprompt inputprompt" aria-hidden="true" data-container="body" data-toggle="tooltip" data-placement="right"
                              data-content="由年级班级信息创建后自动计算"></span> 班级总数（班）：<font class="red">&nbsp;</font>
                    </label>
                    <div class="col-sm-4">
                        <input id="ClassNum" col="ClassNum" type="text" data-suffix="EntityMap" class="form-control" readonly />
                    </div>
                </div>
                <div class="form-group row">
                    <label class="col-sm-2 control-label ">教职工数（人）：</label>
                    <div class="col-sm-4">
                        <input id="TeacherNum" col="TeacherNum" type="text" data-suffix="BLL" class="form-control" />
                    </div>

                    <label class="col-sm-2 control-label ">
                        <span class="glyphicon glyphicon-question-sign titleprompt inputprompt" aria-hidden="true" data-container="body" data-toggle="tooltip" data-placement="right"
                              data-content="由年级班级信息创建后自动计算"></span> 学生总数（人）：<font class="red">&nbsp;</font>
                    </label>
                    <div class="col-sm-4">
                        <input id="StudentNum" col="StudentNum" type="text" data-suffix="Service" class="form-control" readonly />
                    </div>
                </div>
                <div class="form-group row">
                    <label class="col-sm-2 control-label ">占地面积（㎡）：<font class="red">&nbsp;</font></label>
                    <div class="col-sm-4">
                        <input id="FloorArea" col="FloorArea" type="text" data-suffix="BLL" class="form-control" />
                    </div>
                    <label class="col-sm-2 control-label ">建筑面积（㎡）：<font class="red">&nbsp;</font></label>
                    <div class="col-sm-4">
                        <input id="BuildArea" col="BuildArea" type="text" data-suffix="Service" class="form-control" />
                    </div>
                </div>

                <div class="form-group row">
                    <label class="col-sm-2 control-label " id="labImage">
                        <span class="glyphicon glyphicon-question-sign titleprompt inputprompt" aria-hidden="true" data-container="body" data-toggle="tooltip" data-placement="right"
                              data-content="用于“实验（专用）室管理->实验（专用）室列表”二维码生成。推荐图片大小为（198 * 198），小于5M"></span>
                        单位Logo：
                    </label>
                    <div class="col-sm-8">
                        <input type="file" name="uploadify" id="uploadify" />
                        <div style="height: 55px;display:none;" id="fileQueue"></div>
                        <input id="AttachmentId" col="AttachmentId" type="hidden" value="0" />
                        <input id="Path" col="Path" type="hidden" />
                        <a modal="zoomImg" href="javascript:void(0);" id="awardSrc">
                            <img modal="zoomImg" href="javascript:void(0);" src="" id="imgAward" class="col-sm-8" style="max-width:200px;padding: 10px 0px;" />
                        </a>
                    </div>
                </div>
            </form>
        </div>
        <div class="btn-group-sm hidden-xs" id="toolbar" style="text-align: center;">
            <a id="btnAdd" class="btn btn-info" onclick="saveForm();"><i class="fa fa-edit"></i> 保存</a>
        </div>
    </div>
</div>
<script type="text/javascript">
    $(function () {
        $(".inputprompt").popover({
            trigger: 'hover',
            html: true
        });

        $('#AreaId').ysComboBoxTree({
            url: '@Url.Content("~/SystemManage/Area/GetZtreeAreaListJson")', expandLevel: -1, callback: {
                customOnClick: function () {
                    $('#AreaId > input').unbind("click");
                }
            }
        });
        loadUploadify();
        getForm();     
        $("#fileConfigForm").validate({
            rules: {
                Name: { required: true },
                TracksNumStatic: { number: true },
                TeacherNum: { number: true },
                FloorArea: { number: true },
                BuildArea: { number: true },
            },
            messages: {
                TracksNumStatic: { required: '请输入数字' },
                TeacherNum: { required: '请输入数字' },
                FloorArea: { required: '请输入数字' },
                BuildArea: { required: '请输入数字' },
            }
        });
    });

    function loadUploadify() {
        $("#uploadify").uploadifive({
            'uploadScript': '/File/UploadFile?fc=3100',
            'cancelImg': '~/lib/uploadifive/uploadifive-cancel.png',
            'buttonText': '上 传',
            'queueID': 'fileQueue',
            'auto': true,
            'multi': false,
            'fileDesc': '支持格式：*.jpg;*.jpeg;*.png;',
            'fileExt': '*.jpg;*.jpeg;*.png;',
            'onAddQueueItem': function (file) {

            },
            'onUploadComplete': function (fileObj, response) {
                if (typeof (response) == "string" && response != "") {
                    response = JSON.parse(response);
                    if (response.Tag == 1) {
                        $('#AttachmentId').val(response.Description);
                        $('#Path').val(response.Data);
                        $('#imgAward').attr("src", response.Data);
                        $("#awardSrc").attr("src", response.Data);
                        imgages.showextalink();
                        ys.msgSuccess("上传成功！");
                    } else {
                        ys.msgError(response.Message);
                    }
                }
            },
            'onFallback': function (event) {
                ys.msgError(event);
            }
        });
    }

    function getForm() {
        ys.ajax({
            url: '@Url.Content("~/OrganizationManage/Unit/GetFormJson")' + '?id=0',
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1) {
                    $('#fileConfigForm').setWebControls(obj.Data);
                    //处理学校，单位属性 单位性质
                    $("input[name='SchoolNature']").val(obj.Data.SchoolNature);
                    $("#spanSchoolNatureName").text(obj.Data.SchoolNatureName);
                    $("input[name='SchoolProp']").val(obj.Data.SchoolProp);
                    $("#spanSchoolPropName").text(obj.Data.SchoolPropName);

                    if (obj.Data.Logo != "") {
                        $('#imgAward').attr("src", obj.Data.Logo);
                    }

                }
            }
        });
    }

    function saveForm() {
        if ($('#fileConfigForm').validate().form()) {
            var postData = $('#fileConfigForm').getWebControls();
            var Path = $("#Path").val();
            postData.AreaId = ys.getLastValue(postData.AreaId);
            postData.Logo = Path;
            ys.ajax({
                url: '@Url.Content("~/OrganizationManage/Unit/SaveFormJson")',
                type: 'post',
                data: postData,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        }
    }

    function loadInitschoolePoroo() {

    }
</script>
