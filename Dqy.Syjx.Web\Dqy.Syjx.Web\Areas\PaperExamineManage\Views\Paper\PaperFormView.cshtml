﻿@{
    Layout = "~/Views/Shared/_FormWhite.cshtml";
    int UnitType = (int)ViewBag.UnitType;
}
@inject Microsoft.AspNetCore.Hosting.IWebHostEnvironment HostingEnvironment
<script src='@Url.Content("~/junfei/js/CommonPaper.js")' type="text/javascript"></script>
<div class="wrapper animated fadeInRight">
    <form id="form" class="form-horizontal m">
        <div class="form-group row" id="divCode">
            <label class="col-sm-3 control-label ">试卷编号</label>
            <div class="col-sm-7">
                <input id="Code" col="Code" type="text" class="form-control" disabled="disabled"/>
            </div>
            <div class="col-sm-2">
            </div>
        </div>
        <div class="form-group row" id="divModifyTime">
            <label class="col-sm-3 control-label ">修改时间</label>
            <div class="col-sm-7">
                <input id="BaseModifyTime" col="BaseModifyTime" type="text" class="form-control" disabled="disabled"/>
            </div>
            <div class="col-sm-2">
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label">试卷名称</label>
            <div class="col-sm-7">
                <input id="Name" col="Name" type="text" class="form-control" disabled="disabled"/>
            </div>
            <div class="col-sm-2">
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">适用对象</label>
            <div class="col-sm-7">
                <input id="UseObjName" col="UseObjName" type="text" class="form-control" disabled="disabled" />
            </div>
            <div class="col-sm-2">
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">适用学科</label>
            <div class="col-sm-7">
                <input id="CourseName" col="CourseName" type="text" class="form-control" disabled="disabled" />
            </div>
            <div class="col-sm-2">
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">适用学段</label>
            <div class="col-sm-7">
                <input id="SchoolStage" col="SchoolStage" type="text" class="form-control" disabled="disabled" />
            </div>
            <div class="col-sm-2">
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">适用年级</label>
            <div class="col-sm-7">
                <input id="GradeName" col="GradeName" type="text" class="form-control" disabled="disabled" />
            </div>
            <div class="col-sm-2">
            </div>
        </div>
    </form>
</div>

<script type="text/javascript">
    var id = ys.request("id");

    $(function () {
        getForm();
    });

    function getForm() {
        if (id > 0) {
            ys.ajax({
                url: '@Url.Content("~/PaperExamineManage/Paper/GetPageListJson?PaperType=1")' + '&Ids=' + id,
                type: 'get',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        obj.Data = obj.Data[0];
                        $('#Code').val(obj.Data.Code);
                        $('#BaseModifyTime').val(obj.Data.BaseModifyTime);
                        $('#Name').val(obj.Data.Name);
                        $('#UseObjName').val(obj.Data.UseObjName);
                        $('#CourseName').val(obj.Data.CourseName);
                        $('#SchoolStage').val(obj.Data.SchoolStage);
                        $('#GradeName').val(obj.Data.GradeName);
                    }
                }
            });
        }
    }
</script>


