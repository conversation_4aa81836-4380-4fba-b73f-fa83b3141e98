﻿@{
    Layout = "~/Views/Shared/_FormWhite.cshtml";
}

<div class="wrapper animated fadeInRight">
    <form id="form" class="form-horizontal m-t">
        <div class="form-group row">
            <label class="col-sm-2 control-label">登录信息</label>
            <div class="form-control-static" col="UserName"></div>
        </div>
        <div class="form-group row">
            <label class="col-sm-2 control-label">请求地址</label>
            <div class="form-control-static" col="ExecuteUrl"></div>
        </div>
        <div class="form-group row">
            <label class="col-sm-2 control-label">请求参数</label>
            <div class="form-control-static">
                <pre><span col="ExecuteParam"></span></pre>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-2 control-label">状态</label>
            <div class="form-control-static" col="LogStatus"></div>
        </div>
        <div class="form-group row">
            <label class="col-sm-2 control-label">执行结果</label>
            <div class="form-control-static">
                <pre><span col="ExecuteResult"></span></pre>
            </div>
        </div>
    </form>
</div>

<script type="text/javascript">
    var id = ys.request("id");
    $(function () {
        getForm();
    });

    function getForm() {
        if (id > 0) {
            ys.ajax({
                url: '@Url.Content("~/SystemManage/LogApi/GetFormJson")' + '?id=' + id,
                type: "get",
                success: function (obj) {
                    if (obj.Tag == 1) {
                        var result = obj.Data;
                        result.UserName = (ys.isNullOrEmpty(result.UserName) ? "" : result.UserName) + " / ";
                        result.UserName += (ys.isNullOrEmpty(result.DepartmentName) ? "" : result.DepartmentName) + " / ";
                        if (result.LogStatus == "@OperateStatusEnum.Success.ParseToInt()") {
                            result.LogStatus = '<span class="label label-primary">' + "@OperateStatusEnum.Success.GetDescription()" + '</span>';
                        } else {
                            result.LogStatus = '<span class="label label-warning">' + "@OperateStatusEnum.Fail.GetDescription()" + '</span>';
                        }
                        $("#form").setWebControls(result);
                    }
                }
            });
        }
    }
</script>
