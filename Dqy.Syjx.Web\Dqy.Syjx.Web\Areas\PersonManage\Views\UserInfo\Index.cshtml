﻿@using Dqy.Syjx.Enum.BusinessManage
@using Dqy.Syjx.Enum.OrganizationManage
@{
    Layout = "~/Views/Shared/_FormWhite.cshtml";
}
<style type="text/css">
    .select2-container {
        display: block;
        width: auto !important;
    }
</style>
@inject Microsoft.AspNetCore.Hosting.IWebHostEnvironment HostingEnvironment
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/uploadifive/File.min.css"))
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/uploadifive/File.min.js"))
<div class="container-div">
    <div class="btn-group-sm hidden-xs" id="toolbar">
    </div>
    <div class="col-sm-12 select-table table-striped">
        <div class="ibox-title">
            <h5>个人基本信息</h5>
        </div>

        <div class="card-body">
            <form id="form" class="form-horizontal m">
                <input id="Id" col="Id" type="hidden" />
                <div class="form-group row">
                    <label class="col-sm-2 control-label ">姓名：<font class="red"> *</font></label>
                    <div class="col-sm-8">
                        <input id="RealName" col="RealName" type="text" class="form-control" />
                    </div>
                </div>
                <div class="form-group row">
                    <label class="col-sm-2 control-label ">手机号码：<font class="red"> *</font></label>
                    <div class="col-sm-8">
                        <input id="Mobile" col="Mobile" type="text" class="form-control" />
                    </div>
                </div>
                <div class="form-group row">
                    <label class="col-sm-2 control-label " id="labEmail">邮箱：</label>
                    <div class="col-sm-8">
                        <input id="Email" col="Email" type="text" class="form-control" />
                    </div>
                </div>
                <div class="form-group row">
                    <label class="col-sm-2 control-label " id="labGender">性别：</label>
                    <div class="col-sm-8">
                        <div id="Gender" col="Gender"></div>
                    </div>
                </div>

                <div class="form-group row">
                    <label class="col-sm-2 control-label " id="labBirthday">出生日期：</label>
                    <div class="col-sm-8">
                        <input id="Birthday" col="Birthday" type="text" class="form-control" />
                    </div>
                </div>
                <div class="form-group row">
                    <label class="col-sm-2 control-label " id="labEducation">学历：</label>
                    <div class="col-sm-8">
                        <div id="EducationLevel" col="EducationLevel"></div>
                    </div>
                </div>
                <div class="form-group row">
                    <label class="col-sm-2 control-label ">专业：</label>
                    <div class="col-sm-8">
                        <input id="Major" col="Major" type="text" class="form-control" />
                    </div>
                </div>
                <div class="form-group row">
                    <label class="col-sm-2 control-label " id="labJobTitle">职称：</label>
                    <div class="col-sm-8">
                        <div id="JobTitle" col="JobTitle"></div>
                    </div>
                </div>
                <div class="form-group row">
                    <label class="col-sm-2 control-label ">是否实验员：</label>
                    <div class="col-sm-8">
                        <div class='radio-box'>
                            <input type='radio' name='IsExperimenter' col='IsExperimenter' checked="checked" value='1' /><span id="spanIsExperimenter">--</span>
                        </div>
                    </div>
                </div>
                <div class="form-group divExperimenter" style="display:none;">
                    <label class="col-sm-2 control-label ">实验员性质：</label>
                    <div class="col-sm-8">
                        <div id="ExperimenterNature" col='ExperimenterNature' style="padding-top: 8px;"></div>
                    </div>
                </div>
                <div class="form-group divExperimenter" style="display:none;">
                    <label class="col-sm-2 control-label " id="labImage">实验员上岗证：</label>
                    <div class="col-sm-8">
                        <input type="file" name="uploadify" id="uploadify" />
                        <div id="spanFile" style="padding: 10px 0px;"></div>
                        <div style="height: 55px;display:none;" id="fileQueue"></div>
                    </div>
                    <div class="col-sm-2 tag_msg_color">
                        <span class="glyphicon glyphicon-question-sign titleprompt inputprompt" aria-hidden="true" data-container="body" data-toggle="tooltip" data-content="只支持PDF和图片格式"></span>
                    </div>
                </div>
            </form>
        </div>
        <div class="btn-group-sm hidden-xs" id="toolbar" style="text-align: center;">
            <a id="btnAdd" class="btn btn-info" onclick="saveForm();"><i class="fa fa-edit"></i> 保存</a>
        </div>
    </div>
</div>
<script type="text/javascript">
    var SaveValiDate = [];
    $(function () {
        $(".inputprompt").popover({
            trigger: 'hover',
            placement: 'left',
            html: true
        });
        $("#Gender").ysComboBox({
            data: ys.getJson(@Html.Raw(typeof(GenderTypeEnum).EnumToDictionaryString())),
            class: 'form-control'
        });
        $("#EducationLevel").ysComboBox({
            data: ys.getJson(@Html.Raw(typeof(EducationLevelEnum).EnumToDictionaryString())),
            class:'form-control'
        });
        $("#JobTitle").ysComboBox({
            data: ys.getJson(@Html.Raw(typeof(JobTiitleEnum).EnumToDictionaryString())),
            class: 'form-control'
        });
        laydate.render({ elem: '#Birthday', format: 'yyyy-MM-dd', trigger: 'click', position: 'fixed' });
        loadUploadify();
        getForm();

        loadPageSetValid();
    });
    function getForm() {
        ys.ajax({
            url: '@Url.Content("~/PersonManage/UserInfo/GetFormExtJson")',
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1) {
                    $('#form').setWebControls(obj.Data);
                    //处理学校，单位属性 单位性质
                    $("input[name='IsExperimenter']").val(obj.Data.IsExperimenter);
                    if (obj.Data.IsExperimenter == 1) {
                        $("#spanIsExperimenter").text('是');
                        //判断文件类型，如果未PDF则在新页面打开。
                        $('#experimenterCard').attr("href", obj.Data.Path);
                        /*$('#experimenterCard').text(obj.Data.CardName);*/
                        $('.divExperimenter').show();
                        //调用显示附件信息
                        if (isNaN(obj.Data.AttachmentList) && obj.Data.AttachmentList != undefined) {
                            var fileList = obj.Data.AttachmentList;
                            //先清空
                            var objDiv = $("#spanFile");
                            objDiv.html("");
                            if (fileList.length > 0) {
                                for (var i = 0; i < fileList.length; i++) {
                                    var attachmentid = fileList[i].Id;
                                    var filePath = fileList[i].Path;
                                    var fileExt = fileList[i].Ext.toUpperCase();
                                    var fileTitle = fileList[i].Title;
                                    if (fileExt == ".JPG" || fileExt == ".BMP" || fileExt == ".JPEG" || fileExt == ".PNG" || fileExt == ".GIF") {
                                        objDiv.append('<span class="keywords" idValue="' + attachmentid + '"> <a type="del" onclick="delFile(this,\'' + attachmentid + '\')"></a><a  modal="zoomImg"  href="javascript:void(0);" src="' + filePath + '" >' + fileTitle + '</a></span>');
                                    } else {
                                        objDiv.append('<span class="keywords" idValue="' + attachmentid + '"> <a type="del" onclick="delFile(this,\'' + attachmentid + '\')"></a><a target="_blank" href="' + filePath + '" >' + fileTitle + '</a></span>');
                                    }
                                }
                                imgages.showextalink();
                            }
                        }
                    } else {
                        $("#spanIsExperimenter").text('否');
                    }
                }
            }
        });
    }

    function saveForm() {
        if ($('#form').validate().form()) {
            var postData = $('#form').getWebControls();
            var imageArr = [];
            //验证
            var errorMsg = '';
            $(".keywords").each(function () {
                var idValue = $(this).attr("idValue");
                imageArr.push(idValue);
            });
            if (imageArr.length > 0) {
                postData.Imagez = imageArr.join(',');
            }

            if (SaveValiDate != undefined && SaveValiDate.length > 0) {
                SaveValiDate.map(function (item, i) {
                    if (item.typecode == "Gender") {
                        if (!(postData.Gender != undefined && postData.Gender > 0)) {
                            errorMsg += (item.verifyhint + '<br/>');
                        }
                    } else if (item.typecode == "Education") {
                        if (!(postData.EducationLevel != undefined && postData.EducationLevel > 0)) {
                            errorMsg += (item.verifyhint + '<br/>');
                        }
                    } else if (item.typecode == "JobTitle") {
                        if (!(postData.JobTitle != undefined && postData.JobTitle > 0)) {
                            errorMsg += (item.verifyhint + '<br/>');
                        }
                    } else if (item.typecode == "Image") {
                        if (!(imageArr != undefined && imageArr.length > 0)) {
                            errorMsg += (item.verifyhint + '<br/>');
                        }
                    } 
                });
            }

            if (errorMsg != '') {
                ys.msgError(errorMsg);
                return;
            }

            ys.ajax({
                url: '@Url.Content("~/PersonManage/UserInfo/SaveFormJson")',
                type: 'post',
                data: postData,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        }
    }
    //删除附件
    function delFile(obj, value) {
        $(obj).parent().remove();
        imgages.showextalink();
    }
    function loadUploadify() {
        $("#uploadify").uploadifive({
            'uploadScript': '/File/UploadFile?fc=@FileCategoryEnum.WorkLicense.ParseToInt()',
            'cancelImg': '~/lib/uploadifive/uploadifive-cancel.png',
            'buttonText': '上 传',
            //'formData': { "token": "", "UserId": "0", "folder": "/UploadFile/Attachment" },
            'queueID': 'fileQueue',
            'auto': true,
            'multi': false,
            'fileDesc': '支持格式：*.jpg;*.gif;*.png;*.pdf;',
            'fileExt': '*.jpg;*.gif;*.png;*.pdf;',
            'onAddQueueItem': function (file) {

            },
            'onUploadComplete': function (fileObj, response) {
                if (typeof (response) == "string" && response != "") {
                    response = JSON.parse(response);
                    if (response.Tag == 1) {
                        ys.msgSuccess("上传成功！");
                        var id = response.Description;
                        var filePath = response.Data;
                        var fileTitle = response.Message;
                        var fileExt = filePath.substring(filePath.lastIndexOf('.')).toUpperCase();
                        if (fileExt == ".JPG" || fileExt == ".BMP" || fileExt == ".JPEG" || fileExt == ".PNG" || fileExt == ".GIF") {
                            $("#spanFile").append('<span class="keywords" idValue="' + id + '"> <a type="del" onclick="delFile(this,\'' + id + '\')"></a><a  modal="zoomImg"  href="javascript:void(0);" src="' + filePath + '" >' + fileTitle + '</a></span>');
                        } else {
                            $("#spanFile").append('<span class="keywords" idValue="' + id + '"> <a type="del" onclick="delFile(this,\'' + id + '\')"></a><a target="_blank" href="' + filePath + '" >' + fileTitle + '</a></span>');
                        }
                        imgages.showextalink();
                    } else {
                        ys.msgError(response.Message);
                    }
                }
            },
            'onFallback': function (event) {
                ys.msgError(event);
            }
        });
    }

    /*获取数据库配置的填报验证*/
    function loadPageSetValid() {
        ys.ajax({
            url: '@Url.Content("~/SystemManage/ConfigSet/GetListByCode")',
            type: 'Post',
            data: { typecode:'4RYGL-1GRDA-1JBXX'},
            success: function (obj) {
                if (obj.Tag == 1) {
                    var validateRule = {};
                    var validateMessage = {};
                    validateRule.RealName = { required: true };
                    validateMessage.RealName = { required: "请填写姓名" };
                    validateRule.Mobile = { required: true, isPhone: true };
                    validateMessage.Mobile = { required: "请填写正确的手机号码" };

                    if (obj.Data != undefined && obj.Data.length > 0) {
                        for (var i = 0; i < obj.Data.length; i++) {
                            if (obj.Data[i].ConfigValue == "1") {
                                var typecode = obj.Data[i].TypeCode.replaceAll("4RYGL-1GRDA-1JBXX-", "");

                                var verifyhint = "";
                                if (obj.Data[i].VerifyHint != undefined && obj.Data[i].VerifyHint.length > 0) {
                                    verifyhint = obj.Data[i].VerifyHint;
                                }
                                $("#lab" + typecode).append('<font class="red"> *</font>');

                                if (typecode == "Email") {
                                    validateRule.Email = { required: true };
                                    validateMessage.Email = { required: verifyhint };
                                } else if (typecode == "Gender") {
                                    SaveValiDate.push({ typecode: typecode, verifyhint: verifyhint });
                                } else if (typecode == "Birthday") {
                                    validateRule.Birthday = { required: true };
                                    validateMessage.Birthday = { required: verifyhint };
                                } else if (typecode == "Education") {
                                    SaveValiDate.push({ typecode: typecode, verifyhint: verifyhint });
                                } else if (typecode == "JobTitle") {
                                    SaveValiDate.push({ typecode: typecode, verifyhint: verifyhint });
                                } else if (typecode == "Image") {
                                    SaveValiDate.push({ typecode: typecode, verifyhint: verifyhint });
                                }
                            }
                        }
                    }
                    $('#form').validate({
                        rules: validateRule,
                        messages: validateMessage
                    });
                }
            }
        });
    }
</script>
