﻿@{
    Layout = "~/Views/Shared/_FormWhite.cshtml";
}
@inject Microsoft.AspNetCore.Hosting.IWebHostEnvironment HostingEnvironment
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/fileinput/5.0.3/css/fileinput.min.css"))
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/fileinput/5.0.3/js/fileinput.min.js"))
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/uploadifive/File.min.css"))
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/uploadifive/File.min.js"))

<style type="text/css">
    .check-box {
        width: 100px;
    }

    .iradio-box {
        width: 100px;
    }

    .iradio-box {
        position: initial;
        display: inline-block;
    }

    .iradio-blue {
        position: inherit;
        display: inline-block;
        /*  top: 6px;*/
    }

    .div-experit {
        border: 1px dashed #999;
        margin: 15px 0px 15px 0px;
        padding-top: 15px;
    }
</style>
<div class="wrapper animated fadeInRight">
    <form id="form" class="form-horizontal m">
        <div class="form-group row">
            <label class="col-sm-2 control-label " id="labExperimentSum">实验总结</label>
            <div class="col-sm-7">
                <textarea id="ExperimentSummary" col="ExperimentSummary" class="form-control" placeholder="如实验内容、效果、改进意见等（限1000字）"></textarea>
            </div>
        </div>
        <div class="form-group row" id="divImageFile">
            <label class="col-sm-2 control-label " id="labImage">
                现场照片
            </label>
            <div class="col-sm-7">
                <input type="file" name="uploadify" id="uploadify" />
                <div id="spanFile" style="padding: 10px 0px;"></div>
                <div style="height: 55px;display:none;" id="fileQueue"></div>
                <span style="color:#999;font-size:12px;">上课场景照片不超三张，文件小于5M，支持图片文件</span>
            </div>

        </div>
        <div class="form-group run-info">
            <label class="col-sm-2 control-label ">设备运行<span style="color:red;">*</span></label>
            <div class="col-sm-7">
                <div id="runStatuz" style="display: inline-block;"></div>
            </div>
        </div>
        <div class="form-group run-info" id="divProblem" style="display:none;">
            <label class="col-sm-2 control-label ">问题描述<font class="red"> *</font></label>
            <div class="col-sm-7">
                <textarea id="ProblemDesc" col="ProblemDesc" class="form-control"></textarea>
            </div>
        </div>
    </form>
   @*  <div class="btn-group-sm hidden-xs col-sm-8" id="toolbar" style="text-align: center;">
        <a id="btnAdd" class="btn btn-info" onclick="saveForm();"><i class="fa fa-edit"></i> 保 存</a>
    </div> *@
</div>

<script type="text/javascript">
    var id = ys.request("id");
    var IsRelation = 0;
    var SaveValiDate = [];
    var IsRequiredExperimentSumNeed = 0;
    $(function () {
        $(".inputprompt").popover({
            trigger: 'hover',
            html: true
        });
        // parent.removeEditRecordBtn();
        loadRunStatuz();
        loadUploadify();

        getForm();
        loadPageSetValid();
    });

    function getForm() {
        if (id > 0) {
            ys.ajax({
                url: '@Url.Content("~/ExperimentTeachManage/ExperimentBooking/GetRecordDetailFormJson")' + '?id=' + id,
                type: 'get',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        if (obj.Data != undefined) {
                            var thisObj = obj.Data;
                            IsRelation = parseInt(thisObj.IsRelationCamera);
                            $("#ExperimentSummary").val(thisObj.ExperimentSummary);
                            console.log("---obj.Data.RunStatuz----:" + thisObj.RunStatuz);
                            selectRunStatuz(thisObj.RunStatuz);
                            $("#ProblemDesc").val(thisObj.ProblemDesc);
                            if (IsRelation == 1) {
                                $("#divImageFile").hide();
                            } else {
                                loadAttachmentList(thisObj.AttachmentList, id);
                            }
                            if (obj.Data.FunRoomId == 1) {
                                //普通教室，不显示设备运行情况  2022-03-28 by lss
                                $(".run-info").hide();
                                $(".normal").addClass("checked");//普通教室默认运行正常
                                $(".abnormal").removeClass("checked");
                            }
                        }
                    }
                }
            });
        }
    }

    function loadRunStatuz() {
        var html = '';
        html += '<label class="iradio-box"><div class="iradio-blue normal" style="vertical-align:bottom;"><input name="inputType_checkbox" type="radio" checked value="@RunStatuzEnum.Normal.ParseToInt()" style="position: absolute; opacity: 0;"></div> @RunStatuzEnum.Normal.GetDescription()</label>';
        html += '<label class="iradio-box"><div class="iradio-blue abnormal" style="vertical-align:bottom;"><input name="inputType_checkbox" type="radio" value="@RunStatuzEnum.Abnormal.ParseToInt()" style="position: absolute; opacity: 0;"></div> @RunStatuzEnum.Abnormal.GetDescription()</label>';
        $("#runStatuz").html(html);
        $(".normal").addClass("checked");

        $('input[name="inputType_checkbox"]').change(function () {
            if ($(this).prop("checked")) {
                var val = $(this).val();
                selectRunStatuz(val);
            }
        });
    }
    function selectRunStatuz(val) {
        if (val == @RunStatuzEnum.Normal.ParseToInt()) {
            $(".normal").addClass("checked");
            $(".abnormal").removeClass("checked");
            $('#divProblem').hide();
        } else {
            $(".abnormal").addClass("checked");
            $(".normal").removeClass("checked");
            $('#divProblem').show();
        }
    }
    function loadUploadify() {
        $("#uploadify").uploadifive({
            'uploadScript': '/File/UploadFile?fc=@FileCategoryEnum.ExperimentSceneImg.ParseToInt()',
            'cancelImg': '~/lib/uploadifive/uploadifive-cancel.png',
            'buttonText': '上 传',
            'queueID': 'fileQueue',
            'auto': true,
            'multi': false,
            'fileDesc': '支持格式：*.jpg;*.gif;*.png;*.pdf;',
            'fileExt': '*.jpg;*.gif;*.png;*.pdf;',
            'onAddQueueItem': function (file) {

            },
            'onUploadComplete': function (fileObj, response) {
                if (typeof (response) == "string" && response != "") {
                    response = JSON.parse(response);
                    if (response.Tag == 1) {
                        ys.msgSuccess("上传成功！");
                        var id = response.Description;
                        var filePath = response.Data;
                        var fileTitle = response.Message;
                        var fileExt = filePath.substring(filePath.lastIndexOf('.')).toUpperCase();
                        if (fileExt == ".JPG" || fileExt == ".BMP" || fileExt == ".JPEG" || fileExt == ".PNG" || fileExt == ".GIF") {
                            $("#spanFile").append('<span class="keywords" idValue="' + id + '"> <a type="del" onclick="delFile(this,\'' + id + '\')"></a><a  modal="zoomImg"  href="javascript:void(0);" src="' + filePath + '" >' + fileTitle + '</a></span>');
                        } else {
                            $("#spanFile").append('<span class="keywords" idValue="' + id + '"> <a type="del" onclick="delFile(this,\'' + id + '\')"></a><a target="_blank" href="' + filePath + '" >' + fileTitle + '</a></span>');
                        }
                        imgages.showextalink();
                    } else {
                        ys.msgError(response.Message);
                    }
                }
            },
            'onFallback': function (event) {
                ys.msgError(event);
            }
        });
    }
    //加载图片
    function loadAttachmentList(attArr) {
        if (attArr != undefined && attArr.length > 0) {
            var obj = $("#spanFile");
            if (obj) {
                for (var j = 0; j < attArr.length; j++) {
                    obj.append(getImageHtml(attArr[j]));
                }
                imgages.showextalink();
            }
        }
    }

    function getImageHtml(obj) {
        var html = '';
        var fileExt = obj.Ext;
        var attid = obj.Id;
        var filePath = obj.Path;
        var fileTitle = obj.Title;
        if (fileExt == ".JPG" || fileExt == ".BMP" || fileExt == ".JPEG" || fileExt == ".PNG" || fileExt == ".GIF") {
            html = '<span class="keywords" idValue="' + attid + '"> <a type="del" onclick="delFile(this,\'' + attid + '\')"></a><a  modal="zoomImg"  href="javascript:void(0);" src="' + filePath + '" >' + fileTitle + '</a></span>';
        } else {
            html = '<span class="keywords" idValue="' + attid + '"> <a type="del" onclick="delFile(this,\'' + attid + '\')"></a><a target="_blank" href="' + filePath + '" >' + fileTitle + '</a></span>';
        }
        return html;
    }
    function saveForm(index) {
        var runStatuz = $('input[name="inputType_checkbox"]:checked').val();
        if (runStatuz > 0) {
            var msg = '';
            var problemDesc = $('#ProblemDesc').val();
            if (runStatuz == @RunStatuzEnum.Abnormal.ParseToInt() && problemDesc == '') {
                msg += '设备不正常必须填写问题描述！<br />';
            }

            var imageArr = [];
            if (IsRelation == 0) {
                $(".keywords").each(function () {
                    var idValue = $(this).attr("idValue");
                    imageArr.push(idValue);
                });
            }
            var experimentSum = $("#ExperimentSummary").val();
            if (SaveValiDate != undefined && SaveValiDate.length > 0) {
                SaveValiDate.map(function (item, i) {
                    if (item.typecode == "ExperimentSum") {
                        if (experimentSum == '') {
                            msg += (item.verifyhint + '。<br/>');
                            return false;
                        }
                    } else if (item.typecode == "Image") {
                        if (IsRelation == 0) {
                            if (!(imageArr != undefined && imageArr.length > 0)) {
                                msg += (item.verifyhint + '。<br/>');
                            }
                        }
                    }
                });
            }
            if (msg != '') {
                ys.msgError(msg);
                return false;
            }

            ys.confirm('您确认修改登记信息吗？', function () {
                var postData = {
                    Id: id,
                    ExperimentSummary: experimentSum,
                    RunStatuz: runStatuz,
                    ProblemDesc: problemDesc
                }
                if (imageArr.length > 0) {
                    postData.AttachmentIds = imageArr.join(',');
                }
                ys.ajax({
                    url: '@Url.Content("~/ExperimentTeachManage/ExperimentBooking/SaveEditRecordJson")',
                    type: 'post',
                    data: { model: postData },
                    success: function (obj) {
                        if (obj.Tag == 1) {
                            ys.msgSuccess(obj.Message);
                            parent.layer.close(index);
                        }
                        else {
                            ys.msgError(obj.Message);
                        }
                    }
                });
            });
        }
        else {
            ys.msgError('请选择设备运行情况！');
        }
    }
    //删除附件
    function delFile(obj, value) {
        $(obj).parent().remove();
        imgages.showextalink();
    }

    /*获取数据库配置的填报验证*/
    function loadPageSetValid(experimentSum, imageArray) {
        var validateMsg = '';
        ys.ajax({
            url: '@Url.Content("~/SystemManage/ConfigSet/GetListByCode")',
            type: 'Post',
            data: { typecode: '3SYJX-3SYDJ-1DJSY' },
            async: false,
            success: function (obj) {
                if (obj.Tag == 1) {
                    if (obj.Data != undefined && obj.Data.length > 0) {
                        for (var i = 0; i < obj.Data.length; i++) {
                            if (obj.Data[i].ConfigValue == "1") {
                                var typecode = obj.Data[i].TypeCode.replaceAll("3SYJX-3SYDJ-1DJSY-", "");
                                var verifyhint = "";
                                if (obj.Data[i].VerifyHint != undefined && obj.Data[i].VerifyHint.length > 0) {
                                    verifyhint = obj.Data[i].VerifyHint;
                                }
                                $("#lab" + typecode).append('<font class="red"> *</font>');

                                if (typecode == "ExperimentSum") {
                                    IsRequiredExperimentSumNeed = 1;
                                    SaveValiDate.push({ typecode: typecode, verifyhint: verifyhint });
                                } else if (typecode == "Image") {
                                    SaveValiDate.push({ typecode: typecode, verifyhint: verifyhint });
                                }
                            }
                        }
                    }
                }
            }
        });
    }
</script>
