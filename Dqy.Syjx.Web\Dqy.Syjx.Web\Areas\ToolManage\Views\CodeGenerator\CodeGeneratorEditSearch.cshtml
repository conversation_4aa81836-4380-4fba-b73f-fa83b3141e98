﻿@{
    Layout = "~/Views/Shared/_FormWhite.cshtml";
}

<div class="wrapper animated fadeInRight">
    <form id="form" class="form-horizontal m">
        <div class="form-group row">
            <label class="col-sm-3 control-label">是否需要显示搜索</label>
            <div class="col-sm-8" id="searchStatus"></div>
        </div>
    </form>
</div>

<script type="text/javascript">
    $(function () {

        var divSearch = $("#divSearch", parent.document);

        $("#searchStatus").ysRadioBox({ data: ys.getJson(@Html.Raw(typeof(NeedEnum).EnumToDictionaryString())) });

        $('input').on('ifChecked', function (event) {
            var val = $(this).val();
            if (val == 1) {
                divSearch.show();
            }
            else {
                divSearch.hide();
            }
        });

        // 设置默认值
        if (divSearch.is(':hidden')) {
            $("#searchStatus").ysRadioBox('setValue',0);
        }
        else {
            $("#searchStatus").ysRadioBox('setValue',1);
        }

    });


</script>
