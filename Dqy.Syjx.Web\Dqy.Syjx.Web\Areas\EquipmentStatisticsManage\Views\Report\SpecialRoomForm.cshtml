﻿@{
    Layout = "~/Views/Shared/_FormWhite.cshtml";
}
<style type="text/css">
    .select2-container {
        display: block;
        width: auto !important;
    }
</style>
<div class="wrapper animated fadeInRight">
    <form id="form" class="form-horizontal m">
        <div class="form-group row">
            <label class="col-sm-3 control-label ">分类名称<font class="red"> *</font></label>
            <div class="col-sm-7">
                <div id="CategoryId" col="CategoryId" style="width:100%;"></div>
                <span style="display:none;">    <input id="CategoryName" col="CategoryName" type="text" required class="form-control" autocomplete="off" /></span>
            </div>
            <div class="col-sm-2"> </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">专用室名称<font class="red"> *</font></label>
            <div class="col-sm-7">
                <input id="Name" col="Name" type="text" required class="form-control" autocomplete="off" />
            </div>
            <div class="col-sm-2"> </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">
                使用面积 （平米）<font class="red"> *</font>
            </label>
            <div class="col-sm-7">
                <input id="UseArea" col="UseArea" type="text" required class="form-control" autocomplete="off" />
            </div>
            <div class="col-sm-2"> </div>
        </div>
        
        <div class="form-group row">
            <label class="col-sm-3 control-label ">起初建设时间<font class="red"> *</font></label>
            <div class="col-sm-7">
                <input id="BuildTime" col="BuildTime" type="text" required readonly class="form-control" autocomplete="off" />
            </div>
            <div class="col-sm-2"> </div>
        </div>
      
        <div class="form-group row">
            <label class="col-sm-3 control-label ">最新改造时间<font class="red"> *</font></label>
            <div class="col-sm-7">
                <input id="ReformTime" col="ReformTime" type="text" required readonly class="form-control" autocomplete="off" />
            </div>
            <div class="col-sm-2"> </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">备注</label>
            <div class="col-sm-7">
                <input id="Remark" col="Remark" type="text" class="form-control" autocomplete="off" />
            </div>
            <div class="col-sm-2"> </div>
        </div>
    </form>
</div>
<script type="text/javascript">
    var id = ys.request("id");
    var CategoryArr = [];
    $(function () {
        bindCategoryId();
        laydate.render({ elem: '#BuildTime', format: 'yyyy-MM-dd', trigger: 'click', position: 'fixed' });
        laydate.render({ elem: '#ReformTime', format: 'yyyy-MM-dd', trigger: 'click', position: 'fixed' });
        getForm();
    });
    function bindCategoryId() {
        CategoryArr.push({ id: "1", name: "音乐类" });
        CategoryArr.push({ id: "2", name: "美工类" });
        CategoryArr.push({ id: "3", name: "科学类" });
        CategoryArr.push({ id: "4", name: "建构类" });
        CategoryArr.push({ id: "5", name: "体育类" });
        CategoryArr.push({ id: "6", name: "其他类" });

        $("#CategoryId").ysComboBox({
            data: CategoryArr,
            key: 'id',
            value: 'name',
            defaultName: '分类名称' ,
            onChange: function () {
                var selectid = $("#CategoryId").ysComboBox("getValue");
                CategoryArr.forEach(function (item,index,obj) { 
                    if (item.id == selectid) {
                        $("#CategoryName").val(item.name);
                    }
                });
            }
        });
        
    }
    function getForm() {
        if (id > 0) {
            ys.ajax({
                url: '@Url.Content("~/EquipmentStatisticsManage/Report/GetSpecialRoomFormJson")' + '?id=' + id,
                type: 'get',
                async: false,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        $('#form').setWebControls(obj.Data);
                    }
                }
            });
        } else {
            var defaultData = {};
            $('#form').setWebControls(defaultData);
        }
    }

    function saveForm(index) {
        if ($('#form').validate().form()) {
            var postData = $('#form').getWebControls({ Id: 0 });
            var errorMsg = '';
            // if (!(parseInt(postData.SchoolYearStart) > 0)) {
            //     errorMsg += '请选择学年。<br/>';
            // }
            // if (!(parseInt(postData.SchoolTerm) > 0)) {
            //     errorMsg += '请选择学期。<br/>';
            // }
            // if (!(parseInt(postData.CourseId) > 0)) {
            //     errorMsg += '请选择学科。<br/>';
            // }
            // if (!(parseInt(postData.GradeId) > 0)) {
            //     errorMsg += '请选择年级。<br/>';
            // }
            // if (!(parseInt(postData.VersionCurrentId) > 0)) {
            //     errorMsg += '请选择教材版本。<br/>';
            // }
            if (errorMsg != '') {
                ys.msgError('验证失败，请填写完成再提交！<br/>' + errorMsg);
                return;
            }
            ys.ajax({
                url: '@Url.Content("~/EquipmentStatisticsManage/Report/SaveSpecialRoomFormJson")',
                type: 'post',
                data: postData,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        // parent.loadEquipmentData(obj.Data);
                        // parent.layer.close(index);
                    } else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        }
    }
</script>