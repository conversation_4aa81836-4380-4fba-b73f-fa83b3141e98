﻿@{
    Layout = "~/Views/Shared/_FormWhite.cshtml";
}

<div class="wrapper animated fadeInRight">
    <form id="form" class="form-horizontal m">
        <div class="form-group row">
            <label class="col-sm-3 control-label ">字典类型<font class="red"> *</font></label>
            <div class="col-sm-8">
                <input id="dictType" col="DictType" type="text" class="form-control" readonly="readonly" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">字典键<font class="red"> *</font></label>
            <div class="col-sm-8">
                <div>
                    <input id="dictKey" col="DictKey" type="text" class="form-control" />
                </div>
                <span><i class="fa fa-info-circle"></i> 第一个键一般从1开始</span>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">字典值<font class="red"> *</font></label>
            <div class="col-sm-8">
                <input id="dictValue" col="DictValue" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">字典排序</label>
            <div class="col-sm-8">
                <input id="dictSort" col="DictSort" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">显示样式</label>
            <div class="col-sm-8">
                <div id="listClass" col="ListClass"></div>
                <span class="help-block m-b-none"><i class="fa fa-info-circle"></i> table表格字典列显示样式属性</span>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">系统默认</label>
            <div id="isDefault" col="IsDefault" class="col-sm-8"></div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">状态</label>
            <div id="dictStatus" col="DictStatus" class="col-sm-8"></div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">备注</label>
            <div class="col-sm-8">
                <input id="remark" col="Remark" type="text" class="form-control" style="height:50px" />
            </div>
        </div>
    </form>
</div>

<script type="text/javascript">
    var id = ys.request("id");
    var dictType = ys.request("dictType");
    $(function () {
        $("#dictStatus").ysRadioBox({ data: ys.getJson(@Html.Raw(typeof(StatusEnum).EnumToDictionaryString())), default: '@StatusEnum.Yes.ParseToInt()' });
        $("#isDefault").ysRadioBox({ data: ys.getJson(@Html.Raw(typeof(IsEnum).EnumToDictionaryString())) , default: '@IsEnum.No.ParseToInt()'  });
        $("#listClass").ysComboBox({
            data: [{ Key: 'default', Value: '默认' }, { Key: 'primary', Value: '主要' }, { Key: 'success', Value: '成功' }, { Key: 'info', Value: '信息' }, { Key: 'warning', Value: '警告' }, { Key: 'danger', Value: '危险' }],
            class: "form-control"
        });

        getForm();

        $("#form").validate({
            rules: {
                dictKey: { required: true, digits: true },
                dictValue: { required: true }
            }
        });
    });

    function getForm() {
        if (id > 0) {
            ys.ajax({
                url: '@Url.Content("~/SystemManage/DataDictDetail/GetFormJson")' + '?id=' + id,
                type: "get",
                success: function (obj) {
                    if (obj.Tag == 1) {
                        $("#form").setWebControls(obj.Data);
                    }
                }
            });
        }
        else {
            ys.ajax({
                url: '@Url.Content("~/SystemManage/DataDictDetail/GetMaxSortJson")',
                type: "get",
                success: function (obj) {
                    if (obj.Tag == 1) {
                        var defaultData = {};
                        defaultData.DictSort = obj.Data;
                        defaultData.DictType = dictType;
                        $("#form").setWebControls(defaultData);
                    }
                }
            });
        }
    }

    function saveForm(index) {
        if ($("#form").validate().form()) {
            var postData = $("#form").getWebControls({ Id: id });
            ys.ajax({
                url: '@Url.Content("~/SystemManage/DataDictDetail/SaveFormJson")',
                type: "post",
                data: postData,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        if (top.initDataDict) {
                            top.initDataDict();
                        }
                        ys.msgSuccess(obj.Message);
                        parent.$('#gridTable').ysTable('refresh');parent.resetToolbarStatus();
                        parent.layer.close(index);
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        }
    }
</script>

