﻿@{
    Layout = "~/Views/Shared/_FormWhite.cshtml";
}

<div class="wrapper animated fadeInRight">
    <form id="form" class="form-horizontal m">
       @* <div class="form-group row">
            <label class="col-sm-3 control-label ">名称</label>
            <div class="col-sm-8">
                <input id="name" col="Name" type="text" class="form-control" readonly />
            </div>
        </div>*@
        <div class="form-group row">
            <label class="col-sm-3 control-label ">排查人</label>
            <div class="col-sm-8">
                <input id="checkUserName" col="CheckUserName" type="text" class="form-control" readonly/>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">排查时间</label>
            <div class="col-sm-8">
                <input id="checkTime" col="CheckTime" type="text" class="form-control" readonly/>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">排查结果</label>
            <div class="col-sm-8">
                <input id="checkResult" col="CheckResult" type="text" class="form-control" readonly/>
            </div>
        </div>
        <div id="divInfo" style="display:none;">
            <div class="form-group row">
                <label class="col-sm-3 control-label ">问题隐患</label>
                <div class="col-sm-8">
                    <textarea id="problemDanger" col="ProblemDanger" class="form-control" style="height:60px" readonly></textarea>
                </div>
            </div>
            <div class="form-group row">
                <label class="col-sm-3 control-label ">是否整改</label>
                <div class="col-sm-8">
                    <input id="isRectification" col="IsRectification" type="text" class="form-control" readonly/>
                </div>
            </div>
            <div class="form-group row">
                <label class="col-sm-3 control-label ">整改时间</label>
                <div class="col-sm-8">
                    <input id="rectificationTime" col="RectificationTime" type="text" class="form-control" readonly/>
                </div>
            </div>
        </div>

    </form>
</div>

<script type="text/javascript">
    var id = ys.request("id");
    $(function () {

        getForm();

    });

    function getForm() {
        if (id > 0) {
            ys.ajax({
                url: '@Url.Content("~/QueryStatisticsManage/FunRoom/GetFormJson")' + '?id=' + id,
                type: 'get',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        //console.log("obj.Data:" + JSON.stringify(obj.Data));
                        $('#form').setWebControls(obj.Data);
                        var CheckTime = ys.formatDate(obj.Data.CheckTime, "yyyy-MM-dd");
                        $("#checkTime").val(CheckTime);
                        if (obj.Data.CheckResult == @CheckResultEnum.Yes.ParseToInt()) {
                            $("#checkResult").val("@CheckResultEnum.Yes.GetDescription()");
                            $("#divInfo").hide();
                        } else {
                            $("#checkResult").val("@CheckResultEnum.No.GetDescription()");
                            $("#divInfo").show();
                            var RectificationTime = ys.formatDate(obj.Data.RectificationTime, "yyyy-MM-dd");
                            if (RectificationTime != "1970-01-01") {
                                $("#rectificationTime").val(RectificationTime);
                            } else {
                                $("#rectificationTime").val("");
                            }

                            if (obj.Data.IsRectification == @IsEnum.Yes.ParseToInt()) {
                                $("#isRectification").val("@IsEnum.Yes.GetDescription()");
                            } else {
                                $("#isRectification").val("@IsEnum.No.GetDescription()" );
                            }
                        }


                    }
                }
            });
        }
    }
</script>

