﻿@{
Layout = "~/Views/Shared/_FormWhite.cshtml";
}
<div class="wrapper animated fadeInRight">
    <form id="form" class="form-horizontal m">
        <div class="form-group row">
            <label class="col-sm-3 control-label ">单位名称<font class="red"> *</font></label>
            <div class="col-sm-8">
                <div id="UnitId" col="UnitId" style="width: 100%; display: block;"></div>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">学段<font class="red"> *</font></label>
            <div class="col-sm-8">
                <div id="SchoolStage" col="SchoolStage" style="width: 100%; display: block;"></div>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">年级<font class="red"> *</font></label>
            <div class="col-sm-8">
                <div id="GradeId" col="GradeId" style="width: 100%; display: block;"></div>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">班级总数<font class="red"> *</font></label>
            <div class="col-sm-8">
                <input id="ClassNum" col="ClassNum" type="text" class="form-control" autocomplete="off" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">年级学生总数<font class="red"> *</font></label>
            <div class="col-sm-8">
                <input id="StudentNum" col="StudentNum" type="text" class="form-control" autocomplete="off" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">班级名称前缀</label>
            <div class="col-sm-8">
                <input id="ClassNamePrefix" col="ClassNamePrefix" type="text" class="form-control" autocomplete="off" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">班级名称后缀</label>
            <div class="col-sm-8">
                <input id="ClassNamePostfix" col="ClassNamePostfix" type="text" class="form-control" autocomplete="off" />
            </div>
        </div>
    </form>
</div>
<script type="text/javascript">
$(function() {
    $('#form').validate({
        rules: {
            UnitId: { required: true },
            SchoolStage: { required: true },
            GradeId: { required: true },
            ClassNum: { required: true, number: true },
            StudentNum: { required: true, number: true }
        }
    });
    $('#UnitId').ysComboBox({
        url: '@Url.Content("~/OrganizationManage/Unit/GetListJson")' + "?UnitType=3",
        class: 'form-control col-sm-8',
        key: 'Id',
        value: 'Name',
        onChange: function() {
            var selectid = $('#UnitId').ysComboBox('getValue');
            loadSchoolStage(selectid);
        }
    });
    loadSchoolStage(0);
    loadGrade(0);
});

function loadSchoolStage(unitid) {
    if (unitid != undefined && parseInt(unitid) > 0) {
        $('#SchoolStage').ysComboBox({
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetListJson")' + "?TypeCode=1002&UnitId=" + unitid,
            class: 'form-control col-sm-8',
            key: 'DictionaryId',
            value: 'DicName',
            onChange: function() {
                var selectid = parseInt($('#SchoolStage').ysComboBox('getValue'));
                loadGrade(selectid);
            }
        });
    } else {
        $('#SchoolStage').ysComboBox({
            data: [],
            class: 'form-control col-sm-8',
            key: 'DictionaryId',
            value: 'DicName',
            onChange: function() {
                var selectid = parseInt($('#SchoolStage').ysComboBox('getValue'));
                loadGrade(selectid);
            }
        });
    }
}

function loadGrade(stageid) {
    if (stageid > 0) {
        $('#GradeId').ysComboBox({ url: '@Url.Content("~/SystemManage/StaticDictionary/GetListJson")' + "?TypeCode=1003&Pid=" + stageid, class: 'form-control col-sm-8', key: 'DictionaryId', value: 'DicName' });
    } else {
        $('#GradeId').ysComboBox({ data: [], class: 'form-control col-sm-8', key: 'DictionaryId', value: 'DicName' });
    }
}

function getForm() {
    if (id > 0) {
        ys.ajax({
            url: '@Url.Content("~/OrganizationManage/SchoolGradeClass/GetFormJson")' + '?id=' + id,
            type: 'get',
            success: function(obj) {
                if (obj.Tag == 1) {
                    $('#form').setWebControls(obj.Data);
                }
            }
        });
    } else {
        var defaultData = {};
        $('#form').setWebControls(defaultData);
    }
}

function saveForm(index) {
    if ($('#form').validate().form()) {
        var postData = $('#form').getWebControls();
        var errorMsg = '';
        //前缀、后缀不能太长。
        if (postData.ClassNamePrefix != undefined && postData.ClassNamePrefix.length > 10) {
            errorMsg += '班级名称前缀字符长度请不要超过10</br>';
        }
        if (postData.ClassNamePostfix != undefined && postData.ClassNamePostfix.length > 10) {
            errorMsg += '班级名称后字符长度请不要超过</br>';
        }
        if (errorMsg != '') {
            ys.msgError(errorMsg);
            return;
        }
        if (parseFloat(postData.StudentNum / postData.ClassNum) >= 70) {
            ys.msgError("验证失败：单个班级学生数必须小于70。");
        } else if (parseFloat(postData.StudentNum / postData.ClassNum) < 20 || parseFloat(postData.StudentNum / postData.ClassNum) > 50) {
            ys.confirm("班级学生数不在正常范围内20~50，确定要继续创建吗？", function() {
                submintObj(index, postData);
            });
        } else {
            submintObj(index, postData);
        }
    }
}

function submintObj(index, postData) {
    ys.ajax({
        url: '@Url.Content("~/OrganizationManage/SchoolGradeClass/SaveFormJson")',
        type: 'post',
        data: postData,
        success: function(obj) {
            if (obj.Tag == 1) {
                ys.msgSuccess(obj.Message);
                parent.$('#gridTable').ysTable('refresh');
                parent.resetToolbarStatus();
                parent.loadClassInfo();
                parent.layer.close(index);
            } else {
                ys.msgError(obj.Message);
            }
        }
    });
}
</script>