//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Reflection;

[assembly: System.Reflection.AssemblyCompanyAttribute("<PERSON> Yang(杨中科)")]
[assembly: System.Reflection.AssemblyConfigurationAttribute("Debug")]
[assembly: System.Reflection.AssemblyDescriptionAttribute(@"Using this library, Entity Framework Core users can delete or update multiple records from a LINQ Query in a SQL statement without loading entities. This libary supports Entity Framework Core 6.x.
This package is a basic support package, so it can not be used directly. 
Please use the database-specific packages, like  Zack.EFCore.Batch.Npgsql, Zack.EFCore.Batch.MySQL.Pomelo, Zack.EFCore.Batch.Npgsql, etc.")]
[assembly: System.Reflection.AssemblyFileVersionAttribute("*******")]
[assembly: System.Reflection.AssemblyInformationalVersionAttribute("6.1.3")]
[assembly: System.Reflection.AssemblyProductAttribute("Zack.EFCore.Batch_NET6")]
[assembly: System.Reflection.AssemblyTitleAttribute("Zack.EFCore.Batch_NET6")]
[assembly: System.Reflection.AssemblyVersionAttribute("*******")]
[assembly: System.Reflection.AssemblyMetadataAttribute("RepositoryUrl", "https://github.com/yangzhongke/Zack.EFCore.Batch")]

// 由 MSBuild WriteCodeFragment 类生成。

