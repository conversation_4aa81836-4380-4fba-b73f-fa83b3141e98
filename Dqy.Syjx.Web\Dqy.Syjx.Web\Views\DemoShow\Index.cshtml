﻿@inject Microsoft.AspNetCore.Hosting.IWebHostEnvironment HostingEnvironment
@{
    ViewBag.Title = "演示";
    Layout = "~/Views/Shared/_Layout.cshtml";

}

@if (ViewBag.AllowUse)
{
<link href="~/lib/uploadifive/uploadifive.css" rel="stylesheet" />
<script src="~/lib/uploadifive/jquery.uploadifive.js"></script>
<div class="form-group row">
    <table>
        <tr>
            <th>浏览文件</th>
            <td>
                <input id="hFileType" name="FileType" type="hidden" />
                <input type="file" name="uploadify" id="uploadify" />
                <input id="hImageSrc" name="Path" type="hidden" />
                <input id="hFileName" name="FileName" type="hidden" />
                <span id="spanLink"></span>               
            </td>
            <td></td>
        </tr>
        <tr>
            <th></th>
            <td>
                <div style="width: 300px; height: 55px;" id="fileQueue"></div>
            </td>
            <td></td>
        </tr>
    </table>
</div>

<script type="text/javascript">

    $(function () {


        $("#uploadify").uploadifive({
            'uploadScript': '/File/UploadFile?fc=1000',
            'cancelImg': '~/lib/uploadifive/uploadifive-cancel.png',
            'buttonText': '选 择',
            //'formData': { "token": "", "UserId": "0", "folder": "/UploadFile/Attachment" },
            'queueID': 'fileQueue',
            'auto': true,
            'multi': true,
            'onAddQueueItem': function (file) {
                ////读取值
                //var fileType = $("#hidImageType").val().toLowerCase();
                //if (p.checkIsAllowUpload(file.name, fileType))
                //    return true;
                //else {
                //    $('#uploadify').uploadifive('cancel', $('.uploadifive-queue-item').first().data('file'));
                //    $('#spanLink').html('<font style="color:red;">上传文件只能是(' + fileType + ')格式。</font>');
                //    return false;
                //}
            },
            'onUploadComplete': function (fileObj, response) {
                $('#spanLink').append(response);
                //if (typeof (response) == "string" && response != "") {
                //    response = JSON.parse(response);
                //    if (response.error == -1) {
                //        $('#spanLink').text(response.name);
                //        return;
                //    } else {
                //        $('#hImageSrc').val(response.path);
                //        $("#fileQueue").html("")
                //        $('#spanLink').text('上传成功，请单击保存按钮，保存文件。');
                //    }
                //}
                //$('#hFileName').val(response.sourcename);
            },
            'onFallback': function (event) {
                alert(event);
            }
        });

    });

   
</script>

}
else{
    <div>禁止使用本功能！</div>
}