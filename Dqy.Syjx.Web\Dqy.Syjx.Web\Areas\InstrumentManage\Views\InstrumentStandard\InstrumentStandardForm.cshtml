﻿@{
    Layout = "~/Views/Shared/_FormWhite.cshtml";
}

<div class="wrapper animated fadeInRight">
    <form id="form" class="form-horizontal m">
        <div class="form-group parent">
            <label class="col-sm-3 control-label ">上级分类</label>
            <div class="col-sm-8">
                <input id="parentName" type="text" class="form-control" readonly />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">分类代码<font class="red"> *</font></label>
            <div class="col-sm-8">
                <input id="code" col="Code" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">规格属性<font class="red"> *</font></label>
            <div class="col-sm-8">
                <input id="model" col="Model" type="text" class="form-control" />
            </div>
        </div>
    </form>
</div>

<script type="text/javascript">
    var pid = ys.request("pid");
    var id = ys.request("id");
    $(function () {
        if (id > 0) {
            $('.parent').hide();
        }
        getForm();

        $("#form").validate({
            rules: {
                code: { required: true },
                model: { required: true }
            }
        });
    });

    function getForm() {
        ys.ajax({
            url: '@Url.Content("~/InstrumentManage/InstrumentStandard/GetEntity")' + '?id=' + (id > 0 ? id : pid),
            type: "get",
            success: function (obj) {
                if (obj.Tag == 1) {
                    if (id > 0) {
                        pid = obj.Data.Pid;
                        $("#form").setWebControls(obj.Data);
                    }
                    else {
                        $('#parentName').val(obj.Data.Name);
                    }
                }
            }
        });
    }

    function saveForm(index) {
        if ($('#form').validate().form()) {
            var postData = $("#form").getWebControls({ Id: id, Pid: pid });
            ys.ajax({
                url: '@Url.Content("~/InstrumentManage/InstrumentStandard/SaveFormJson")',
                type: 'post',
                data: postData,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        parent.searchTreeGrid(pid);
                        parent.layer.close(index);
                    }
                    else {
                        ys.alertError(obj.Message);
                    }
                }
            });
        }
    }
</script>

