﻿@{
    Layout = "~/Views/Shared/_FormWhite.cshtml";
}

<div class="wrapper animated fadeInRight">
    <form id="form" class="form-horizontal m">
        <div class="form-group row">
            <label class="col-sm-3 control-label ">默认评估项目<font class="red"> *</font></label>
            <div class="col-sm-8" id="evaluateProjectId" col="EvaluateProjectId">
            </div>
        </div>
    </form>
</div>

<script type="text/javascript">
    $(function () {
        loadEvaluateProject();
    });

    function loadEvaluateProject() {
        $("#evaluateProjectId").ysComboBox({
            url: '@Url.Content("~/EvaluateManage/InstrumentEvaluateProject/GetProjectListJson")',
            class: 'form-control',
            key: 'Id',
            value: 'EvaluateName'
        });
    }

    function saveForm(index) {
        if ($('#form').validate().form()) {
            var evaluateProjectId = $("#evaluateProjectId").ysComboBox('getValue');
            if (!(evaluateProjectId > 0)) {
                ys.msgError('请选择评估项目！');
                return false;
            }
            ys.ajax({
                url: '@Url.Content("~/EvaluateManage/InstrumentEvaluateProject/SetDefaultFormJson")',
                type: 'post',
                data: { id: evaluateProjectId},
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        parent.$('#gridTable').ysTable('refresh');parent.resetToolbarStatus();
                        parent.layer.close(index);
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        }
    }
</script>

