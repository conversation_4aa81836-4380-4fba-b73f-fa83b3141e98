﻿@{ Layout = "~/Views/Shared/_FormWhite.cshtml"; }

@inject Microsoft.AspNetCore.Hosting.IWebHostEnvironment HostingEnvironment
@section header{
    @BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/fileinput/5.0.3/css/fileinput.min.css"))
    @BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/fileinput/5.0.3/js/fileinput.min.js"))
}

<div class="wrapper animated fadeInRight">
    <form id="form" class="form-horizontal m">
        <div class="form-group row">
            <label class="col-sm-2 control-label ">选择文件</label>
            <div class="col-sm-10">
                <input id="importFile" type="file">
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-2 control-label "> </label>
           @*  <div class="col-sm-10 control-label" style="text-align:left;">
                <a href='@Url.Content("~/template/导入摄像头信息模板.xlsx")' class="btn btn-secondary btn-xs"><i class="fa fa-file-excel-o"></i> 下载模板</a>
            </div> *@
        </div>
        <div class="form-group row">
            <label class="col-sm-2 control-label "></label>
            <div class="col-sm-10 text-danger">
                注意：<div>
                    1：请使用下载的模板进行编辑，不要改变模板结构，否则将导入不成功。<br />
                    2：学校名称必须和平台中的一致，否则将导入不成功。<br />
                    3：摄像头名称、编码必须唯一，否则将导入不成功。<br />
                    4：以上内容除【备注】外都是必填项。<br />
                    5：如实施的实验室不在此表中，请联系学校管理员在实验教学管理平台添加实验室信息。 <br />
                    6：摄像机名称对应海康综合安防平台的监控点名称、摄像机编号对应监控点编号。
                </div><br />
                提示：仅允许导入“xls”或“xlsx”格式文件！
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-2 control-label "></label>
            <div class="col-sm-10 text-danger" id="divErrorMsg"> 
            </div>
        </div>
    </form>
</div>

<script type="text/javascript">
    var filePath = undefined;
    $(document).ready(function () {
        $("#importFile").fileinput({
            language: 'zh',
            'uploadUrl': '@Url.Content("~/File/UploadExcel")' + '?fileModule=@UploadFileType.Import.ParseToInt()',
            showPreview: false,
            allowedFileExtensions: ['xls', 'xlsx']
        }).on("fileuploaded", function (event, data) {
            var obj = data.response;
            if (obj.Tag == 1) {
                filePath = obj.Data;
            }
            else {
                filePath = '';
            }
            $("#divErrorMsg").html("");
        });
    });

    function saveForm(index) {
        if (!filePath) {
            ys.alertError('文件未上传或者上传失败');
            return;
        }

        var postData =$("#form").getWebControls();
        postData.FilePath = filePath;
        ys.ajax({
            url: '@Url.Content("~/CameraManage/SchoolCamera/ImportJson")',
            type: "post",
            data: postData,
            success: function (obj) {
                if (obj.Tag == 1) {
                    ys.msgSuccess('导入成功');
                    parent.$('#gridTable').ysTable('refresh');parent.resetToolbarStatus();
                    parent.layer.close(index);
                }
                else {
                    //ys.msgError(obj.Message);
                    $("#divErrorMsg").html(obj.Message);
                }
            }
        });
    }
</script>