﻿@{ Layout = "~/Views/Shared/_FormWhite.cshtml"; }


<div class="container-div">
    <div class="wrapper animated fadeInRight">
        <form id="form" class="form-horizontal m">
            <div class="form-group row">
                <label class="col-sm-2 control-label ">存放地<font class="red"> *</font></label>
                <div class="col-sm-6">
                    <div id="storagePlace" col="StoragePlace"></div>
                </div>
                <div class="col-sm-2">
                    <span class="glyphicon glyphicon-question-sign titleprompt inputprompt" aria-hidden="true" data-container="body" data-toggle="tooltip" data-placement="bottom"
                          data-content="1、如缺少场所和橱柜名称，请到实验（专用）室管理栏中添加添加；<br />
                                    2、如不存放到橱柜，则只要选择场所名称；"></span>
                </div>
            </div>
            <div class="form-group row" style="display:none;" id="divFloor">
                <label class="col-sm-2 control-label ">层次</label>
                <div class="col-sm-6">
                    <input id="floor" col="Floor" class="form-control" placeholder="层次" />
                </div>
                <div class="col-sm-2">
                    <span class="glyphicon glyphicon-question-sign titleprompt inputprompt" aria-hidden="true" data-container="body" data-toggle="tooltip" data-placement="bottom"
                          data-content="层次是指橱柜中位置，如：上3、下2等。"></span>
                </div>
            </div>
        </form>
    </div>
</div>

<script type="text/javascript">
    //var ids = ys.request("ids");
    var ids = parent.SELECTED_IDS;
    var IsVerify = ys.request("isVerify");

    $(function () {
        $(".inputprompt").popover({
            trigger: 'hover',
            html: true
        });
        if (IsVerify == null) {
            IsVerify = 0;
        }
        loadStoragePlace();

    });

    function loadStoragePlace() {
        $('#storagePlace').ysComboBoxTree({
            url: '@Url.Content("~/BusinessManage/Cupboard/GetCupboardTreeListJson")',
            class: "form-control",
            key: 'id',
            value: 'name',
            callback: {
                customOnSuccess: function (param, treeId, data) {
                    if (data.Data.length == 0) {
                        ComBox.LoadPageMessage('存放地', '/BusinessManage/FunRoom/Index', '实验（专用）室列表', 0);
                    }
                },
                customOnClick: function (event, treeId, treeNode) {
                    var storagePlace = $('#storagePlace').ysComboBoxTree('getValue');
                    if (storagePlace.indexOf(',') != -1) {
                        $('#divFloor').show();
                    } else {
                        $('#divFloor').hide();
                        $('#floor').val('');
                    }
                }
            }
        });
    }

    function saveForm(index) {
        if ($("#form").validate().form()) {
            var postData = $("#form").getWebControls({ Ids: ids, IsVerify: IsVerify });
            if (postData.StoragePlace == '' || postData.StoragePlace == undefined) {
                ys.msgError('请选择存放地。');
                return false;
            }
            ys.ajax({
                url: '@Url.Content("~/InstrumentManage/SchoolInstrument/BatchInputRoomJson")',
                type: "post",
                data: postData,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        parent.$('#gridTable').ysTable('refresh');
                        parent.resetToolbarStatus();
                        parent.layer.close(index);
                        if (IsVerify == 1) {
                            parent.showSection();
                        }
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        }
    }

</script>
