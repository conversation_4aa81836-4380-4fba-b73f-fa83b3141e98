﻿@using Dqy.Syjx.Model.Result.OrganizationManage;

@{
    Layout = "~/Views/Shared/_FormWhite.cshtml";

    //List<GradeModel> listGrade = ViewBag.ListGrade as List<GradeModel>;
}

<div class="wrapper animated fadeInRight">
    <form id="form" class="form-horizontal m">
      @*  @foreach (GradeModel grade in listGrade)
        {
            <div class="form-group row">
                <div class="col-sm-2">
                    <label class="check-box"><div class="icheckbox-blue"><input name="grade_checkbox" type="checkbox" value="@grade.GradeId" style="position: absolute; opacity: 0;"><ins class="iCheck-helper" style="position: absolute; top: 0%; left: 0%; display: block; width: 100%; height: 100%; margin: 0px; padding: 0px; background: rgb(255, 255, 255); border: 0px none; opacity: 0;"></ins></div>@grade.GradeName</label>
                </div>
            </div>
             <div class="form-group row">
                    <div class="col-sm-10">
                    @foreach (WeekModel week in grade.WeekModels)
                    {
                        <label class="check-box"><div class="icheckbox-blue"><input name="grade_checkbox" type="checkbox" value="@week.WeekId" style="position: absolute; opacity: 0;"><ins class="iCheck-helper" style="position: absolute; top: 0%; left: 0%; display: block; width: 100%; height: 100%; margin: 0px; padding: 0px; background: rgb(255, 255, 255); border: 0px none; opacity: 0;"></ins></div>@week.WeekName</label>
                    }
                </div>
            </div>
        }*@
    </form>
</div>

<script type="text/javascript">
    var id = ys.request("gradeId");
    var weekId = ys.request("weekId");

    $(function () {
        getForm();
    });


    function getForm() {
        if (id > 0) {
            var url = '@Url.Content("~/OrganizationManage/CourseSection/GetGradeWeekList")' + '?gradeId=' + id + '&weekId=' + weekId;
            ys.ajax({
                url: url,
                type: "get",
                success: function (obj) {
                    if (obj.Tag == 1 && obj.Total > 0) {
                        var result = obj.Data;
                        //console.log("result:" + JSON.stringify(result));
                        var html = '';
                        $(result).each(function(i,v){
                            var gradeId = v.GradeId;
                            var gradeName = v.GradeName;
                            var weeks =v.WeekModels;

                            html += `<div class="form-group row"><label class="col-sm-2 control-label "></label><div class="col-sm-2">`;
                            html += `<label class="check-box"><div class="icheckbox-blue"><input name="ck_${gradeId}" type="checkbox" value="${gradeId}" style="position: absolute; opacity: 0;"></div><b style="font-size:15px;">${gradeName}</b></label>`;
                            html += `</div></div>`;

                            html += `<div class="form-group row"><label class="col-sm-2 control-label "></label><div class="col-sm-10">`;
                            $(weeks).each(function (k, obj) {
                                var weekId = obj.WeekId;
                                var weekName = obj.WeekName;

                                html += `<label class="check-box"><div class="icheckbox-blue"><input name="chk_${gradeId}" type="checkbox" value="${weekId}" gradeId="${gradeId}" style="position: absolute; opacity: 0;"></div>${weekName}</label>`;

                            });       
                            html += `</div></div>`;
                            html += `<hr/>`;
                        });

                        $("#form").html(html);


                        $('input[name^="chk_"]').change(function () {
                            if ($(this).prop("checked")) {
                                $(this).parent(".icheckbox-blue").addClass("checked");

                            } else {
                                $(this).parent(".icheckbox-blue").removeClass("checked");
                            }
                        });

                        $('input[name^="ck_"]').change(function () {
                            var checkValue = $(this).val();
                  
                            if ($(this).prop("checked")) {
                                $(this).parent(".icheckbox-blue").addClass("checked");
                                $('input[name="chk_' + checkValue + '"]').prop("checked", true);
                                $('input[name="chk_' + checkValue + '"]').parent(".icheckbox-blue").addClass("checked");

                            } else {
                                $(this).parent(".icheckbox-blue").removeClass("checked");
                                $('input[name="chk_' + checkValue + '"]').prop("checked", false);
                                $('input[name="chk_' + checkValue + '"]').parent(".icheckbox-blue").removeClass("checked");
                            }
                        });
                    }
                }
            });
        }
    }

    function saveForm(index) {

        var arrGradeId = $('input[name^="chk_"]:checked').map(function () { return $(this).attr("gradeId") }).get();
        var arrWeekId = $('input[name^="chk_"]:checked').map(function () { return this.value }).get();

        var postData = {
            listGradeId: arrGradeId,
            listWeekId: arrWeekId,
            gradeId: id,
            weekId: weekId
        };

        ys.ajax({
            url: '@Url.Content("~/OrganizationManage/CourseSection/SaveBatchCopyForm")',
            type: "post",
            data: postData,
            success: function (obj) {
                if (obj.Tag == 1) {
                    ys.msgSuccess(obj.Message);
                    parent.$('#gridTable').ysTable('refresh'); parent.resetToolbarStatus();
                    parent.layer.close(index);
                }
                else {
                    ys.msgError(obj.Message);
                }
            }
        });
    }
</script>
