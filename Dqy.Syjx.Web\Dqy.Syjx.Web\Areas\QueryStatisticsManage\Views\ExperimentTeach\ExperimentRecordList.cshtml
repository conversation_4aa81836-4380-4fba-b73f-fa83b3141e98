﻿@{
    Layout = "~/Views/Shared/_Index.cshtml";
    int UnitType = (int)ViewBag.UnitType;
}
<style type="text/css">
    .select2-container {
        width: 100% !important;
    }

    .select-list li input {
        width: 150px !important;
    }
</style>
<div class="container-div">
    <div class="row">
        <div id="searchDiv" class="col-sm-12 search-collapse">
            <div class="select-list">
                <ul>
                    <li>
                        <a class="btn btn-secondary btn-sm" onclick="addDetailTabForm()"><i class="fa fa-street-view"></i>&nbsp;按实验汇总查看</a>
                    </li>
                    <li>
                        <span id="schoolYearStart" col="SchoolYearStart" style="display: inline-block; width: 80px;"></span>
                    </li>
                    <li>
                        <span id="schoolTerm" col="SchoolTerm" style="display: inline-block; width: 80px;"></span>
                    </li>
                    <li id="liCountyId" style="display:none;">
                        <span id="countyId" col="CountyId" style="display:inline-block;width:120px;"></span>
                    </li>
                    <li>
                        <span id="schoolStageId" col="SchoolStageId" style="display:inline-block; width:100px;"></span>
                    </li>
                    <li id="liSchoolId" style="display:none;">
                        <span id="schoolId" col="SchoolId" style="display:inline-block;width:150px;"></span>
                    </li>
                   
                    <li>
                        <span id="GradeId" col="GradeId" style="display: inline-block; width: 110px;"></span>
                        <input type="hidden" id="schoolGradeClassId" col="SchoolGradeClassId" value="0" />
                    </li>
                    <li>
                        <span id="courseId" col="CourseId" style="display: inline-block; width: 100px;"></span>
                    </li>
                    <li>
                        <span id="sourceType" col="SourceType" style="display: inline-block; width: 100px;"></span>
                    </li>
                    <li>
                        <span id="experimentType" col="ExperimentType" style="display: inline-block; width: 100px;"></span>
                    </li>
                    <li>
                        <span id="isNeedDo" col="IsNeedDo" style="display: inline-block; width: 100px;"></span>
                    </li>
                    <li>
                        <input id="RecordUserName" col="RecordUserName" placeholder="登记人" style="width:100px;" />
                    </li>
                    <li>
                        <input id="experimentName" col="ExperimentName" placeholder="实验名称" style="width:150px;" />
                    </li>
                    <li>
                        <a id="btnSearch" class="btn btn-primary btn-sm" onclick="searchGrid()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                    </li>
                    <li>
                        <a class="btn btn-secondary btn-sm" onclick="resetGrid()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                    </li>
                    <!--帮助文档需要内容,id值必须为“helpBtn”-->
                    <li>
                        <i class="fa fa-question-circle" id="helpBtn"></i>
                    </li>
                </ul>
            </div>
        </div>
        <div class="btn-group-sm hidden-xs" id="toolbar">
            <a id="btnExport" class="btn btn-warning btn-sm" onclick="exportForm()"><i class="fa fa-download"></i> 导出</a>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="gridTable" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>
<!--帮助文档需要内容,id值必须为“tipMsg”-->
<div id="tipMsg"></div>
<script type="text/javascript">
    var SortName = 'Id asc';
    var CountyId = ys.request("CountyId");
    var SchoolId = ys.request("SchoolId");
    var SchoolYearStart = ys.request("SchoolYearStart");
    var SchoolTerm = ys.request("SchoolTerm");
    var CourseId = ys.request("CourseId");
    var SchoolGradeClassId = ys.request("SchoolGradeClassId");
    var BasePageCode = 101022;
    $(function () {

         if (@UnitType == @UnitTypeEnum.City.ParseToInt()) {
             $('#liCountyId').show();
             $('#liSchoolId').show();
             loadCounty();
             loadSchool(0);
             BasePageCode = 103022;
         } else if (@UnitType == @UnitTypeEnum.County.ParseToInt()) {
            $('#liSchoolId').show();
             loadSchool(-1, 0);
             SortName = 'SchoolId asc ,Id asc';
             BasePageCode = 102022;
        }

        loadGrade(); //年级

        if (@UnitType == @UnitTypeEnum.City.ParseToInt() || @UnitType == @UnitTypeEnum.County.ParseToInt())
        {
            $('#liSchoolStageId').show();
        }

        loadSchoolTermYear();//学年
        loadSchoolTerm(); //学期
        loadCourse();//学科
        loadSourceType();//实验来源
        loadExperimentType();//实验类型
        $("#isNeedDo").ysComboBox({ data: ys.getJson(@Html.Raw(typeof(IsNeedEnum).EnumToDictionaryString())), defaultName: '实验要求' });//实验要求

        if (!(SchoolGradeClassId > 0)) {
            SchoolGradeClassId = 0;
        }
        $('#schoolGradeClassId').val(String(SchoolGradeClassId));
        setParamValue(CountyId, SchoolId, SchoolYearStart, SchoolTerm, CourseId);
        initGrid();
    });

    function addDetailTabForm() { //按实验汇总查看
        var url = '@Url.Content("~/QueryStatisticsManage/ExperimentTeach/ExperimentRecordStatistics")';
        createMenuItem(url, "实验汇总查看");
    }

    function setParamValue(CountyId, SchoolId, SchoolYearStart, SchoolTerm, CourseId) {
        $("#countyId").ysComboBox('setValue', CountyId);
        $("#schoolId").ysComboBox('setValue', SchoolId);
        $("#schoolYearStart").ysComboBox('setValue', SchoolYearStart);
        $('#schoolTerm').ysComboBox('setValue', SchoolTerm);
        $('#courseId').ysComboBox('setValue', CourseId);
    }
     //区县
    function loadCounty() {
         $("#countyId").ysComboBox({
            url: '@Url.Content("~/OrganizationManage/Unit/GetCountyBoxByCityIdJson")',
            key: "Id",
            value: "Name",
            defaultName: '区县名称',
            onChange: function () {
                var countyId = $("#countyId").ysComboBox("getValue");
                loadSchool(countyId);
            }
        });
    }

    //学校
    function loadSchool(countyId, schoolStageId) {
        if (parseInt(countyId) == -1) {
                $("#schoolId").ysComboBox({
                url: '@Url.Content("~/OrganizationManage/Unit/GetChildrenPageList")' + "?PageSize=10000&SchoolStageId=" + schoolStageId,
                    key: 'Id',
                    value: 'Name',
                    defaultName: '单位名称'
                });
                $(".select2-container").width("100%");
            } else if (parseInt(countyId) == 0) {
                $("#schoolId").ysComboBox({
                    data: [],
                    key: 'Id',
                    value: 'Name',
                    defaultName: '单位名称'
                });
                $(".select2-container").width("100%");
            } else if (parseInt(countyId) > 0) {
                $("#schoolId").ysComboBox({
                    url: '@Url.Content("~/OrganizationManage/Unit/GetUnitList?")' + 'Pid=' + countyId,
                    key: 'Id',
                    value: 'Name',
                    defaultName: '单位名称'
                });
                $(".select2-container").width("100%");
            }
    }

    //学年
    function loadSchoolTermYear() {
        ComBox.SchoolTermYear($('#schoolYearStart'), undefined, '学年');
    }

    //学期
    function loadSchoolTerm() {
        $("#schoolTerm").ysComboBox({ data: ys.getJson(@Html.Raw(typeof(SchoolTermEnum).EnumToDictionaryString())), defaultName: '学期' });
    }

    //实验来源
    function loadSourceType() {
        $("#sourceType").ysComboBox({ data: ys.getJson(@Html.Raw(typeof(SourceTypeEnum).EnumToDictionaryString())), defaultName: '实验来源' });
    }

    //实验类型
    function loadExperimentType() {
        $("#experimentType").ysComboBox({ data: ys.getJson(@Html.Raw(typeof(ExperimentTypeEnum).EnumToDictionaryString())), defaultName: '实验类型' });
    }
    
    //班级
    function loadGrade() {
        $('#GradeId').ysComboBox({
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetChildToListJson")' + '?TypeCode=@DicTypeCodeEnum.Grade.ParseToInt()',
            key: 'DictionaryId',
            value: 'DicName',
            defaultName: '年级'
        });
    }

    //学科
    function loadCourse() {
        $('#schoolStageId').ysComboBox({
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetStageByUserId")',
            key: 'DictionaryId',
            value: 'DicName',
            defaultName: '学段',
            onChange: function () {
                if (@UnitType == @UnitTypeEnum.County.ParseToInt()) {
                    var schoolStageId = $('#schoolStageId').ysComboBox("getValue");
                    if (schoolStageId > 0) {
                        loadSchool(-1, schoolStageId);
                    }
                }
            }
        });

        $('#courseId').ysComboBox({
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetCourseByUserId?nature=1")',
            defaultName: '适用学科',
            key: 'DictionaryId',
            value: 'DicName'
        });
    }

    function initGrid() {
        var queryUrl = '@Url.Content("~/QueryStatisticsManage/ExperimentTeach/GetExperimentBookingList")';
        $('#gridTable').ysTable({
            url: queryUrl,
            sortName: SortName,
            showExportSetBtn : true,
            showExportSetCode: BasePageCode,
            columns: [
                {
                    field: '', title: '操作', halign: 'center', align: 'center', width: 60 ,
                     formatter: function (value, row, index) {
                        var html = $.Format('<a class="btn btn-info btn-xs" href="#" onclick="look(this)" value="{0}"><i class="fa fa-eye"></i>查看</a> ', row.Id);
                        return html;
                    }
                },
                { field: 'CountyName', title: '区县名称', halign: 'center', align: 'left', visible: (@UnitType == @UnitTypeEnum.City.ParseToInt()), width: 80  },
                {
                    field: 'SchoolId', title: '单位名称', sortable: true, halign: 'center', align: 'left', visible: (@UnitType != @UnitTypeEnum.School.ParseToInt()), width: 150,
                    formatter: function (value, row, index) {
                        if (row.SchoolName) {
                            return row.SchoolName
                        }
                    }
                },
                {
                    field: 'SchoolTerm', title: '学期', sortable: true, halign: 'center', align: 'center', width: 100 ,
                    formatter: function (value, row, index) {
                        return row.SchoolTermName;
                    }
                },
                /*{ field: 'GradeName', title: '年级', sortable: true, halign: 'center', align: 'center', width: 60 },*/
                {
                    field: 'SchoolGradeClassId', title: '上课班级', sortable: true, halign: 'center', align: 'center', width: 120,
                    formatter: function (value, row, index) {
                        if (row.ClassName) {
                            return row.ClassName
                        }
                    }
                },
                {
                    field: 'CourseId', title: '学科', sortable: true, halign: 'center', align: 'center', width: 50,
                    formatter: function (value, row, index) {
                        if (row.CourseName) {
                            return row.CourseName
                        }
                    }
                },
                {
                    field: 'SourceType', title: '实验来源', sortable: true, halign: 'center', align: 'center', width: 50 ,
                    formatter: function (value, row, index) {
                        return value == @SourceTypeEnum.ExperimentPlan.ParseToInt()
                            ? '@SourceTypeEnum.ExperimentPlan.GetDescription()'
                            : '@SourceTypeEnum.ExperimentList.GetDescription()';
                    }
                },
                { field: 'ExperimentName', title: '实验名称', sortable: true, halign: 'center', align: 'left', width: 140 },
                {
                    field: 'ExperimentType', title: '实验类型', sortable: true, halign: 'center', align: 'center', width: 50 ,
                    formatter: function (value, row, index) {
                        return value == @ExperimentTypeEnum.Demo.ParseToInt()
                            ? '@ExperimentTypeEnum.Demo.GetDescription()'
                            : '@ExperimentTypeEnum.Group.GetDescription()';
                    }
                },
                { field: 'Groupz', title: '分组数', sortable: true, halign: 'center', align: 'center', width: 50 },
                { field: 'IsNeedDoName', title: '实验要求', sortable: true, halign: 'center', align: 'center', width: 50 },
                {
                    field: 'ClassTime', title: '上课时间', sortable: true, halign: 'center', align: 'center', width: 120,
                    formatter: function (value, row, index) {
                        var html = '';
                        if (row.Id) {
                            html = '--';
                            if (parseInt(row.SectionIndex) > 0) {
                                html = $.Format("{0}(第{1}节)", ys.formatDate(row.ClassTime, "yyyy-MM-dd"), row.SectionIndex);
                            }
                        }
                        return html;
                    }
                },
                { field: 'FunRoomName', title: '上课地点', sortable: true, halign: 'center', align: 'left' ,width: 140  },
                {
                    field: 'RecordUserName', title: '登记人', sortable: true, halign: 'center', align: 'center', width: 90,
                    formatter: function (value, row, index) {
                        var html = '';
                        if (row.Id) {
                            if (row.RecordMode == @ExperimentRecordModeEnum.Easy.ParseToInt()) {
                                html = Syjx.GetRecordEasyHtml();
                            }
                            if (value && value.length > 0) {
                                html += value;
                            } else {
                                html = '--';
                            }
                        }
                        return html;
                    }
                }
            ],
            queryParams: function (params) {
                var pagination = $('#gridTable').ysTable('getPagination', params);
                var queryString = $('#searchDiv').getWebControls(pagination);
                return queryString;
            }
        });
    }

    function searchGrid() {
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }

    function resetGrid() {
        //清空条件
        $('#schoolYearStart').ysComboBox('setValue', SchoolYearStart);
        $('#schoolTerm').ysComboBox('setValue', SchoolTerm);
        $('#schoolGradeClassId').ysComboBox('setValue', String(SchoolGradeClassId));
        $("#GradeId").ysComboBox('setValue', -1);
        $('#courseId').ysComboBox('setValue', CourseId);
        $("#schoolStageId").ysComboBox('setValue', -1);
        $('#sourceType').ysComboBox('setValue', -1);
        $('#experimentType').ysComboBox('setValue', -1);
        $('#RecordUserName').val('');
        $('#isNeedDo').ysComboBox('setValue', -1);
        $('#experimentName').val('');
        if (@UnitType == @UnitTypeEnum.City.ParseToInt()) {
            $('#countyId').ysComboBox('setValue', CountyId);
            $('#schoolId').ysComboBox('setValue', SchoolId);
            $('#schoolGradeClassId').ysComboBox('setValue', String(SchoolGradeClassId));
        }
        if (@UnitType == @UnitTypeEnum.County.ParseToInt()) {
            $('#schoolId').ysComboBox('setValue', SchoolId);
            loadSchool(-1, 0);
        }
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }

    function look(obj) {
        var id = $(obj).attr('value');
        var url = '@Url.Content("~/ExperimentTeachManage/ExperimentBooking/Detail")' + '?id=' + id;
        createMenuItem(url, "实验查看");
    }

    function exportForm() { //导出
        var url = '/QueryStatisticsManage/ExperimentTeach/ExportExperimentRecord';
        var pagination = $('#gridTable').ysTable('getPagination', { "sort": "Id", "order": "asc", "offset": 0, "limit": 10 });
        var postData = $("#searchDiv").getWebControls(pagination);
        postData.BasePageCode = BasePageCode;
        ys.exportExcel(url, postData);
    }
</script>