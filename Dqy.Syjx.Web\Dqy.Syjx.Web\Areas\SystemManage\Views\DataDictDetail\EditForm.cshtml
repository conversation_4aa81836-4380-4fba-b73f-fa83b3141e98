﻿@{
    Layout = "~/Views/Shared/_FormWhite.cshtml";
}

<div class="wrapper animated fadeInRight">
    <form id="form" class="form-horizontal m">
        <div class="form-group row">
            <label class="col-sm-3 control-label " id="labName">名称<font class="red"> *</font></label>
            <div class="col-sm-8">
                <input id="dictValue" col="DictValue" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">排序<font class="red"> *</font></label>
            <div class="col-sm-8">
                <input id="dictSort" col="DictSort" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">状态<font class="red"> *</font></label>
            <div id="dictStatus" col="DictStatus" class="col-sm-8"></div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">备注</label>
            <div class="col-sm-8">
                <textarea id="Remark" style="max-width:100%" col="Remark" type="text" class="form-control"></textarea>
            </div>
        </div>
    </form>
</div>

<script type="text/javascript">
    var id = ys.request("id");
    var typeCode = ys.request("t");
    var TitleName = ys.request("n");
    $(function () {
        $("#labName").html(TitleName + '名称<font class="red"> *</font>');
        $("#dictStatus").ysRadioBox({ data: ys.getJson(@Html.Raw(typeof(StatusEnum).EnumToDictionaryString())), default: '@StatusEnum.Yes.ParseToInt()' });
        // $("#listClass").ysComboBox({
        //     data: [{ Key: 'default', Value: '默认' }, { Key: 'primary', Value: '主要' }, { Key: 'success', Value: '成功' }, { Key: 'info', Value: '信息' }, { Key: 'warning', Value: '警告' }, { Key: 'danger', Value: '危险' }],
        //     class: "form-control"
        // });

        getForm();

        $("#form").validate({
            rules: {
                dictKey: { required: true, digits: true },
                dictValue: { required: true }
            }
        });
    });

    function getForm() {
        if (id > 0) {
            ys.ajax({
                url: '@Url.Content("~/SystemManage/DataDictDetail/GetFormJson")' + '?id=' + id,
                type: "get",
                success: function (obj) {
                    if (obj.Tag == 1) {
                        if (!(obj.Data.DictSort && obj.Data.DictSort > 0)) {
                            obj.Data.DictSort = 0;
                        }
                        $("#form").setWebControls(obj.Data);                        
                    }
                }
            });
        }
        else {
            ys.ajax({
                url: '@Url.Content("~/SystemManage/DataDictDetail/GetMaxSortJson")',
                type: "get",
                success: function (obj) {
                    if (obj.Tag == 1) {
                         var defaultData = {};
                        defaultData.DictSort = obj.Data;
                        // defaultData.DictType = dictType;
                        $("#form").setWebControls(defaultData);
                    }
                }
            });
        }
    }

    function saveForm(index) {
        if ($("#form").validate().form()) {
            var postData = $("#form").getWebControls({ Id: id });
            //验证必填项。
            var errMsg = '';
            if (!Number(postData.DictSort)) {
                errMsg += '请填写排序值。</br>';
            }
            if (postData.Remark != undefined && postData.Remark.length > 50) {
                errMsg += '填写的备注信息请控制在50字符内.</br>';
            }
             
            if (errMsg != '') {
                layer.msg(errMsg, { icon: 2, time: 3000, btn: ['关闭'], yes: function () { layer.closeAll(); }, area: ['400px'] });
                return false;
            }

            postData.TypeCode=typeCode;
            ys.ajax({
                url: '@Url.Content("~/SystemManage/DataDictDetail/SaveCustomFormJson")',
                type: "post",
                data: postData,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        if (top.initDataDict) {
                            top.initDataDict();
                        }
                        ys.msgSuccess(obj.Message);
                        parent.$('#gridTable').ysTable('refresh'); parent.resetToolbarStatus();
                        parent.layer.close(index);
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        }
    }
</script>

