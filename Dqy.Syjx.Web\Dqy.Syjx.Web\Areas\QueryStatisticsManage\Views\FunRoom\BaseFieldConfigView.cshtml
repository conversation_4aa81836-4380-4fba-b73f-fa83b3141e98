﻿@{
    Layout = "~/Views/Shared/_Index.cshtml";
}
@inject Microsoft.AspNetCore.Hosting.IWebHostEnvironment HostingEnvironment
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/laydate/5.0.9/laydate.min.js"))
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/yisha/css/style.min.css"))
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/yisha/js/yisha.min.js"))

@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/jquery.validation/1.14.0/jquery.validate.min.js"))

@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/icheck/1.0.2/skins/custom.min.css"))
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/icheck/1.0.2/icheck.min.js"))
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/uploadifive/File.min.css"))
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/uploadifive/File.min.js"))

<div class="container-div">
    <div class="btn-group-sm hidden-xs" id="toolbar">
    </div>
    <div class="col-sm-12 select-table table-striped">
        <div class="ibox-title">
            <h5>制度与队伍建设</h5>
        </div>

        <div class="card-body">
            <form id="form" class="form-horizontal m">
                <input id="Id" col="Id" type="hidden" value="0" />
                <div class="form-group row">
                    <label class="col-sm-2 control-label ">校长：</label>
                    <div class="col-sm-10">
                        <input id="Headmaster" col="Headmaster" type="text" class="form-control" readonly />
                    </div>
                </div>
                <div class="form-group row">
                    <label class="col-sm-2 control-label ">分管校长：</label>
                    <div class="col-sm-10">
                        <input id="BranchHeadmaster" col="BranchHeadmaster" type="text" class="form-control" readonly/>
                    </div>
                </div>
                <div class="form-group row">
                    <label class="col-sm-2 control-label ">分管部门：</label>
                    <div class="col-sm-10">
                        <input id="BranchDepartment" col="BranchDepartment" type="text" class="form-control" readonly/>
                    </div>
                </div>
                <div class="form-group row">
                    <label class="col-sm-2 control-label ">部门负责人：</label>
                    <div class="col-sm-10">
                        <input id="DepartmentHead" col="DepartmentHead" type="text" class="form-control" readonly/>
                    </div>
                </div>


                <div class="form-group row">
                    <label class="col-sm-2 control-label ">安全管理制度</label>
                    <div class="col-sm-10">
                        <div style="float:left;">
                            <div style="height: 55px;display:none;" id="fileQueueManager"></div>
                        </div>
                        &nbsp;&nbsp;
                        <div id="spanFile_1300" style="float:left;padding-left:10px;">
                        </div>
                    </div>
                </div>


                <div class="form-group row">
                    <label class="col-sm-2 control-label ">安全考核制度</label>
                    <div class="col-sm-10">
                        <div style="float:left;">
                            <div style="height: 55px;display:none;" id="fileQueueExamine"></div>
                        </div>
                        &nbsp;&nbsp;
                        <div id="spanFile_1301" style="float: left; padding-left: 10px;">
                        </div>
                    </div>
                </div>

                <div class="form-group row">
                    <label class="col-sm-2 control-label ">事故应急预案</label>
                    <div class="col-sm-10">
                        <div style="float:left;">
                            <div style="height: 55px;display:none;" id="fileQueuePlan"></div>
                        </div>
                        &nbsp;&nbsp;
                        <div id="spanFile_1302" style="float: left; padding-left: 10px;">
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
<script type="text/javascript">
    var id = ys.request("id");
    $(function () {
        //console.log("id:" + id);
        getForm();
    });

     function getForm() {
        if (id > 0) {
            ys.ajax({
                url: '@Url.Content("~/QueryStatisticsManage/FunRoom/GetFormJsonByUnitId")' + '?id=' + id,
                type: 'get',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        $('#form').setWebControls(obj.Data);

                        //调用显示附件信息
                        var fileList = obj.Data.Atts;
                        if (fileList.length > 0) {
                            for (var i = 0; i < fileList.length; i++) {
                                var id = fileList[i].Id;
                                var filePath = fileList[i].Path;
                                var fileExt = fileList[i].Ext.toUpperCase();
                                var fileTitle = fileList[i].Title;
                                var fileFileCategory = fileList[i].FileCategory;
                                var objDiv = $("#spanFile_" + fileFileCategory);
                                if (fileExt == ".JPG" || fileExt == ".BMP" || fileExt == ".JPEG" || fileExt == ".PNG" || fileExt == ".GIF") {
                                    objDiv.append('<span class="keywords" idValue="' + id + '"> <a  modal="zoomImg"  href="javascript:void(0);" src="' + filePath + '" >' + fileTitle + '</a></span>');
                                } else {
                                    objDiv.append('<span class="keywords" idValue="' + id + '"> <a target="_blank" href="' + filePath + '" >' + fileTitle + '</a></span>');
                                }
                            }
                            imgages.showextalink();
                        }
                    }
                }
            });
        }
    }
</script>
