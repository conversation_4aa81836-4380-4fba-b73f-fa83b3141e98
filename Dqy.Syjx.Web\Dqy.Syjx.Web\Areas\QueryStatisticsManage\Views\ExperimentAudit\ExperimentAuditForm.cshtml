﻿@{
    Layout = "~/Views/Shared/_FormWhite.cshtml";
}
<style type="text/css">
    /*   .check-box {
            width: 100px;
        }*/

    .iradio-box {
        width: 100px;
    }

    .iradio-box {
        position: initial;
        display: inline-block;
    }

    .iradio-blue {
        position: inherit;
        display: inline-block;
        top: 6px;
    }
</style>

<div class="wrapper animated fadeInRight">
    <div class="btn-group-sm hidden-xs" id="toolbar">
    </div>
    <div id="divHead" style="display:none;" class="col-sm-12 select-table table-striped">

        <div class="ibox-title">
            <h5>
                审核人信息
            </h5>
        </div>
        <div class="card-body">
            <form class="form-horizontal m">
                <div class="divform divSingle">
                    <div class="form-group row">
                        <label class="col-sm-3 control-label ">审核人</label>
                        <div class="col-sm-7">
                            <input id="userName" col="UserName" type="text" class="form-control" readonly />
                        </div>
                    </div>

                    <div class="form-group row" style="margin-top:10px;">
                        <label class="col-sm-3 control-label ">审核人账号</label>
                        <div class="col-sm-7">
                            <input id="userAccount" col="UserAccount" type="text" class="form-control" readonly />
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <div class="col-sm-12 select-table table-striped">

        <div class="ibox-title">
            <h5>
                达标审核
            </h5>
        </div>
        <div class="card-body">
            <form id="form" class="form-horizontal m">
               
                <div class="divform divSingle">
                   
                    <div class="form-group row">
                        <label class="col-sm-3 control-label ">实验室达标情况<font class="red"> *</font></label>
                        <div class="col-sm-7">
                            <div id="funRoom" col="FunRoom"></div>
                        </div>
                    </div>

                    <div class="form-group row">
                        <label class="col-sm-3 control-label ">实验仪器达标情况<font class="red"> *</font></label>
                        <div class="col-sm-7">
                            <div id="instrument" col="Instrument"></div>
                        </div>
                    </div>

                    <div class="form-group row">
                        <label class="col-sm-3 control-label ">实验开出达标情况<font class="red"> *</font></label>
                        <div class="col-sm-7">
                            <div id="openRate" col="OpenRate"></div>
                        </div>
                    </div>

                    <div class="form-group row">
                        <label class="col-sm-3 control-label ">审核意见</label>
                        <div class="col-sm-7">
                            <textarea id="auditRemark" style="max-width:100%" col="AuditRemark" class="form-control"></textarea>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<script type="text/javascript">
    var id = ys.request("id");
    var isView = ys.request("isView");
    $(function () {

        if (isView == 0){
            $("#divHead").hide();
        }else{
            $("#divHead").show();
        }

        $("#funRoom").ysRadioBox({ data: ys.getJson(@Html.Raw(typeof(SchoolStandardEnum).EnumToDictionaryString())) });
        $("#instrument").ysRadioBox({ data: ys.getJson(@Html.Raw(typeof(SchoolStandardEnum).EnumToDictionaryString())) });
        $("#openRate").ysRadioBox({ data: ys.getJson(@Html.Raw(typeof(SchoolStandardEnum).EnumToDictionaryString())) });

        getForm();

        $('#form').validate({
            rules: {
                schoolId: { required: true }
            }
        });
    });

    function getForm() {
        if (id > 0) {
            ys.ajax({
                url: '@Url.Content("~/QueryStatisticsManage/ExperimentAudit/GetFormJson")' + '?id=' + id,
                type: 'get',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        $('#form').setWebControls(obj.Data);

                        $("#userName").val(obj.Data.UserName);
                        $("#userAccount").val(obj.Data.UserAccount);
                    }
                }
            });
        }
        else {
            var defaultData = {};
            $('#form').setWebControls(defaultData);
        }
    }

    function saveForm(index) {
        if ($('#form').validate().form()) {
            var postData = $('#form').getWebControls({ Id: id });
            if (postData.FunRoom == ""){
                ys.msgError("请选择“实验室达标情况”");
                return;
            }
            if (postData.Instrument == "") {
                ys.msgError("请选择“实验仪器达标情况”");
                return;
            }
            if (postData.OpenRate == "") {
                ys.msgError("请选择“实验开出达标情况”");
                return;
            }
            ys.ajax({
                url: '@Url.Content("~/QueryStatisticsManage/ExperimentAudit/SaveFormJson")',
                type: 'post',
                data: postData,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        parent.searchGrid();
                        parent.layer.close(index);
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        }
    }
</script>

