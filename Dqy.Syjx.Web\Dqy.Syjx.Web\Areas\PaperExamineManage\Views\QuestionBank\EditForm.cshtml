﻿@{
    Layout = "~/Views/Shared/_Index.cshtml";
    //Layout = "~/Views/Shared/_FormWhite.cshtml";
}
@inject Microsoft.AspNetCore.Hosting.IWebHostEnvironment HostingEnvironment
@Dqy.Syjx.Util.BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/uploadifive/File.min.css"))
@Dqy.Syjx.Util.BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/uploadifive/File.min.js"))
<style type="text/css">
    .tableoption thead tr td {
        background-color: #e8e8e8;
        padding: 3px 5px;
        font-size: 16px;
    }

    .tableoption tr {
        height: 46px;
        text-align: left;
        display: table-row;
        vertical-align: inherit;
        border-color: inherit;
        border-collapse: collapse;
        border-spacing: 0;
    }

    .choicetxt {
        height: 24px;
        padding: 2px 6px;
        font-size: 14px;
    }

    .option-icon {
        height: 30px;
        cursor: pointer;
    }
    .preview-namedesc{color:gray;padding-left:25px;}
    .preview-picture{max-width:400px;max-height:400px;display:flex;padding-left: 25px;}
    .preview-picture img{width: auto;height: auto;max-width: 100%;max-height: 100%;}
    .keywords {
        background: none;
        height: auto;
        margin-bottom: 0px;
        margin-right: 0px;
    }
</style>
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 select-table table-striped">
            <div class="wrapper animated fadeInRight">
                <form id="form" class="form-horizontal m">
                    <div class="form-group row">
                        <label class="col-sm-2 control-label ">题目编号：<font class="red"> *</font></label>
                        <div class="col-sm-2">
                            <input id="Code" col="Code" type="text" class="form-control" readonly placeholder="系统自动生成" />
                        </div>
                        <label class="col-sm-2 control-label " style="color:#999;text-align:left;">系统自动生成</label>
                        <label class="col-sm-2 control-label ">修改时间：<font class="red"> *</font></label>
                        <div class="col-sm-2">
                            <input id="BaseModifyTime" col="BaseModifyTime" type="text" class="form-control" readonly placeholder="系统默认最新修改时间" />
                        </div>
                        <label class="col-sm-2 control-label " style="color: #999; text-align: left;">系统默认最新修改时间</label>
                    </div>
                    <div class="form-group row">
                        <label class="col-sm-2 control-label ">题目性质：<font class="red"> *</font></label>
                        <div class="col-sm-10">
                            <div id="Nature" col="Nature" style="display: inline-block;"></div>
                            <span style="color:red;">“特殊”是指特定专用类，非通用类；如：一次培训完成后就结束！</span>
                        </div>
                    </div>
                    <div class="form-group row">
                        <label class="col-sm-2 control-label ">题型：<font class="red"> *</font></label>
                        <div class="col-sm-4">
                            <div id="QuestionType" col="QuestionType"></div>
                            <input type="hidden" id="hidQuestionTypeNature" value="0" />
                        </div>
                        <label class="col-sm-2 control-label ">题目分类：<font class="red"> *</font></label>
                        <div class="col-sm-4">
                            <div id="Classify" col="Classify"></div>
                        </div>
                    </div>
                    <div class="form-group row">
                        <label class="col-sm-2 control-label ">适用学科：<font class="red"> *</font></label>
                        <div class="col-sm-4">
                            <div id="CourseId" col="CourseId"></div>
                        </div>
                        <label class="col-sm-2 control-label ">适用学段：<font class="red"> *</font></label>
                        <div class="col-sm-4">
                            <div id="SchoolStage" col="SchoolStage"></div>
                        </div>
                    </div>
                    <div class="form-group row">
                        <label class="col-sm-2 control-label ">适用年级：<font class="red"> *</font></label>
                        <div class="col-sm-4">
                            <div id="GradeId" col="GradeId"></div>
                        </div>
                        <label class="col-sm-2 control-label ">适用对象：<font class="red"> *</font></label>
                        <div class="col-sm-4">
                            <div id="UseObj" col="UseObj"></div>
                        </div>
                    </div>
                    <div class="form-group row">
                        <label class="col-sm-2 control-label ">题目标签：<font class="red"> *</font></label>
                        <div class="col-sm-4">
                            <input id="Tag" col="Tag" type="text" class="form-control" />
                        </div>
                        <div class="col-sm-6" style="margin-top: 10px;color:#999;">
                            用于组卷时快速查找题目，如：XX培训，控制在6个文字以内
                        </div>
                    </div>
                    <div class="form-group row">
                        <label class="col-sm-2 control-label ">标题：<font class="red"> *</font></label>
                        <div class="col-sm-4">
                            <textarea id="Title" col="Title" class="form-control"></textarea>
                        </div>
                        <div class="col-sm-2">
                            <a id="btnAdd" class="btn btn-success" onclick="uploadQuestion();"><i class="fa fa-plus"></i> 上传图片</a>
                        </div>
                        <div class="col-sm-4" id="questionimgs"></div>
                    </div>
                    <div class="form-group row">
                        <label class="col-sm-2 control-label"><a id="btnAdd" class="btn btn-success" onclick="addOption();"><i class="fa fa-plus"></i> 新增选项</a></label>
                        <div class="col-sm-10">
                            <div class="selScrrol">
                                <table class="tableoption" cellspacing="0" cellpadding="0" width="100%" style="border-bottom:0px !important;">
                                    <thead>
                                        <tr>
                                            <td style="min-width: 300px;">选项文字</td>
                                            <td align="center" style="min-width: 60px; ">图片</td>
                                            <td style="min-width: 80px; "><div style="overflow: hidden; text-align: center;">说明</div></td>
                                            <td style="min-width: 60px; text-align: center; "><span>正确答案</span></td>
                                            <td align="left" style="padding: 3px 5px 3px 15px; min-width: 100px;"><span>上移下移</span></td>
                                            <td align="center" style="width: 50px; ">删除</td>
                                        </tr>
                                    </thead>
                                    <tbody id="optionBody">
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    <div class="footer" style="text-align:center;height:56px;">
                        <a class="btn btn-success" onclick="saveQuestionForm()"><i class="fa fa-subway"></i> 保存提交</a>
                    </div>
                </form>
                <div id="divUpload" style="display:none;">
                    <input type="file" name="uploadify" id="uploadify" />
                    <div style="height: 55px;display:none;" id="fileQueue"></div>
                    <input type="file" name="uploadify" id="optionUploadify" />
                    <div style="height: 55px;display:none;" id="fileOptionQueue"></div>
                </div>
            </div>
        </div>

        <div class="col-sm-12 search-collapse" id="toolbar"style="padding-bottom:100px;">
            <h1>预览效果</h1>
            <div style="line-height: 20px; letter-spacing: 2px; padding: 10px;">
                <span id="preTitle"  style="font-size:15px;text-wrap:normal;font-weight:bold;"></span><br />
                <div id="preTitlePicture">

                </div>
                <div id="preOptions" style="padding:10px 5px;">
                </div>

            </div>
        </div>
    </div>
</div>
@section footer{
    <script src='@Url.Content("~/junfei/js/CommonPaper.js")' type="text/javascript"></script>
    <script type="text/javascript">
        var id = ys.request("id");
        var QuestionTypeUrl = '@Url.Content("~/SystemManage/StaticDictionary/GetListJson")' + '?TypeCode=2002';
        var ClassifyUrl = '@Url.Content("~/SystemManage/StaticDictionary/GetListJson")' + '?TypeCode=2001';
        var UseObjUrl = '@Url.Content("~/SystemManage/StaticDictionary/GetListJson")' + '?TypeCode=2003';
        var CourseUrl = '@Url.Content("~/SystemManage/StaticDictionary/GetListJson")' + '?TypeCode=1005&OptType=8';
        var SchoolStageUrl = '@Url.Content("~/SystemManage/StaticDictionary/GetListJson")' + '?TypeCode=1002&OptType=8';
        var GradeUrl = '@Url.Content("~/SystemManage/StaticDictionary/GetListJson")' + '?TypeCode=1003&OptType=8';

        var pictureicon = '@Url.Content("~/image/picture.png")';
        var remarkicon = '@Url.Content("~/image/remark.png")';
        var upicon = '@Url.Content("~/image/up.png")';
        var downicon = '@Url.Content("~/image/down.png")';
        var delicon = '@Url.Content("~/image/del.png")';
        var IsSaveQuestion = 0;//是否需要保存
        var IsSaveQuestion = 0;//是否需要保存
        var TimerMillisecond = 40;
        var SavePay = 1;//保存方式，1：提交按钮触发  2：定时保存  3：操作触发保存  4：
        $(function () {
            getForm();

            $('#form').validate({
                rules: {
                    Title: { required: true },
                    Tag: { required: true}
                }
            });

            loadQuestionUploadify();
            loadOptionUploadify();
        });

        //添加选项。
        function addOption() {
            var questionType = $('#QuestionType').ysComboBoxTree('getValue');
            if(! (questionType != undefined && parseInt(questionType) > 0)) {
                layer.msg("请先选择题目题型。");
                return;
            }
            var this_index = 0;
            var num = 0;
            var max_num = 100;
            $(".option_tr").map(function (index, obj) {
                num += 1;
                var tagvalue = $(this).attr("tagvalue");
                if (parseInt(tagvalue) > this_index) {
                    this_index = parseInt(tagvalue);
                }
            });
            this_index += 1;//最大值基础上+1
            if (num >= max_num) {
                layer.msg("选项最多不能超过100个。");
                return;
            }

            var html = '';
            html  += '<tr tagvalue="' + this_index + '" class="option_tr" id="option_tr_' + this_index + '" optionsort="' + this_index +'">';
            html += '<td style="min-width: 300px;">';
            html += '<input type="hidden" id="optionid_' + this_index +'"  value="0" />';
            html += '<input type="text" id="name_' + this_index +'" class="form-control" tabindex="' + this_index +'" title="回车添加新选项，上下键编辑前后选项" style="border: 1px solid rgb(205, 205, 205);" />';
            html += '</td>';
            html += '<td align="center" style="min-width: 50px; ">';
            html += '<input type="hidden" id="hidpicture_' + this_index + '" value=""><img id="imgpicture_' + this_index + '" src="' + pictureicon + '" class="option-icon" onclick="uploadOption(' + this_index + ');"/>';
            html += '<span class="keywords" idValue="" id="spanattment_' + this_index +'" style="display:none;"></span>';
            html += '</td>';
            html += '<td style="min-width: 50px; ">';
            html += '<input type="hidden" id="hidNameDesc_' + this_index +'" value="">';
            html += '<div style="overflow: hidden; text-align: center;"><img src="' + remarkicon + '" class="option-icon" onclick="showNameDescForm(' + this_index +');" /></div>';
            html += '</td>';
            html += '<td style="min-width: 50px; text-align: center; " class="tdoption_checkbox">';
            html += '<div id="divcheckbox_' + this_index + '"  onclick="optioncheckbox(this,' + this_index +');" class="icheckbox-blue" style="top: auto; left: auto; position: initial; margin-left: 46%;">';
            html += '<input name="option_checkbox" type="checkbox" style="position: absolute; opacity: 0;">';
            html += '</div>';
            html += '</td>';
            html += ' <td align="left" style="padding: 3px 5px 3px 15px; min-width:100px;">';
            html += '<img src="' + upicon + '" class="option-icon" onclick="optionup(this,' + this_index + ');"/> <img src="' + downicon + '" class="option-icon" onclick="optiondown(this,' + this_index +');" />';
            html += '</td>';
            html += '<td align="center" style="width: 60px; ">';
            html += '<img  src="' + delicon + '" class="option-icon" onclick="delOption(this,' + this_index + ');"/>';
            html += '</td>';
            html += '</tr>';
            $("#optionBody").append(html);
            //绑定事件。
            loadPrivewEvent(this_index);
            refreshPreview();
            return this_index;
        }

        function getForm() {
            if (id > 0) {
                ys.ajax({
                    url: '@Url.Content("~/PaperExamineManage/QuestionBank/GetFormJson")' + '?id=' + id,
                    type: 'get',
                    success: function (obj) {
                        if (obj.Tag == 1) {
                            //$('#form').setWebControls(obj.Data);
                            $("#Code").val(obj.Data.Code);
                            $("#BaseModifyTime").val(obj.Data.BaseModifyTime);
                            $("#Tag").val(obj.Data.Tag);
                            $("#Title").val(obj.Data.Title);
                            //加载图片。
                            if (obj.Data.AttachmentList != undefined && obj.Data.AttachmentList.length > 0) {
                                for (var i = 0; i < obj.Data.AttachmentList.length; i++) {
                                    loadImageTitle(obj.Data.AttachmentList[i].Path, obj.Data.AttachmentList[i].Title, obj.Data.AttachmentList[i].Id);
                                }
                            }
                            loadBindData(obj.Data);
                            if (obj.Data.Options != undefined && obj.Data.Options.length > 0) {
                                for (var i = 0; i < obj.Data.Options.length; i++) {
                                    var OptionEtt = obj.Data.Options[i];
                                    if (OptionEtt != undefined) {
                                        var tagindex = addOption();
                                        $("#optionid_" + tagindex).val(OptionEtt.Id);
                                        $("#name_" + tagindex).val(OptionEtt.Name);
                                        $("#hidNameDesc_" + tagindex).val(OptionEtt.NameDesc);
                                        if (OptionEtt.IsTrue == 1) {
                                            $("#divcheckbox_" + tagindex).addClass('checked');
                                        }

                                        $("#option_tr_" + tagindex).attr("optionsort", OptionEtt.Sort);
                                        //说明

                                        //图片
                                        if (OptionEtt.Attachment!=undefined) {
                                            loadOptionAtt(OptionEtt.Attachment.Path, OptionEtt.Attachment.Title, OptionEtt.Attachment.Id, tagindex);
                                        }
                                    }
                                }
                            }
                            loadPreviewEffectInit();
                        }
                    }
                });
            }
            else {
                var defaultData = {};
                $('#form').setWebControls(defaultData);
                //下拉
                loadBindData(undefined);
                //addOption();
                //addOption();
            }
        }

        //#region 预览效果

        //加载预览效果
        function loadPreviewEffectInit() {
            $("#preTitle").html("1：");
            var questiontypeNature = $("#hidQuestionTypeNature").val();
            var title = $("#Title").val();
            if (title != undefined && title.length>0) {
                $("#preTitle").html("1：" + title);
            }
            $("#preOptions").html('');
            updateTitlePicture();
            //加载选项
            $.each($("#optionBody .option_tr"), function (index, item) {
                var optinIndex = $(this).attr("tagvalue");
                var optionTag = getOptionTag(index);
                var optionmsg = $("#name_" + optinIndex).val();
                if (!(optionmsg != undefined && optionmsg.length > 0)) {
                    optionmsg = '';
                }
                var html = $.Format('<div class="preOption" id="preOption_{0}" style="padding-left:25px;padding-bottom: 10px;">', optinIndex);
                if (questiontypeNature == 1) {
                    html += '<label class="radio-box" style="padding-left:0px;">';
                    html += '<input type="radio" name="previewradiobox" value="2" ref="undefined">';
                    html += $.Format('&nbsp;&nbsp;{0}：<span id="prename_{2}">{1}<span></label>', optionTag, optionmsg, optinIndex);
                    html += '</label>';
                } else { 
                    html += '<label class="check-box">';
                    html += '<div class="icheckbox-blue" onclick="previewcheckbox(this);"><input name="previewcheckbox" type="checkbox" style="position: absolute; opacity: 0;"></div>';
                    html += $.Format('{0}：<span id="prename_{2}">{1}<span></label>', optionTag, optionmsg, optinIndex);
                }
                html += $.Format('<div id="prepicture_{0}" class="preview-picture">{1}</div>', optinIndex, updateOptionPicture(optinIndex, 1));
                html += updateNameDesc(optinIndex, 1);
                html += '</div>';
                $("#preOptions").append(html);
            });
        }
        function updateTitlePicture() {
            $("#preTitlePicture").html(""); 
            $("#questionimgs .keywords img").each(function () {
                var html = '';
                var thisPath = $(this).attr("src");
                if (thisPath != undefined && thisPath.length > 0) {
                    html += $.Format('<div class="preview-picture" style="display: inline-block;"><img src="{0}" /></div>', thisPath);
                }
                $("#preTitlePicture").append(html);
            });
        }

        //更新选项图片效果
        function updateOptionPicture(tagindex, opttype) {
            var html = '';
            var thisPath = $("#spanattment_" + tagindex + " img").attr("src");
            if (thisPath != undefined && thisPath.length > 0) {
                html = $.Format('<img src="{0}"/>', thisPath);
            }
            if (opttype == 1) {
                 
            } else {
                $("#prepicture_" + tagindex).html(html);
            }
            return html;
        }

        /*更新说明信息
         *tagindex 索引
         *pttype 1:添加   2：更新
         */
        function updateNameDesc(tagindex, opttype) {
            var html = '';
            var namedesc = $("#hidNameDesc_" + tagindex).val();
            var thisObj = $("#prenamedesc_" + tagindex).val();
            if (namedesc != undefined && namedesc.length > 0) {
                if (opttype == 1) {
                    html += $.Format('<div id="prenamedesc_{1}" class="preview-namedesc" >（注：{0}）</div>', namedesc, tagindex);
                } else if (thisObj == undefined) {
                    $("#preOption_" + tagindex).append($.Format('<div id="prenamedesc_{1}" class="preview-namedesc">（注：{0}）</div>', namedesc, tagindex));
                } else {
                    $("#prenamedesc_" + tagindex).text($.Format("（注：{0}）", namedesc));
                }
            } else {
                if (thisObj != undefined) {
                    thisObj.remove();
                }
            }
            return html;
        }

        //获取字母
        function getOptionTag(index) {
            var html = '';
            var strArr = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'];
            if (index > 25) {
                var thisIndex = index % 25;
                var thisNum = index / 25;
                html = (strArr[thisIndex] + '' + thisNum);
            } else {
                html = strArr[index];
            }
            return html;
        }

        function previewcheckbox(obj) {
            var classstr = $(obj).attr('class');
            if (classstr != undefined && classstr.length > 0) {
                if (classstr.indexOf('checked') > -1) {
                    $(obj).removeClass("checked");
                } else {
                    $(obj).addClass('checked');
                }
            }
        }

        /**
         * 绑定事件更新
         * 
         */
        function loadPrivewEvent(tagindex) {
            //标题
            $("#Title").bind("keyup", function () {
                $("#preTitle").html("1：" + $(this).val());
            });

            //名称
            $("#name_" + tagindex).bind("keyup", function () {
                $("#prename_" + tagindex).text($(this).val());
            });

            //备注的改变事件。
            $("#hidNameDesc_" + tagindex).change(function () {
                updateOptionPicture(tagindex, 2);
            });
        }

        function refreshPreview() {
            loadPreviewEffectInit();
        }
        //#endregion

        //#region 加载下拉选项。

        function loadBindData(postData) {
            if (postData != undefined) {
                Paper.BindNatureRadioBox($("#Nature"), postData.Nature);
                loadQuestionOption(postData.QuestionType);
                // Paper.BindComboBox($("#QuestionType"), QuestionTypeUrl, postData.QuestionType, 0, '',0);
                Paper.BindComboBox($("#Classify"), ClassifyUrl, postData.Classify, 0, '', 0);
                Paper.BindComboBox($("#UseObj"), UseObjUrl, postData.UseObj, 0, ',0');
                Paper.BindChangeComboBox($("#CourseId"), CourseUrl, postData.CourseId, 0, '', 1, loadSchoolStage);
                Paper.BindChangeComboBox($("#SchoolStage"), (SchoolStageUrl + "&Pid=" + postData.CourseId), postData.SchoolStage, 0, '', 1, loadGrade);
                if (postData.SchoolStage > 0) {
                    Paper.BindComboBox($("#GradeId"), (GradeUrl + "&Pid=" + postData.SchoolStage), postData.GradeId, 0, '', 1);
                } else {
                    Paper.BindComboBox($("#GradeId"), (GradeUrl + "&Pid=" + postData.CourseId), postData.GradeId, 0, '', 1);
                }
            } else {
                Paper.BindNatureRadioBox($("#Nature"), 1);
                /*  Paper.BindComboBox($("#QuestionType"), QuestionTypeUrl, -1, 0, ',0');*/
                loadQuestionOption(0);
                Paper.BindComboBox($("#Classify"), ClassifyUrl, -1, 0, '', 0);
                Paper.BindComboBox($("#UseObj"), UseObjUrl, -1, 0, '', 0);
                //学科、学段、年级
                //Paper.BindComboBox($("#CourseId"), CourseUrl, 0, 0, '');
                Paper.BindChangeComboBox($("#CourseId"), CourseUrl, -1, 0, '', 1, loadSchoolStage);
                loadSchoolStage(-1);
                loadGrade(-1);
            }
        }


        function loadSchoolStage(selectid) {
            if (selectid > 0) {
                Paper.BindChangeComboBox($("#SchoolStage"), (SchoolStageUrl + "&Pid=" + selectid), -1, 0, '', 1, loadGrade);
            } else {
                Paper.BindAllEmptyComboBox($("#SchoolStage"), -1, 0, '', 1, loadGrade);
            }
        }

            /*选择学段，根据学段加载班级
             *  没有选择学段、选择了学科，根据学科加载。
             *  否则加载全部（默认）。
             */
        function loadGrade(selectid) {
            var selectPid = $("#SchoolStage").ysComboBox('getValue');
            if (!parseInt(selectPid) > 0) {
                var courseid = $("#CourseId").ysComboBox('getValue');
                if (parseInt(courseid) > 0) {
                    selectPid = courseid;
                }
            }
            if (parseInt(selectid) > 0) {
                Paper.BindComboBox($("#GradeId"), (GradeUrl + "&Pid=" + selectPid), 0, 0, '', 1);
            } else {
                Paper.BindAllEmptyComboBox($("#GradeId"), -1, 0, '', 1, undefined);
            }
        }

        function loadQuestionOption(selectid) {
            $('#QuestionType').ysComboBoxTree({
                url: '@Url.Content("~/SystemManage/StaticDictionary/GetTreeJson")' + '?TypeCode=2002',
                class: 'form-control',
                //key: 'DictionaryId',
                //value: 'DicName',
                callback: {
                    customOnClick: function (event, treeId, treeNode) {
                        if (typeof (treeNode) == "string") {
                            treeNode = JSON.parse(treeNode);
                        }
                        $("#hidQuestionTypeNature").val(0);
                        if (treeNode.Attribute != undefined && treeNode.Attribute > 0) {
                            refreshPreview();
                            $("#hidQuestionTypeNature").val(treeNode.Attribute);
                        }
                    }
                }
            });
            if (parseInt(selectid) > 0) {
                $('#QuestionType').ysComboBoxTree("setValue", selectid)
            }
        }


        //#endregion

        //#region 保存方法

        function saveBtnForm() {
            SavePay = 1;
            saveQuestionForm();
        }

        function saveQuestionForm() {
            if ($('#form').validate().form()) {
                var postData = $('#form').getWebControls({ Id: id });
                postData.Nature = $("#Nature .radio-box input:checked").val();
                if (typeof (postData) == "string") {
                    postData = JSON.parse(postData);
                }
                var errorMsg = '';
                if (!(postData.Nature != undefined && parseInt(postData.Nature) > 0)) {
                    errorMsg += '请选择题目性质<br/>';
                }
                if (!(postData.QuestionType != undefined && parseInt(postData.QuestionType) >= 0)) {
                    errorMsg += '请选择题型<br/>';
                }
                if (!(postData.Classify != undefined && parseInt(postData.Classify) >= 0)) {
                    errorMsg += '请选择题目分类<br/>';
                }
                if (!(postData.CourseId != undefined && parseInt(postData.CourseId) >= 0)) {
                    errorMsg += '请选择适用学科<br/>';
                }
                if (!(postData.SchoolStage != undefined && parseInt(postData.SchoolStage) >= 0)) {
                    errorMsg += '请选择适用学段<br/>';
                }
                if (!(postData.GradeId != undefined && parseInt(postData.GradeId) >= 0)) {
                    errorMsg += '请选择适用年级<br/>';
                }
                if (!(postData.UseObj != undefined && parseInt(postData.UseObj) >= 0)) {
                    errorMsg += '请选择适用对象<br/>';
                }
                //加载图片
                var imageArr = [];
                $("#questionimgs .keywords").each(function () {
                    var idValue = $(this).attr("idValue");
                    imageArr.push(idValue);
                });
                postData.AttachmentIds = imageArr;

                //判断是否选中正确答案，单选只能选一个。
                var questionTypeNature = $("#hidQuestionTypeNature").val();
                var checknum = 0;
                $.each($(".tdoption_checkbox .checked"), function (index, item) {
                    checknum += 1;
                });
                if (parseInt(questionTypeNature) == 1) {
                    if (checknum==0) {
                        errorMsg += '请选择题目的正确答案。<br/>';
                    } else if (checknum > 1) {
                        errorMsg += '当前题型正确答案只能有一个。<br/>';
                    }
                } else if (parseInt(questionTypeNature) == 2) {
                    if (checknum == 0) {
                        errorMsg += '请选择题目的正确答案。<br/>';
                    }
                }

                var options = [];
                if (SavePay == 1 || SavePay == 2) {
                    //只有提交保存，和定时保存的时候才会保存所有
                    $(".option_tr").map(function (index, item) {
                        var tagindex = $(this).attr("tagvalue");
                        var optionid = $("#optionid_" + tagindex).val();
                        if (optionid == undefined || !(parseInt(optionid) > 0)) {
                            optionid = 0;
                        }
                        var namestr = $("#name_" + tagindex).val();
                        if (!(namestr != undefined && namestr.length > 0)) {
                            errorMsg += "请填写选项文字。<br/>";
                        }
                        var attachmentid = 0;
                        var pictureval = $("#hidpicture_" + tagindex).val()
                        if (pictureval != undefined && parseFloat(pictureval) > 0) {
                            attachmentid = pictureval;
                        }
                        var namedesc = $("#hidNameDesc_" + tagindex).val();
                        var istrueclass = $("#divcheckbox_" + tagindex).attr("class");
                        var istrue = 0;
                        if (istrueclass != undefined && istrueclass.indexOf('checked') > -1) {
                            istrue = 1;
                        }
                        var sort = $("#option_tr_" + tagindex).attr("optionsort");
                        options.push({ Id: optionid, Name: namestr, AttachmentId: attachmentid, NameDesc: namedesc, Sort: sort, IsTrue: istrue });
                    });

                    if (options.length == 0) {
                        errorMsg = "请添加题目选项";
                    }
                }
                if (errorMsg != '') {
                    if (SavePay == 1) {
                        layer.open({
                            content: errorMsg
                            , btn: ['关闭']
                            , yes: function (index, layero) {
                                //按钮【按钮一】的回调
                                layer.close(index);
                            }
                            , cancel: function () {
                                //右上角关闭回调
                                layer.closeAll();
                                //return false 开启该代码可禁止点击该按钮关闭
                            }
                        });
                        return false;
                    } else {

                    }
                }
                postData.SavePay = SavePay;
                postData.Options = options;
                ys.ajax({
                    url: '@Url.Content("~/PaperExamineManage/QuestionBank/SaveFormJson")',
                    type: 'post',
                    data: postData,
                    success: function (obj) {
                        if (obj.Tag == 1) {
                            if (typeof (obj.Data)=="string") {
                                obj.Data = JSON.parse(obj.Data);
                            }
                            if (obj.Data!=undefined) {
                                id = obj.Data.Id;
                                $("#Code").val(obj.Data.Code);
                                $("#BaseModifyTime").val(obj.Data.BaseModifyTime);
                            }

                            if (SavePay == 1) {
                                layer.open({
                                    content: '保存提交成功，点击确认返回“自编题库”列表，'
                                    , btn: ['确定', '关闭']
                                    , yes: function (index, layero) {
                                        //按钮【按钮一】的回调
                                        var url = '@Url.Content("~/PaperExamineManage/QuestionBank/List")';
                                        createMenuAndCloseCurrent(url, "自编题库");
                                    }, btn2: function (index, layero) {
                                        layer.close(index);
                                    }
                                    , cancel: function () {
                                        //右上角关闭回调
                                        layer.closeAll();
                                        //return false 开启该代码可禁止点击该按钮关闭
                                    }
                                });
                            }
                            //ys.msgSuccess(obj.Message);
                            //parent.$('#gridTable').ysTable('refresh');parent.resetToolbarStatus();
                        /*    parent.layer.close(index);*/
                        }
                        else {
                            ys.msgError(obj.Message);
                        }
                    }
                });
            }
        }

        function saveOptionForm(tagindex) {
            var namestr = $("#name_" + tagindex).val();
            var istrueclass = $("#divcheckbox_" + tagindex).attr("class");
            var istrue = 0;
            if (istrueclass != undefined && istrueclass.indexOf('checked') > -1) {
                istrue = 1;
            }
            var sort = $("#option_tr_" + tagindex).attr("optionsort");
            var postData = { Name: namestr, Sort: sort, IsTrue: istrue, QuestionBankId: id };
            ys.ajax({
                url: '@Url.Content("~/PaperExamineManage/QuestionBank/SaveFormJson")',
                type: 'post',
                data: JSON.stringify(postData),
                success: function (obj) {
                    return obj;
                    //if (obj.Tag == 1) {
                    //    ys.msgSuccess(obj.Message);
                    //    //parent.$('#gridTable').ysTable('refresh');parent.resetToolbarStatus();
                    //    /*    parent.layer.close(index);*/
                    //}
                    //else {
                    //    ys.msgError(obj.Message);
                    //}
                }
            });
        }
        //#endregion

        //#region 上传图片

        var ImageQuestionIndex = 0;

        function loadQuestionUploadify() {
            $("#uploadify").uploadifive({
                'uploadScript': '/File/UploadFile?fc=2001',
                'cancelImg': '~/lib/uploadifive/uploadifive-cancel.png',
                'buttonText': '上 传',
                'queueID': 'fileQueue',
                'auto': true,
                'multi': false,
                'fileDesc': '支持格式：*.jpg;*.gif;*.png;*.pdf;',
                'fileExt': '*.jpg;*.gif;*.png;*.pdf;',
                'onAddQueueItem': function (file) {

                },
                'onUploadComplete': function (fileObj, response) {
                    if (typeof (response) == "string" && response != "") {
                        response = JSON.parse(response);
                        if (response.Tag == 1) {
                            ys.msgSuccess("上传成功！");
                            var id = response.Description;
                            var filePath = response.Data;
                            var fileTitle = response.Message;
                            var fileExt = filePath.substring(filePath.lastIndexOf('.')).toUpperCase();
                            if (fileExt == ".JPG" || fileExt == ".BMP" || fileExt == ".JPEG" || fileExt == ".PNG" || fileExt == ".GIF") {
                                $("#questionimgs").append('<span class="keywords" idValue="' + id + '"> <a type="del" onclick="delFile(this,\'' + id + '\')"></a><a  modal="zoomImg"  href="javascript:void(0);" src="' + filePath + '" ><img src="' + filePath + '" class="option-icon" title="' + fileTitle+'"/></a></span>');
                            }
                            updateTitlePicture();
                            imgages.showextalink();
                        } else {
                            ys.msgError(response.Message);
                        }
                    }
                },
                'onFallback': function (event) {
                    ys.msgError(event);
                }
            });
        }

        function uploadQuestion() {
            var obj = undefined;
            $("#uploadifive-uploadify input").map(function (index, item) {
                obj = $(this);
                var styleStr = obj.attr("style");
                if (styleStr.indexOf('display')>-1) {
                    obj = undefined;
                }
            });
            if (obj!=undefined) {
                obj.click();
            }
            //$("#uploadifive-uploadify input:eq(1)").click();
        }

        function loadImageTitle(filePath, fileTitle, id) {
            var fileExt = filePath.substring(filePath.lastIndexOf('.')).toUpperCase();
            if (fileExt == ".JPG" || fileExt == ".BMP" || fileExt == ".JPEG" || fileExt == ".PNG" || fileExt == ".GIF") {
                $("#questionimgs").append('<span class="keywords" idValue="' + id + '"> <a type="del" onclick="delFile(this,\'' + id + '\')"></a><a  modal="zoomImg"  href="javascript:void(0);" src="' + filePath + '" ><img src="' + filePath + '" class="option-icon" title="' + fileTitle + '"/></a></span>');
            }
            imgages.showextalink();
        }

        //选项图片上传
        var ImageOptionIndex = 0;

        function uploadOption(optinindex) {
            ImageOptionIndex = optinindex;
            var obj = undefined;
            $("#uploadifive-optionUploadify input").map(function (index, item) {
                obj = $(this);
                var styleStr = obj.attr("style");
                if (styleStr.indexOf('display') > -1) {
                    obj = undefined;
                }
            });
            if (obj != undefined) {
                obj.click();
            }
            //$("#uploadifive-uploadify input:eq(1)").click();
        }

        function loadOptionUploadify() {
            $("#optionUploadify").uploadifive({
                'uploadScript': '/File/UploadFile?fc=2002',
                'cancelImg': '~/lib/uploadifive/uploadifive-cancel.png',
                'buttonText': '上 传',
                'queueID': 'fileOptionQueue',
                'auto': true,
                'multi': false,
                'fileDesc': '支持格式：*.jpg;*.gif;*.png;*.pdf;',
                'fileExt': '*.jpg;*.gif;*.png;*.pdf;',
                'onAddQueueItem': function (file) {

                },
                'onUploadComplete': function (fileObj, response) {
                    if (typeof (response) == "string" && response != "") {
                        response = JSON.parse(response);
                        if (response.Tag == 1) {
                            ys.msgSuccess("上传成功！");
                            loadOptionAtt(response.Data, response.Message, response.Description, 0);
                        } else {
                            ys.msgError(response.Message);
                        }
                    }
                },
                'onFallback': function (event) {
                    ys.msgError(event);
                }
            });
        }

        function loadOptionAtt(filePath, fileTitle, id, tagindex) {
            var currentTagIndex = ImageOptionIndex;
            if (parseInt(tagindex) > 0) {
                currentTagIndex = tagindex;
            }
            var fileExt = filePath.substring(filePath.lastIndexOf('.')).toUpperCase();
            if (fileExt == ".JPG" || fileExt == ".BMP" || fileExt == ".JPEG" || fileExt == ".PNG" || fileExt == ".GIF") {
                var html = $.Format('<span><a type="del" onclick="delOptionFile(this,{0},{1})"></a><a  modal="zoomImg"  href="javascript:void(0);" src="{2}" ><img src="{2}" class="option-icon" title="{3}"/></a><span>', id, currentTagIndex, filePath, fileTitle);
                $("#hidpicture_" + currentTagIndex).val(id);
                $("#spanattment_" + currentTagIndex).html(html);
                $("#spanattment_" + currentTagIndex).show();
                $("#imgpicture_" + currentTagIndex).hide();
                updateOptionPicture(currentTagIndex, 2);
            }
            imgages.showextalink();
        }
        //删除选项图片
        function delOptionFile(obj, value,tagindex) {
            $("#spanattment_" + tagindex).hide();
            $("#imgpicture_" + tagindex).show();
            $(obj).parent().remove();
            updateOptionPicture(tagindex, 2);
            imgages.showextalink();
        }

        //删除附件
        function delFile(obj, value) {
            $(obj).parent().remove();
            imgages.showextalink();
            updateTitlePicture();
        }
        //#endregion

        //#region 多选框选择正确答案方法  ，上下排序功能, 删除方法。

        //选择正确答案方法。
        function optioncheckbox(obj, tagindex) {
            var classstr = $(obj).attr('class');
            if (classstr != undefined && classstr.length > 0) {
                if (classstr.indexOf('checked') > -1) {
                    $(obj).removeClass("checked");
                } else {
                    var questionTypeNature = $("#hidQuestionTypeNature").val();
                    if (parseInt(questionTypeNature) == 2) {
                        $(obj).addClass('checked');
                    } else {
                        $(obj).addClass('checked');
                        $.each($(".tdoption_checkbox .checked"), function (index, item) {
                            if ($(this).attr("id") != ("divcheckbox_" + tagindex)) {
                                $(this).removeClass("checked")
                            }
                        });
                    }
                }
            }
        }

        //排序向上
        function optionup(obj, tagindex) {
            var thisSort = $("#option_tr_" + tagindex).attr("optionsort");
            var prevObj = $("#option_tr_" + tagindex).prev();
            if (prevObj != undefined && prevObj.attr("optionsort") != undefined) {
                var prevSort = prevObj.attr("optionsort");
                if (prevSort != undefined && parseInt(prevSort) > 0 && parseInt(prevSort) < parseInt(thisSort)) {
                    var tempSort = thisSort;
                    thisSort = prevSort;
                    prevSort = tempSort;
                } else {
                    prevSort = parseInt(thisSort) + 1;
                }
                $("#option_tr_" + tagindex).attr("optionsort", thisSort);
                prevObj.attr("optionsort", prevSort);
                $("#option_tr_" + tagindex).after(prevObj);
                refreshPreview();
            } else {
                ys.msgError("你已是第一个选项了。");
            }
        }

        //排序向下
        function optiondown(obj, tagindex) {
            var thisSort = $("#option_tr_" + tagindex).attr("optionsort");
            var nextObj = $("#option_tr_" + tagindex).next();
            if (nextObj != undefined && nextObj.attr("optionsort")!= undefined) {
                var nextSort = nextObj.attr("optionsort");
                if (nextSort != undefined && parseInt(nextSort) > 0 && parseInt(thisSort) < parseInt(nextSort)) {
                    var tempSort = thisSort;
                    thisSort = nextSort;
                    nextSort = tempSort;
                } else {
                    nextSort = parseInt(thisSort) - 1;
                }
                $("#option_tr_" + tagindex).attr("optionsort", thisSort);
                nextObj.attr("optionsort", nextSort);
                $("#option_tr_" + tagindex).before(nextObj);
                refreshPreview();
            } else {
                ys.msgError("你已是最后一个选项了。");
            }
        }

        //删除方法
        function delOption(obj, tagindex) {
            ys.confirm('确认要删除当前选项吗？', function () {
                $("#option_tr_" + tagindex).remove();
                //删除预览效果
                refreshPreview();
            });
        }

        //#endregion

        //#region 处理题目选项的说明。

        function showNameDescForm(optionindex) {
            var namedesc = $("#hidNameDesc_" + optionindex).val();
            ys.openDialog({
                title: '选项说明',
                content: '@Url.Content("~/PaperExamineManage/QuestionBank/TextareaForm")' + '?namedesc=' + namedesc + '&optionindex=' + optionindex,
                width: '560px',
                height: '260px',
                callback: function (index, layero) {
                    var iframeWin = window[layero.find('iframe')[0]['name']];
                    iframeWin.saveForm(index);
                }
            });
        }

        function backSaveNameDesc(namedesc, optionindex) {
            $("#hidNameDesc_" + optionindex).val(namedesc);
            updateNameDesc(optionindex, 2);
        }

        //#endregion
    </script>
}

