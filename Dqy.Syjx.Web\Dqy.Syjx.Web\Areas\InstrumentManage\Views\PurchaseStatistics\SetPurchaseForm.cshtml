﻿@{ Layout = "~/Views/Shared/_FormWhite.cshtml"; }

<div class="wrapper animated fadeInRight">
    <form id="form" class="form-horizontal m">
        <div class="form-group row">
            <label class="col-sm-2 control-label ">采购方式：</label>
            <div class="col-sm-6">
                <div id="purchaseType" col="PurchaseType"></div>
            </div>
        </div>
    </form>
</div>

<script type="text/javascript">
    var ids = parent.SELECTED_IDS;
    $(function () {
        $("#purchaseType").ysRadioBox({ data: ys.getJson(@Html.Raw(typeof(PurchaseTypeEnum).EnumToDictionaryString())), class:"form-control"});
    });

    function saveForm(index) {
        if ($("#form").validate().form()) {
            var postData = $("#form").getWebControls({ Ids: ids });
            if (!(postData.PurchaseType > 0)) {
                ys.msgError('请选择采购方式');
                return false;
            }
            ys.ajax({
                url: '@Url.Content("~/InstrumentManage/PurchaseStatistics/UpdatePurchaseType")',
                type: "post",
                data: postData,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        parent.$('#gridTable').ysTable('refresh');parent.resetToolbarStatus();
                        parent.layer.close(index);
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        }
    }
</script>
