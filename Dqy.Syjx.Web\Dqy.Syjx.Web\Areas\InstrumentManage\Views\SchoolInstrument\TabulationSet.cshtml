﻿@{
    Layout = "~/Views/Shared/_FormWhite.cshtml";

    OperatorInfo UserInfo = ViewBag.UserInfo as OperatorInfo;
}
@inject Microsoft.AspNetCore.Hosting.IWebHostEnvironment HostingEnvironment


<div class="wrapper animated fadeInRight">

        <form id="form" class="form-horizontal m">

            <div class="form-group row">
                <label class="col-sm-2 control-label ">制表人</label>
                <div class="col-sm-8">
                    <input id="name" col="Name" type="text" value="@UserInfo.RealName" class="form-control" />
                </div>
            </div>

            <div class="form-group row">
                <label class="col-sm-2 control-label ">制表日期</label>
                <div class="col-sm-8">
                    <input id="purchaseDate" col="PurchaseDate" type="text" value="@DateTime.Now.ToString("yyyy-MM-dd")" class="form-control" readonly />
                </div>
            </div>

        </form>
</div>
<script type="text/javascript">
    var id = ys.request("id");

    $(function () {
        laydate.render({ elem: '#purchaseDate', format: 'yyyy-MM-dd', trigger: 'click', position: 'fixed' });


    });


    function saveForm(index) {
        if ($('#form').validate().form()) {
            var postData = $("#form").getWebControls({ Id: id});
            if (postData.Name==""){
                ys.msgError('请填写制表人！');
                return;
            }
            parent.zhiBiaoRen = postData.Name;
            parent.zhiBiaoRiQi = postData.PurchaseDate;
            if (id == 0) {
                parent.exportInfo();
                parent.layer.close(index);
            }else{
                parent.exportA6Info();
                parent.layer.close(index);
            }
            
        }
    }
</script>
