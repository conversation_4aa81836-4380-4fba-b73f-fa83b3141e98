﻿@{
    Layout = "~/Views/Shared/_FormWhite.cshtml";
}

<div class="wrapper animated fadeInRight">
    <form id="form" class="form-horizontal m">
        <div class="form-group row">
            <label class="col-sm-3 control-label ">状态<font class="red"> *</font></label>
            <div class="col-sm-8">
                <div id="statuz" col="Statuz"></div>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">章节<font class="red"> *</font></label>
            <div class="col-sm-8">
                <input id="chapter" col="Chapter" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">章节排序<font class="red"> *</font></label>
            <div class="col-sm-8">
                <input id="chapterSort" col="ChapterSort" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">实验代码<font class="red"> *</font></label>
            <div class="col-sm-8">
                <input id="experimentCode" col="ExperimentCode" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">实验名称<font class="red"> *</font></label>
            <div class="col-sm-8">
                <input id="experimentName" col="ExperimentName" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">实验类型<font class="red"> *</font></label>
            <div class="col-sm-8">
                <div id="experimentType" col="ExperimentType"></div>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">实验要求<font class="red"> *</font></label>
            <div class="col-sm-8">
                <div id="isNeedDo" col="IsNeedDo"></div>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">是否考核<font class="red"> *</font></label>
            <div class="col-sm-8">
                <div id="isEvaluate" col="IsEvaluate"></div>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">是否基础实验<font class="red"> *</font></label>
            <div class="col-sm-8">
                <div id="isBase" col="IsBase"></div>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">所需仪器</label>
            <div class="col-sm-8">
                <textarea id="equipmentNeed" col="EquipmentNeed" type="text" class="form-control"></textarea>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">实验材料（含试剂）</label>
            <div class="col-sm-8">
                <textarea id="materialNeed" col="MaterialNeed" type="text" class="form-control"></textarea>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">备注</label>
            <div class="col-sm-8">
                <input id="remark" col="Remark" type="text" class="form-control" />
            </div>
        </div>
    </form>
</div>

<script type="text/javascript">
    var id = ys.request("id");
    var textbookVersionBaseId = ys.request("textbookVersionBaseId");
    $(function () {
        
        loadStatuz();
        loadExperimentType();
        loadIsNeedDo();
        loadIsEvaluate();
        loadIsBase();

        getForm();

        $('#form').validate({
            rules: {
                statuz: { required: true },
                chapter: { required: true },
                chapterSort: { number: true },
                experimentCode: { required: true },
                experimentName: { required: true },
            }
        });
    });

    function getForm() {
        if (id > 0) {
            ys.ajax({
                url: '@Url.Content("~/ExperimentTeachManage/TextbookVersionDetail/GetFormJson")' + '?id=' + id,
                type: 'get',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        $('#form').setWebControls(obj.Data);
                    }
                }
            });
        }
    }

    function saveForm(index) {
        if ($('#form').validate().form()) {
            var postData = $('#form').getWebControls({ Id: id, TextbookVersionBaseId: textbookVersionBaseId });
            var msg = checkInputValid(postData);
            if (msg != '') {
                ys.msgError(msg);
                return false;
            }
            ys.ajax({
                url: '@Url.Content("~/ExperimentTeachManage/TextbookVersionDetail/SaveFormJson")',
                type: 'post',
                data: postData,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        parent.$('#gridTable').ysTable('refresh');parent.resetToolbarStatus();
                        parent.layer.close(index);
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        }
    }

    function checkInputValid(postData) {
        var msg = '';
        if (!postData.Statuz) {
            msg += '请选择状态！<br />';
        }
        if (!postData.ExperimentType) {
            msg += '请选择实验类型！<br />';
        }
        if (!postData.IsNeedDo) {
            msg += '请选择实验要求！<br />';
        }
        if (!postData.IsEvaluate) {
            msg += '请选择是否考核！<br />';
        }
        if (!postData.IsBase) {
            msg += '请选择是否基础实验！<br />';
        }
        //if (!postData.EquipmentNeed) {
        //    msg += '请输入所需主要仪器及器材！<br />';
        //}
        //if (!postData.MaterialNeed) {
        //    msg += '请输入所需试剂！<br />';
        //}
        return msg;
    }


    function loadStatuz() {
        $("#statuz").ysComboBox({ data: ys.getJson(@Html.Raw(typeof(StatusEnum).EnumToDictionaryString())), class: 'form-control' });
    }
    function loadExperimentType() {
        $("#experimentType").ysComboBox({ data: ys.getJson(@Html.Raw(typeof(ExperimentTypeEnum).EnumToDictionaryString())), class: 'form-control' });
    }
    function loadIsNeedDo() {
        $("#isNeedDo").ysComboBox({ data: ys.getJson(@Html.Raw(typeof(IsNeedEnum).EnumToDictionaryString())), class: 'form-control' });
    }
    function loadIsEvaluate() {
        $("#isEvaluate").ysComboBox({ data: ys.getJson(@Html.Raw(typeof(IsStatusEnum).EnumToDictionaryString())), class: 'form-control' });
    }
    function loadIsBase() {
        $("#isBase").ysComboBox({ data: ys.getJson(@Html.Raw(typeof(IsStatusEnum).EnumToDictionaryString())), class: 'form-control' });
    }
</script>

