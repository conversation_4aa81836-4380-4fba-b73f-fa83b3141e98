﻿@{
    Layout = "~/Views/Shared/_FormWhite.cshtml";
}
<div class="wrapper animated fadeInRight">
    <form id="form" class="form-horizontal m">
        <div class="form-group row">
            <label class="col-sm-3 control-label ">模块名称<font class="red"> *</font></label>
            <div class="col-sm-7">
                <input id="ModuleName" col="ModuleName" type="text" class="form-control" autocomplete="off" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">表单名称<font class="red"> *</font></label>
            <div class="col-sm-7">
                <input id="MenuName" col="MenuName" type="text" class="form-control" autocomplete="off" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">字段名称<font class="red"> *</font></label>
            <div class="col-sm-7">
                <input id="TypeName" col="TypeName" type="text" class="form-control" autocomplete="off" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">编码<font class="red"> *</font></label>
            <div class="col-sm-7">
                <input id="TypeCode" col="TypeCode" type="text" class="form-control" autocomplete="off" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">验证提示信息<font class="red"> *</font></label>
            <div class="col-sm-7">
                <input id="VerifyHint" col="VerifyHint" type="text" class="form-control" autocomplete="off" />
            </div>
        </div>
    </form>
</div>

<script type="text/javascript">
    var id = ys.request("id");
    $(function () {
        getForm();
        $('#form').validate({
            rules: {
                ModuleName: {
                    required: true,
                    minlength: 2,
                    maxlength: 31
                },
                MenuName: {
                    required: true,
                    minlength: 2,
                    maxlength: 31
                },
                TypeName: {
                    required: true,
                    minlength: 2,
                    maxlength: 31
                },
                TypeCode: {
                    required: true,
                    minlength: 2,
                    maxlength: 31
                },
                TypeCode: {
                    required: true,
                    minlength: 2,
                    maxlength: 200
                },
            }
        });
    });

    function getForm() {
        ys.ajax({
            url: '@Url.Content("~/SystemManage/ConfigSet/GetFormJson")' + '?id=' + id,
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1) {
                    $('#form').setWebControls(obj.Data);
                }
            }
        });
    }

    function saveForm(index) {
        if ($('#form').validate().form()) {
            var postData = $('#form').getWebControls({ Id: id });
            ys.ajax({
                url: '@Url.Content("~/SystemManage/ConfigSet/SaveFormJson")',
                type: 'post',
                data: postData,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        parent.$('#gridTable').ysTable('refresh');parent.resetToolbarStatus();
                        parent.layer.close(index);
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        }
    }

    function clearData() {
        var defaultData = {};
        $('#form').setWebControls(defaultData);
    }
</script>

