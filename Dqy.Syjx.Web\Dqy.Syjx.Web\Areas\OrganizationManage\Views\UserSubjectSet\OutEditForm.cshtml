﻿@{
    Layout = "~/Views/Shared/_FormWhite.cshtml";
}

<div class="wrapper animated fadeInRight">
    <form id="form" class="form-horizontal m">
        <div class="form-group row">
            <label class="col-sm-3 control-label ">学科<font class="red"> *</font></label>
            <div class="col-sm-8">
                <div id="SubjectValue" col="SubjectValue"></div>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">人员<font class="red"> *</font></label>
            <div class="col-sm-8">
                <div id="UserId" col="UserId"></div>
            </div>
        </div>
    </form>
</div>

<script type="text/javascript">
    var id = ys.request("id");
    $(function () {
        getForm();

    });
    function loadCourse(defaultValue) {
        ys.ajax({
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetSchoolCourseListJson")',
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1) {
                    bindCourseData(obj.Data, defaultValue);
                } else {
                    bindCourseData({});
                }
            }
        });
    }

    function bindCourseData(data, defaultValue) {
        $('#SubjectValue').ysComboBox({
            class: 'form-control',
            data: data,
            key: 'DictionaryId',
            value: 'DicName'
        });
        if (defaultValue > 0) {
            $('#SubjectValue').ysComboBox("setValue", defaultValue);
        }
    }

    function loadUserId(defaultValue) {
        ys.ajax({
            url: '@Url.Content("~/OrganizationManage/User/GetGroupLeaderListJson")',
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1) {
                    bindUserIdData(obj.Data, defaultValue);
                } else {
                    bindUserIdData({});
                }
            }
        });
    }

    function bindUserIdData(data, defaultValue) {
        CourseListData = data;
        $('#UserId').ysComboBox({
            class: 'form-control',
            data: data,
            key: 'Id',
            value: 'RealName'
        });
        if (defaultValue > 0) {
            $('#UserId').ysComboBox("setValue", defaultValue);
        }
    }

    function getForm() {
        if (id > 0) {
            ys.ajax({
                url: '@Url.Content("~/OrganizationManage/UserSubjectSet/GetFormJson")' + '?id=' + id,
                type: 'get',
                success: function (obj) {
                    if (obj.Tag == 1) {
           
                        loadCourse(obj.Data.SubjectValue);
                        loadUserId(obj.Data.UserId);
                    }
                }
            });
        }
        else {
            loadCourse(0);
            loadUserId(0);
            var defaultData = {};
            $('#form').setWebControls(defaultData);
        }
    }

    function saveForm(index) {
        if ($('#form').validate().form()) {
            var postData = $('#form').getWebControls({ Id: id });
            ys.ajax({
                url: '@Url.Content("~/OrganizationManage/UserSubjectSet/SaveOutFormJson")',
                type: 'post',
                data: postData,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        parent.searchGrid();
                        parent.layer.close(index);
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        }
    }
</script>

