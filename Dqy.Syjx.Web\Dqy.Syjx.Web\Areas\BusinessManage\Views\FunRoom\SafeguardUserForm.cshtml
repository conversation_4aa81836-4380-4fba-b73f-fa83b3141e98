﻿@{
    Layout = "~/Views/Shared/_FormWhite.cshtml";
}
<div class="wrapper animated fadeInRight">
    <form id="form" class="form-horizontal m">
        <div class="form-group row">
            <label class="col-sm-3 control-label ">原管理员<font class="red"> *</font></label>
            <div class="col-sm-6">
                <input id="oldUserName" type="text" class="form-control" readonly />
            </div>
            <div class="col-sm-3">
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">新管理员<font class="red"> *</font></label>
            <div class="col-sm-6">
                <div id="SafeguardUserId" col="SafeguardUserId"></div>
                
            </div>
           
            <div class="col-sm-3">
                <span class="glyphicon glyphicon-question-sign titleprompt inputprompt" aria-hidden="true" data-container="body" data-toggle="tooltip" data-placement="right"
                      data-content="你需要的人员如不在下拉框中，请到【实验员授权】栏进行授权。"></span>
            </div>
        </div>
    </form>
</div>
<input type="hidden" id="hidUserId" value="0" />
<script type="text/javascript">
    var id = ys.request("id");
    $(function () {
        $(".inputprompt").popover({
            trigger: 'hover',
            placement: 'right',
            html: true
        });
        loadSafeguardUserId();
    });
    function loadSafeguardUserId() {
        ys.ajax({
            url: '@Url.Content("~/BusinessManage/FunRoom/GetSafeguardListJson")' + '?id=' + id,
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1) {
                    $("#oldUserName").val(obj.Description);
                    $('#SafeguardUserId').ysComboBox({
                        data: obj.Data,
                        class: 'form-control',
                        key: 'Id',
                        value: 'RealName'
                    });
                } else {
                    $('#SafeguardUserId').ysComboBox({ data: [], key: 'Id', value: 'RealName', class: 'form-control' });
                    ys.msgError(obj.Message);
                }
            }
        });
    }
           
    function saveForm(index) {
        if ($('#form').validate().form()) {
            var formData = $('#form').getWebControls({ Id: id });

            var errorMsg = '';
            if (!(parseInt(formData.SafeguardUserId) > 0)) {
                errorMsg = '请选择新管理员。<br/>';
            }
            if (errorMsg != '') {
                ys.msgError('验证失败！<br/>' + errorMsg);
                return;
            }
            var postData = { id: formData.Id, safeguarduserid: formData.SafeguardUserId };
            ys.ajax({
                url: '@Url.Content("~/BusinessManage/FunRoom/SaveSafeguardFormJson")',
                type: 'post',
                data: postData,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        parent.$('#gridTable').ysTable('refresh');
                        parent.resetToolbarStatus();
                        parent.layer.close(index);
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        } else {
            ys.msgError(obj.Message);
        }
    }
     
</script>