﻿@{
    Layout = "~/Views/Shared/_FormWhite.cshtml";
}

<div class="wrapper animated fadeInRight">
    <form id="form" class="form-horizontal m">
        <div class="form-group row" id="divAttendanceStatuz">
            <label class="col-sm-2 control-label ">学生考勤：<font class="red"> *</font></label>
            <div class="col-sm-8">
                <div id="AttendanceStatuz" col="AttendanceStatuz"></div>
            </div>
        </div>
        <div class="form-group row" id="divScore">
            <label class="col-sm-2 control-label ">等级评价：<font class="red"> *</font></label>
            <div class="col-sm-8">
                <div id="Score" col="Score"></div>
            </div>
        </div>
        <div class="form-group row" id="divRemark">
            <label class="col-sm-2 control-label ">综合评价：</label>
            <div class="col-sm-8">
                <textarea id="Remark" style="max-width:100%" col="Remark" type="text" class="form-control"></textarea>
            </div>
        </div>
    </form>
</div>
<script type="text/javascript">
    var id = ys.request("id");//id标识
    var ids = ys.request("ids");//id集合标识
    var isview = ys.request("v");//1:查看
    var Com_Opt_Type = ys.request("t");//1:修改  2：批量考勤  3：批量评价
    $(function () {
        ysComboboxInit();
        if (Com_Opt_Type == 2) {
            $("#divScore").hide();
            $("#divRemark").hide();
        } else if (Com_Opt_Type == 3) {
            $("#divAttendanceStatuz").hide();
            $("#divRemark").hide();
        } else {
            getForm();//添加修改
        } 
    });
    //#region 初始化下拉加载

    function ysComboboxInit() {
        $("#AttendanceStatuz").ysComboBox({ data: ys.getJson(@Html.Raw(typeof(AttendanceStatuzEnum).EnumToDictionaryString())), class: 'form-control' });

        var scoreArr = [];
        scoreArr.push({ id: 1, name :'一星'});
        scoreArr.push({ id: 2, name :'二星'});
        scoreArr.push({ id: 3, name: '三星' });
        scoreArr.push({ id: 4, name: '四星' });
        scoreArr.push({ id: 5, name: '五星' }); 
        $('#Score').ysComboBox({
            data: scoreArr,
            key: 'id',
            value: 'name',
            class: 'form-control'
        });
    }

    //#endregion

    function getForm() {
        if (id > 0) {
            ys.ajax({
                url: '@Url.Content("~/ExperimentTeachManage/ExperimentCriticize/GetFormJson")' + '?id=' + id,
                type: 'get',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        $('#form').setWebControls(obj.Data); 
                    }
                }
            });
        } 
    }
    
    function saveForm(index) {
        var postData = $('#form').getWebControls({ Id: id });
        //验证必填项。
        var errMsg = '';
        var posturl = '@Url.Content("~/ExperimentTeachManage/ExperimentCriticize/SaveFormJson")';
        if (Com_Opt_Type == 1 || Com_Opt_Type == 2) {
            if (!(postData.AttendanceStatuz != undefined && postData.AttendanceStatuz > 0)) {
                errMsg += '请选择学生考勤.</br>';
            }
            if (Com_Opt_Type == 2) {
                posturl = '@Url.Content("~/ExperimentTeachManage/ExperimentCriticize/SaveBatchStatuzFormJson")';
                postData.Ids = ids;
            }
        }
        if (Com_Opt_Type == 1 || Com_Opt_Type == 3) {
            if (!(postData.Score != undefined && postData.Score > 0)) {
                errMsg += '请选择等级评价.</br>';
            }
            if (Com_Opt_Type == 3) {
                posturl = '@Url.Content("~/ExperimentTeachManage/ExperimentCriticize/SaveBatchScoreFormJson")';
                postData.Ids = ids;
            }
        }
        if (postData.Remark != undefined && postData.Remark.length > 500) {
            errMsg += '你填写的综合评价字符请控制在500以内.< /br>';
        }
        if (errMsg != '') {
            layer.msg(errMsg, { icon: 2, time: 3000, btn: ['关闭'], yes: function () { layer.closeAll(); }, area: ['400px'] });
            return false;
        }

        ys.ajax({
            url: posturl,
            type: 'post',
            data: postData,
            success: function (obj) {
                if (obj.Tag == 1) {
                    ys.msgSuccess(obj.Message);
                    parent.searchGrid();
                    parent.layer.close(index);
                }
                else {
                    ys.msgError(obj.Message);
                }
            }
        });
    }
</script>

