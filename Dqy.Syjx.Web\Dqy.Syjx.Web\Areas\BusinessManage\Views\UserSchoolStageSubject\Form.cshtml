﻿@{
    Layout = "~/Views/Shared/_Index.cshtml";
}
<style type="text/css">
    .check-box {
        width: 100px;
    }
    .select2-container {
        display: block;
        width: auto !important;
    }
</style>
<div class="container-div">
    <div class="row">
        <div class="btn-group-sm hidden-xs" id="toolbar">
        </div>
        <div class="col-sm-12 select-table table-striped">
            <div class="ibox-title">
                <h5>管理员授权配置</h5>
            </div>
            <div class="card-body">
                <form id="form" class="form-horizontal m">
                    <input type="hidden" id="Id" col="Id" value="0" />
                    <input type="hidden" id="UserId" col="UserId" value="0" />
                    <input type="hidden" id="SubjectIdz" col="SubjectIdz" value="" />
                    <div class="form-group row">
                        <label class="col-sm-2 control-label ">姓名<font class="red"> *</font></label>
                        <div class="col-sm-8">
                            <input id="RealName" col="RealName" type="text" class="form-control" readonly />
                        </div>
                        <div class="col-sm-1" id="divBtnOptSelect">
                            <a class="btn btn-success" onclick="showChooseUserForm()"><i class="fa fa-plus"></i> 选择</a>
                        </div>
                    </div>
                    <div class="form-group row">
                        <label class="col-sm-2 control-label ">学段</label>
                        <div class="col-sm-8" id="SchoolStageIdz" col="SchoolStageIdz"></div>
                        <div class="col-sm-1">
                            <span class="glyphicon glyphicon-question-sign titleprompt inputprompt" aria-hidden="true" data-container="body" data-toggle="tooltip" data-content="是指该人任职的学段。"></span>
                        </div>
                    </div>
                    <div class="form-group row">
                        <label class="col-sm-2 control-label ">管理科目</label>
                        <div class="col-sm-8" id="Subjectz" col="Subjectz"></div>
                        <div class="col-sm-1">
                            <span class="glyphicon glyphicon-question-sign titleprompt inputprompt" aria-hidden="true" data-container="body" data-toggle="tooltip" data-content="是指该人可以管理的实验室或专用室的类别，以及对应仪器的计划填报、入库与运维。"></span>
                        </div>
                    </div>
                    <div class="form-group divNature"style="display:none;">
                        <label class="col-sm-2 control-label ">是否实验员</label>
                        <div class="col-sm-8" id="IsExperimenterbox" col="IsExperimenterbox">
                        </div>
                        <div class="col-sm-1" style="color:#999;">
                            <span class="glyphicon glyphicon-question-sign titleprompt inputprompt" aria-hidden="true" data-container="body" data-toggle="tooltip" data-content="是指中学理化生和小学科学的实验室管理员"></span>
                        </div>
                    </div>
                    <div class="form-group divNature" style="display:none;">
                        <label class="col-sm-2 control-label ">实验员性质</label>
                        <div class="col-sm-8">
                            <div id="extNature" col="extNature"></div>
                        </div>
                        <div class="col-sm-1">
                        </div>
                    </div>
                    <div class="form-group row">
                        <label class="col-sm-2 control-label ">备注</label>
                        <div class="col-sm-8">
                            <input id="Remark" col="Remark" type="text" class="form-control" />
                        </div>
                    </div>
                </form>
            </div>
            <div class="btn-group-sm hidden-xs" id="toolbar" style="text-align: center;">
                <a id="btnAdd" class="btn btn-info" onclick="saveForm();"><i class="fa fa-edit"></i> 保存</a>
            </div>
        </div>
    </div>
</div>
<input type="hidden"  id="hidIsExperimenter" value="" />
<script type="text/javascript">
    var id = ys.request("id");
    $(function () {
        $(".inputprompt").popover({
            trigger: 'hover',
            placement: 'left',
            html: true
        });
        $("#extNature").ysComboBox({ data: ys.getJson(@Html.Raw(typeof(ExperimenterNatureEnum).EnumToDictionaryString())), class: "form-control", defaultName: "请选择" });

        ys.ajax({
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetListJson")' + '?TypeCode=1002',
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1) {
                    var html = '';
                    for (var i = 0; i < obj.Data.length; i++) {
                        html += '<label class="check-box">';
                        html += $.Format('<div class="icheckbox-blue"><input name="SchoolStageIdz_checkbox" type="checkbox" value="{0}" style="position: absolute; opacity: 0;"></div>', obj.Data[i].DictionaryId);
                        html += $.Format('{0}</label>', obj.Data[i].DicName);
                    }
                    $("#SchoolStageIdz").html(html);

                    $('input[name="SchoolStageIdz_checkbox"]').change(function () {
                        if ($(this).prop("checked")) {
                            $(this).parent(".icheckbox-blue").addClass("checked");

                        } else {
                            $(this).parent(".icheckbox-blue").removeClass("checked");
                        }
                        var schoolstages = $('input[name="SchoolStageIdz_checkbox"]:checked').map(function () { return this.value }).get().join(',');
                        loadSubjectIdz(schoolstages);
                    });
                    if (obj.Data.length == 1) {
                        $('input[name="SchoolStageIdz_checkbox"]').prop("checked", true);
                        $('input[name="SchoolStageIdz_checkbox"]').parent(".icheckbox-blue").addClass("checked");
                        loadSubjectIdz(obj.Data[0].DictionaryId + '');
                    } else {
                        loadSubjectIdz("");
                    }
                }
                else {
                    ys.msgError(obj.Message);
                }
            }
        });
        getForm();

        $('#form').validate({
            rules: {
                UserId: { required: true },
                SchoolStageIdz: { required: true },
                SubjectIdz: { required: true }
            }
        });
    });
    function loadSubjectIdz(stageids) {
        $("#Subjectz").html('');
        if (stageids != undefined && stageids.length > 0) {
            ys.ajax({
                url: '@Url.Content("~/SystemManage/StaticDictionary/GetChildToListJson")' + '?TypeCode=1005&Pids=' + stageids,
                type: 'get',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        var html = '';
                        var subjectz = $("#SubjectIdz").val();
                        for (var i = 0; i < obj.Data.length; i++) {
                            html += '<label class="check-box">';
                            if (subjectz.indexOf(obj.Data[i].DictionaryId) > -1) {
                                html += $.Format('<div class="icheckbox-blue checked"><input name="Subjectz_checkbox" type="checkbox" value="{0}" style="position: absolute; opacity: 0;" checked="checked" nature = "{1}"></div>', obj.Data[i].DictionaryId, obj.Data[i].Nature);
                            } else {
                                html += $.Format('<div class="icheckbox-blue"><input name="Subjectz_checkbox" type="checkbox" value="{0}" style="position: absolute; opacity: 0;" nature= "{1}"></div>', obj.Data[i].DictionaryId, obj.Data[i].Nature);
                            }
                            html += $.Format('{0}</label>', obj.Data[i].DicName);
                        }
                        $("#Subjectz").html(html);
                        setSubjectIdz();
                        $('input[name="Subjectz_checkbox"]').change(function () {
                            if ($(this).prop("checked")) {
                                $(this).parent(".icheckbox-blue").addClass("checked");

                            } else {
                                $(this).parent(".icheckbox-blue").removeClass("checked");
                            }
                            setSubjectIdz();
                        });
                        loadExperimenterSelect();
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        } else {
            $("#SubjectIdz").val('');
        }
    }
    function loadExperimenterSelect() {
        if ($("#hidIsExperimenter").val() == "@IsEnum.Yes.ParseToInt()") {
            $("#IsExperimenterbox").html('<label class="check-box"> <div class="icheckbox-blue checked"><input name="IsExperimenterbox_checkbox" type="checkbox" value="@IsEnum.Yes.ParseToInt()" style="position: absolute; opacity: 0;" checked="checked"></div>是</label>');
        } else {
            $("#IsExperimenterbox").html('<label class="check-box"> <div class="icheckbox-blue"><input name="IsExperimenterbox_checkbox" type="checkbox" value="@IsEnum.Yes.ParseToInt()" style="position: absolute; opacity: 0;"></div>是</label>');
        }
        $('input[name="IsExperimenterbox_checkbox"]').change(function () {
            if ($(this).prop("checked")) {
                $(this).parent(".icheckbox-blue").addClass("checked");
            } else {
                $(this).parent(".icheckbox-blue").removeClass("checked"); 
                $("#extNature").ysComboBox("setValue", -1);
            }
        });
    }
    function setSubjectIdz() {
        var isShow = false;
        var schoolstages = $('input[name="Subjectz_checkbox"]:checked').map(function () {
            if ($(this).attr("nature") == 1) {
                isShow = true;
            }
            return this.value;
        }).get().join(',');
        if (isShow) {
            $(".divNature").show();
        } else {
            $(".divNature").hide();
            $('input[name="IsExperimenterbox_checkbox"]').prop("checked", false);
            $('input[name="IsExperimenterbox_checkbox"]').parent(".icheckbox-blue").removeClass("checked");
        }
        $("#SubjectIdz").val(schoolstages);
    }
    function showChooseUserForm() {
         ys.openDialog({
            title: "选择人员",
             content: '@Url.Content("~/BusinessManage/UserSchoolStageSubject/ChooseUserForm")' + '?id=' + id,
            /* height: "200px",*/
            callback: function (index, layero) {
                var iframeWin = window[layero.find('iframe')[0]['name']];
                iframeWin.saveForm(index);
            }
        });
    }

    function getForm() {
        if (id > 0) {
            $("#divBtnOptSelect").hide();
            ys.ajax({
                url: '@Url.Content("~/BusinessManage/UserSchoolStageSubject/GetFormJson")' + '?id=' + id,
                type: 'get',
                success: function (obj) {
                    if (obj.Tag == 1) {
                         $('#form').setWebControls(obj.Data);
                        loadSubjectIdz(obj.Data.SchoolStageIdz);
                        if (obj.Data.IsExperimenter ==@IsEnum.Yes.ParseToInt()) {
                            $("#hidIsExperimenter").val(obj.Data.IsExperimenter);
                            $("#extNature").ysComboBox('setValue', obj.Data.ExperimenterNature);
                        }
                    }
                }
            });
        }
        else {
            var defaultData = { Id: 0, UserId: 0 };
            $('#form').setWebControls(defaultData);
        }
    }

    function setSelectName(name,userid) {
        $("#RealName").val(name);
        $("#UserId").val(userid);
    }
    function saveForm(index) {
        if ($('#form').validate().form()) {
            var postData = $('#form').getWebControls({ Id: id });
            var errorMsg = '';
            if (!(parseInt(postData.UserId) > 0)) {
                errorMsg += '请选择姓名。<br/>';
            }
            if (!(postData.SchoolStageIdz != undefined && postData.SchoolStageIdz.length > 0)) {
                errorMsg += '请选择学段。<br/>';
            }
            if (!(postData.SubjectIdz != undefined && postData.SubjectIdz.length > 0)) {
                errorMsg += '请选择管理科目。<br/>';
            }
            if (postData.IsExperimenterbox == @IsEnum.Yes.ParseToInt()) {
                postData.IsExperimenter = @IsEnum.Yes.ParseToInt();
                if (postData.extNature !=@ExperimenterNatureEnum.FullTime.ParseToInt() && postData.extNature !=@ExperimenterNatureEnum.PartTime.ParseToInt() ) {
                    errorMsg += '请选择实验员性质。<br/>';
                }
                postData.ExperimenterNature = postData.extNature;
            } else {
                postData.IsExperimenter = @IsEnum.No.ParseToInt();
                postData.ExperimenterNature = 0;
            }
            if (errorMsg != '') {
                ys.msgError('验证失败，请填写完成再提交！<br/>' + errorMsg);
                return;
            }
            ys.ajax({
                url: '@Url.Content("~/BusinessManage/UserSchoolStageSubject/SaveFormJson")',
                type: 'post',
                data: postData,
                success: function (obj) {
                    if (obj.Tag == 1) {
                         ys.msgSuccess(obj.Message);
                        var url = '@Url.Content("~/BusinessManage/UserSchoolStageSubject/Index")';
                        createMenuAndCloseCurrent(url, "管理员授权");
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        }
    }
</script>

