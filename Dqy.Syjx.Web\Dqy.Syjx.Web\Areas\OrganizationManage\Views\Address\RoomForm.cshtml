﻿@{
    Layout = "~/Views/Shared/_FormWhite.cshtml";
}

<div class="wrapper animated fadeInRight">
    <form id="form" class="form-horizontal m">
        <input type="hidden" id="Id" col="Id" value="0" />
        <input type="hidden" id="Depth" col="Depth" value="1" />
        <input type="hidden" id="UnitId" col="UnitId" value="0" />
        <div class="form-group row">
            <label class="col-sm-3 control-label ">楼宇（场地）<font class="red"> *</font></label>
            <div class="col-sm-7">
                <div col="Pid" id="Pid"></div>
            </div>
            <div class="col-sm-1">
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">场所（室）<font class="red"> *</font></label>
            <div class="col-sm-7">
                <input id="Name" col="Name" type="text" class="form-control" />
            </div>
            <div class="col-sm-1" style="padding-top:8px;">
                <span id="spanName" class="glyphicon glyphicon-question-sign titleprompt inputprompt" aria-hidden="true" data-container="body" data-toggle="tooltip" data-content="物理地址编号（如：101、302）或篮球场"></span>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">排序号<font class="red"> *</font></label>
            <div class="col-sm-7">
                <input id="Sort" col="Sort" type="text" class="form-control" />
            </div>
            <div class="col-sm-1">
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">备场所（室）别名<font class="red"> </font></label>
            <div class="col-sm-7">
                <input id="AliasName" col="AliasName" type="text" class="form-control" />
            </div>
            <div class="col-sm-1" style="padding-top:8px;">
                <span class="glyphicon glyphicon-question-sign titleprompt inputprompt" aria-hidden="true" data-container="body" data-toggle="tooltip" data-placement="right" data-content="如：二（3）班、物理实验室"></span>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">所属部门<font class="red"> </font></label>
            <div class="col-sm-7">
                <div col="DepartmentIds" id="DepartmentIds"></div>
            </div>
            <div class="col-sm-1">
            </div>
        </div>
    </form>
</div>

<script type="text/javascript">
    var id = ys.request("id");
    $(function () {
        $(".inputprompt").popover({
            trigger: 'hover',
            placement: 'left',
            html: true
        });
        $('#Pid').ysComboBox({
            url: '@Url.Content("~/OrganizationManage/Address/GetListJson")' + "?Pid=0",
            class: "form-control",
            key: 'Id',
            value: 'Name'
        });
        $('#DepartmentIds').ysComboBoxTree({
            url: '@Url.Content("~/OrganizationManage/Department/GetUnitTreeListJson")',
            class: "form-control",
            key: 'id',
            value: 'name'
        });
        getForm();
        $('#form').validate({
            rules: {
                Name: { required: true },
                Sort: { required: true },
                Pid: { required: true }
            }
        });
    });

    function getForm() {
        if (id > 0) {
            ys.ajax({
                url: '@Url.Content("~/OrganizationManage/Address/GetFormJson")' + '?id=' + id,
                type: 'get',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        $('#form').setWebControls(obj.Data);
                    }
                }
            });
        }
        else {
            var defaultData = { Id: 0, Depth: 1,Sort: 9999};
            $('#form').setWebControls(defaultData);
        }
    }

    function saveForm(index) {
        if ($('#form').validate().form()) {
            var postData = $('#form').getWebControls({ Id: id });
            postData.DepartmentIds = ys.getLastValue(postData.DepartmentIds);
            var errorMsg = '';
            if (!(parseInt(postData.Pid) > 0)) {
                errorMsg += '请选择楼宇（场地）。<br/>';
            } 
            if (errorMsg != '') {
                ys.msgError('验证失败，请填写完成再提交！<br/>' + errorMsg);
                return;
            }
            ys.ajax({
                url: '@Url.Content("~/OrganizationManage/Address/SaveFormJson")',
                type: 'post',
                data: postData,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        parent.layer.close(index);
                        parent.searchTreeGrid();
                        
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        }
    }
</script>

