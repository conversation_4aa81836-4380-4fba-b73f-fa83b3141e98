﻿@{ Layout = "~/Views/Shared/_FormWhite.cshtml"; }

<div class="wrapper animated fadeInRight">
    <form id="form" class="form-horizontal m">
        <div class="form-group row">
            <label class="col-sm-2 control-label ">适用学科<font class="red"> *</font></label>
            <div class="col-sm-6">
                <div id="courseId" col="CourseId"></div>
            </div>
            <span class="col-sm-4 control-label" style="text-align:left;color:#999;">
                （请正确设置，否则影响学科仪器达标数据）
            </span>
        </div>
        <div class="form-group row">
            <label class="col-sm-2 control-label ">适用学段<font class="red"> *</font></label>
            <div class="col-sm-6">
                <div id="stageId" col="StageId"></div>
            </div>
        </div>
    </form>
</div>

<script type="text/javascript">
    //var ids = ys.request("ids");
    var ids = parent.SELECTED_IDS;
    var IsVerify = ys.request("isVerify");
    $(function () {
        loadCourse();
        //loadSchoolStage();
        if (IsVerify == null){
            IsVerify = 0;
        }

        $("#form").validate({
            rules: {
                stageId: { required: true },
                courseId: { required: true }
            }
        });
    });

    function saveForm(index) {
        if ($("#form").validate().form()) {
            var postData = $("#form").getWebControls({ Ids: ids, IsVerify: IsVerify });
            ys.ajax({
                url: '@Url.Content("~/InstrumentManage/SchoolInstrument/BatchEditCourseJson")',
                type: "post",
                data: postData,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        parent.$('#gridTable').ysTable('refresh');
                        parent.resetToolbarStatus();
                        parent.layer.close(index);
                        if (IsVerify==1){
                            parent.showSection();
                        }
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        }
    }

    function loadSchoolStage() {
        $('#stageId').html('');
        ys.ajax({
            url: '@Url.Content("~/BusinessManage/UserSchoolStageSubject/GetSchoolStageByUser")' + '?courseId=' + ($('#courseId').ysComboBox('getValue') || 0),
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1) {
                    $('#stageId').ysCheckBox({
                        data: obj.Data,
                        key: 'DictionaryId',
                        value: 'DicName',
                        class: "form-control"
                    });
                    if (obj.Data.length == 0) {
                        ComBox.LoadPageMessage('您管理的学科', '/BusinessManage/UserSchoolStageSubject/StageSubjectInput', '实验员授权', @RoleEnum.BusinessManager.ParseToInt());
                    } else if (obj.Data.length == 1){
                        $('#stageId').ysCheckBox('setValue', obj.Data[0].DictionaryId);
                    }
                }
            }
        });
    }

    function loadCourse(schoolStage) {

        ys.ajax({
            url: '@Url.Content("~/BusinessManage/UserSchoolStageSubject/GetSubjectByUser")',
            type: 'get',
            success: function (obj) {
                if (obj.Data.length == 0) {
                    ComBox.LoadPageMessage('您管理的学科', '/BusinessManage/UserSchoolStageSubject/StageSubjectInput', '实验员授权', @RoleEnum.BusinessManager.ParseToInt());
                }else{
                    $('#courseId').ysComboBox({
                        data: obj.Data,
                        key: 'DictionaryId',
                        value: 'DicName',
                        dataName: 'Data',
                        class: "form-control",
                        onChange: function () {
                            loadSchoolStage();
                        }
                    });

 
                    if (obj.Data.length > 1) {

                        $('#courseId').ysComboBox('setValue', obj.Data[0].DictionaryId);
                    }
                }
            }
        });
    }
</script>
