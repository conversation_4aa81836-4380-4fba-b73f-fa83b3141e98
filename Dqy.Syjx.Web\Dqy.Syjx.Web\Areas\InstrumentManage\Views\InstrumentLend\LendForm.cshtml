﻿@{
    Layout = "~/Views/Shared/_FormWhite.cshtml";
}

<div class="wrapper animated fadeInRight">
    <form id="form" class="form-horizontal m">
        <div class="form-group divWrite">
            <label class="col-sm-3 control-label ">借用人<font class="red"> *</font></label>
            <div class="col-sm-8">
                <div id="lendUserId" col="LendUserId"></div>
            </div>
            <div class="col-sm-1">
                <span class="glyphicon glyphicon-question-sign titleprompt inputprompt" aria-hidden="true" data-container="body" data-toggle="tooltip" data-content="如果借用人没有“借用确认”功能，请为该借用人添加角色“学科教师”。"></span>
            </div>
        </div>
        <div class="form-group divLook" style="display:none;">
            <label class="col-sm-3 control-label ">借用人<font class="red"> *</font></label>
            <div class="col-sm-8">
                <input id="lendUserName" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">借用数量<font class="red"> *</font></label>
            <div class="col-sm-8">
                <input id="lendNum" col="LendNum" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label ">借用周期（天）<font class="red"> *</font></label>
            <div class="col-sm-8">
                <input id="lendDay" col="LendDay" type="text" class="form-control" />
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-3 control-label " id="labRemark">用途</label>
            <div class="col-sm-8">
                <input id="Remark" col="Remark" type="text" class="form-control" />
            </div>
        </div>
    </form>
</div>

<script type="text/javascript">
    var schoolInstrumentId = ys.request("schoolInstrumentId");
    var id = ys.request("id");
    var isLook = ys.request("isLook");
    $(function () {

        $(".inputprompt").popover({
            trigger: 'hover',
            placement: 'left',
            html: true
        });

        if (isLook == 1) {
            parent.hideDialogButton();
            getForm();
        }
        else {
            loadLendUser();
        }

        loadFormVali();
    });

    function getForm() {
        if (id > 0) {
            ys.ajax({
                url: '@Url.Content("~/InstrumentManage/InstrumentLend/GetFormJson")' + '?id=' + id,
                type: "get",
                success: function (obj) {
                    if (obj.Tag == 1) {
                        var result = obj.Data;
                        loadLendUser(result.LendUserId);
                        $("#form").setWebControls(result);
                        $('#lendUserName').val(result.LendUserName);
                        $('.divWrite').hide();
                        $('.divLook').show();
                    }
                }
            });
        }
        $('input').attr('readonly', true);
    }

    function saveForm(index) {
        if (!$('#lendUserId').ysComboBox('getValue')) {
            ys.msgError('请选择借用人');
            return false;
        }
        if ($('#form').validate().form()) {
            var postData = $('#form').getWebControls({ SchoolInstrumentIds: schoolInstrumentId });
            ys.ajax({
                url: '@Url.Content("~/InstrumentManage/InstrumentLend/SaveFormJson")',
                type: 'post',
                data: postData,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        parent.$('#gridTable').ysTable('refresh');parent.resetToolbarStatus();
                        parent.layer.close(index);
                    }
                    else {
                        ys.alertError(obj.Message);
                    }
                }
            });
        }
    }

    function loadLendUser(defaultValue) {
        $('#lendUserId').ysComboBox({
            url:'@Url.Content("~/InstrumentManage/InstrumentLend/GetUserListJson")',
            key: 'Id',
            value: 'RealName',
            class: "form-control"
        });
        if (defaultValue > 0) {
            $('#lendUserId').ysComboBox('setValue', defaultValue);
        }
    }

     //#region
    //加载配置验证信息
    function loadFormVali() {
        ys.ajax({
            url: '@Url.Content("~/SystemManage/ConfigSet/GetListByCode")',
            type: 'Post',
            data: { typecode: '1SYYQ-3YQGL-2YQJC' },
            success: function (obj) {
                if (obj.Tag == 1) {
                    var validateRule = {};
                    var validateMessage = {};
                    validateRule.lendUserId = { required: true }; 
                    validateRule.lendNum = { required: true, number: true, thanMinValue: 0}; 
                    validateRule.lendDay = { number: true, number: true, thanMinValue: 0};

                    if (obj.Data != undefined && obj.Data.length > 0) {
                        for (var i = 0; i < obj.Data.length; i++) {
                            if (obj.Data[i].ConfigValue == "1") {
                                var typecode = obj.Data[i].TypeCode.replaceAll("1SYYQ-3YQGL-2YQJC-", "");
                                var verifyhint = "";
                                if (obj.Data[i].VerifyHint != undefined && obj.Data[i].VerifyHint.length > 0) {
                                    verifyhint = obj.Data[i].VerifyHint;
                                }
                                $("#lab" + typecode).append('<font class="red"> *</font>');
                                if (typecode == "Remark") {
                                    validateRule.Remark = { required: true };
                                    validateMessage.Remark = { required: verifyhint };
                                }
                            }
                        }
                    }
                    $('#form').validate({
                        rules: validateRule,
                        messages: validateMessage
                    });
                }
            }
        });
    }

    //#endregion
</script>

